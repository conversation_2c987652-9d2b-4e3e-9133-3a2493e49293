"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[777],{

/***/ 7777:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2389);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4976);
/* harmony import */ var _components_layout_PublicLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(4370);






const Help = () => {
    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__/* .useTranslation */ .Bd)();
    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');
    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');
    const categories = [
        {
            id: 'all',
            name: t('help.categories.all', 'Toate'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .BookOpenIcon */ .RuZ,
        },
        {
            id: 'getting-started',
            name: t('help.categories.getting_started', 'Primii Pași'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .AcademicCapIcon */ .tlP,
        },
        {
            id: 'features',
            name: t('help.categories.features', 'Funcționalități'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .LightBulbIcon */ .XLj,
        },
        {
            id: 'settings',
            name: t('help.categories.settings', 'Setări'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CogIcon */ .DP5,
        },
        {
            id: 'security',
            name: t('help.categories.security', 'Securitate'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ShieldCheckIcon */ .Zus,
        },
        {
            id: 'billing',
            name: t('help.categories.billing', 'Facturare'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CreditCardIcon */ .BFk,
        },
        {
            id: 'mobile',
            name: t('help.categories.mobile', 'Aplicația Mobilă'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DevicePhoneMobileIcon */ .qXP,
        },
    ];
    const helpSections = [
        {
            title: t('help.sections.quick_start.title', 'Start Rapid'),
            description: t('help.sections.quick_start.description', 'Ghiduri pentru a începe rapid cu FinanceFlow'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .AcademicCapIcon */ .tlP,
            color: 'bg-blue-500',
            items: [
                {
                    title: t('help.sections.quick_start.items.setup', 'Configurarea Contului'),
                    description: t('help.sections.quick_start.items.setup_desc', 'Cum să vă configurați contul în 5 minute'),
                    type: 'guide',
                    duration: '5 min',
                },
                {
                    title: t('help.sections.quick_start.items.first_transaction', 'Prima Tranzacție'),
                    description: t('help.sections.quick_start.items.first_transaction_desc', 'Adăugați prima dvs. tranzacție'),
                    type: 'video',
                    duration: '3 min',
                },
                {
                    title: t('help.sections.quick_start.items.categories', 'Organizarea Categoriilor'),
                    description: t('help.sections.quick_start.items.categories_desc', 'Creați și gestionați categoriile'),
                    type: 'guide',
                    duration: '7 min',
                },
            ],
        },
        {
            title: t('help.sections.features.title', 'Funcționalități'),
            description: t('help.sections.features.description', 'Explorați toate funcționalitățile disponibile'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .LightBulbIcon */ .XLj,
            color: 'bg-green-500',
            items: [
                {
                    title: t('help.sections.features.items.budgets', 'Gestionarea Bugetelor'),
                    description: t('help.sections.features.items.budgets_desc', 'Creați și monitorizați bugete'),
                    type: 'guide',
                    duration: '10 min',
                },
                {
                    title: t('help.sections.features.items.reports', 'Rapoarte și Analize'),
                    description: t('help.sections.features.items.reports_desc', 'Generați rapoarte detaliate'),
                    type: 'video',
                    duration: '8 min',
                },
                {
                    title: t('help.sections.features.items.goals', 'Obiective Financiare'),
                    description: t('help.sections.features.items.goals_desc', 'Setați și urmăriți obiective'),
                    type: 'guide',
                    duration: '6 min',
                },
                {
                    title: t('help.sections.features.items.notifications', 'Notificări Inteligente'),
                    description: t('help.sections.features.items.notifications_desc', 'Configurați alertele personalizate'),
                    type: 'guide',
                    duration: '4 min',
                },
            ],
        },
        {
            title: t('help.sections.troubleshooting.title', 'Rezolvarea Problemelor'),
            description: t('help.sections.troubleshooting.description', 'Soluții pentru problemele comune'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .QuestionMarkCircleIcon */ .R2D,
            color: 'bg-yellow-500',
            items: [
                {
                    title: t('help.sections.troubleshooting.items.sync', 'Probleme de Sincronizare'),
                    description: t('help.sections.troubleshooting.items.sync_desc', 'Rezolvați problemele de sincronizare'),
                    type: 'guide',
                    duration: '5 min',
                },
                {
                    title: t('help.sections.troubleshooting.items.login', 'Probleme de Autentificare'),
                    description: t('help.sections.troubleshooting.items.login_desc', 'Nu vă puteți conecta la cont?'),
                    type: 'guide',
                    duration: '3 min',
                },
                {
                    title: t('help.sections.troubleshooting.items.performance', 'Performanță Aplicație'),
                    description: t('help.sections.troubleshooting.items.performance_desc', 'Aplicația funcționează lent?'),
                    type: 'guide',
                    duration: '4 min',
                },
            ],
        },
        {
            title: t('help.sections.advanced.title', 'Funcții Avansate'),
            description: t('help.sections.advanced.description', 'Pentru utilizatorii experimentați'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CogIcon */ .DP5,
            color: 'bg-purple-500',
            items: [
                {
                    title: t('help.sections.advanced.items.api', 'Integrare API'),
                    description: t('help.sections.advanced.items.api_desc', 'Conectați aplicații externe'),
                    type: 'documentation',
                    duration: '15 min',
                },
                {
                    title: t('help.sections.advanced.items.automation', 'Automatizări'),
                    description: t('help.sections.advanced.items.automation_desc', 'Configurați reguli automate'),
                    type: 'guide',
                    duration: '12 min',
                },
                {
                    title: t('help.sections.advanced.items.export', 'Export Date'),
                    description: t('help.sections.advanced.items.export_desc', 'Exportați datele în diverse formate'),
                    type: 'guide',
                    duration: '6 min',
                },
            ],
        },
    ];
    const popularArticles = [
        {
            title: t('help.popular.article1.title', 'Cum să configurez primul meu buget?'),
            views: '2.5k',
            category: 'getting-started',
        },
        {
            title: t('help.popular.article2.title', 'Conectarea conturilor bancare'),
            views: '1.8k',
            category: 'features',
        },
        {
            title: t('help.popular.article3.title', 'Securitatea datelor financiare'),
            views: '1.2k',
            category: 'security',
        },
        {
            title: t('help.popular.article4.title', 'Utilizarea aplicației mobile'),
            views: '980',
            category: 'mobile',
        },
        {
            title: t('help.popular.article5.title', 'Gestionarea abonamentelor'),
            views: '756',
            category: 'billing',
        },
    ];
    const getTypeIcon = (type) => {
        switch (type) {
            case 'video':
                return _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .VideoCameraIcon */ .UaG;
            case 'documentation':
                return _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DocumentTextIcon */ .AQX;
            default:
                return _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .BookOpenIcon */ .RuZ;
        }
    };
    const getTypeColor = (type) => {
        switch (type) {
            case 'video':
                return 'text-red-600 bg-red-50';
            case 'documentation':
                return 'text-blue-600 bg-blue-50';
            default:
                return 'text-green-600 bg-green-50';
        }
    };
    const filteredSections = helpSections.filter(section => {
        if (selectedCategory === 'all')
            return true;
        return section.items.some(item => item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.description.toLowerCase().includes(searchQuery.toLowerCase()));
    });
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_layout_PublicLayout__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "min-h-screen bg-gray-50", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-white shadow-sm", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/", className: "flex items-center text-gray-600 hover:text-gray-900 transition-colors", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowLeftIcon */ .A60, { className: "w-5 h-5 mr-2" }), t('common.back', 'Înapoi')] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "h-6 w-px bg-gray-300" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1", { className: "text-3xl font-bold text-gray-900", children: t('support.help.title', 'Centrul de Ajutor') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "mt-4 text-lg text-gray-600 max-w-3xl", children: t('support.help.subtitle', 'Găsiți răspunsuri la întrebările dvs. și învățați să folosiți FinanceFlow la maximum.') })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mb-12", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative max-w-2xl mx-auto mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .MagnifyingGlassIcon */ .$p$, { className: "h-5 w-5 text-gray-400" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "text", value: searchQuery, onChange: (e) => setSearchQuery(e.target.value), className: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg", placeholder: t('help.search.placeholder', 'Căutați în centrul de ajutor...') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex flex-wrap justify-center gap-3", children: categories.map((category) => {
                                        const IconComponent = category.icon;
                                        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { onClick: () => setSelectedCategory(category.id), className: `flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${selectedCategory === category.id
                                                ? 'bg-blue-600 text-white'
                                                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'}`, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconComponent, { className: "w-4 h-4 mr-2" }), category.name] }, category.id));
                                    }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 lg:grid-cols-4 gap-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "lg:col-span-3", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-8", children: filteredSections.map((section, sectionIndex) => {
                                            const SectionIcon = section.icon;
                                            return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "p-6 border-b border-gray-200", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: `${section.color} p-3 rounded-lg mr-4`, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SectionIcon, { className: "w-6 h-6 text-white" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900", children: section.title }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600 mt-1", children: section.description })] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: section.items.map((item, itemIndex) => {
                                                                const TypeIcon = getTypeIcon(item.type);
                                                                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "group cursor-pointer", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start p-4 rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: `p-2 rounded-lg mr-4 ${getTypeColor(item.type)}`, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TypeIcon, { className: "w-5 h-5" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-semibold text-gray-900 group-hover:text-blue-600 transition-colors", children: item.title }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronRightIcon */ .vKP, { className: "w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-colors" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600 text-sm mt-1", children: item.description }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center mt-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded", children: item.duration }), item.type === 'video' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .PlayIcon */ .udU, { className: "w-3 h-3 text-red-500 ml-2" }))] })] })] }) }, itemIndex));
                                                            }) }) })] }, sectionIndex));
                                        }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "lg:col-span-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: t('help.popular.title', 'Articole Populare') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-3", children: popularArticles.map((article, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "group cursor-pointer", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2", children: article.title }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-xs text-gray-500 mt-1", children: [article.views, " ", t('help.popular.views', 'vizualizări')] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronRightIcon */ .vKP, { className: "w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-colors ml-2" })] }) }, index))) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: t('help.quick_actions.title', 'Acțiuni Rapide') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/support/contact", className: "flex items-center p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors group", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChatBubbleLeftRightIcon */ .vQ9, { className: "w-5 h-5 text-gray-400 group-hover:text-blue-600 mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "font-medium text-gray-900 group-hover:text-blue-600", children: t('help.quick_actions.contact', 'Contactați Suportul') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500", children: t('help.quick_actions.contact_desc', 'Obțineți ajutor personalizat') })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/support/documentation", className: "flex items-center p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors group", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DocumentTextIcon */ .AQX, { className: "w-5 h-5 text-gray-400 group-hover:text-blue-600 mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "font-medium text-gray-900 group-hover:text-blue-600", children: t('help.quick_actions.docs', 'Documentația API') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500", children: t('help.quick_actions.docs_desc', 'Pentru dezvoltatori') })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { className: "flex items-center p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors group w-full text-left", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .VideoCameraIcon */ .UaG, { className: "w-5 h-5 text-gray-400 group-hover:text-blue-600 mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "font-medium text-gray-900 group-hover:text-blue-600", children: t('help.quick_actions.video_tour', 'Tur Video') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500", children: t('help.quick_actions.video_tour_desc', 'Prezentare generală') })] })] })] })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mt-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-2xl font-bold text-white mb-4", children: t('help.cta.title', 'Nu ați găsit ceea ce căutați?') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-blue-100 mb-6 max-w-2xl mx-auto", children: t('help.cta.description', 'Echipa noastră de suport este gata să vă ajute cu orice întrebare sau problemă.') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col sm:flex-row gap-4 justify-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/support/contact", className: "bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors", children: t('help.cta.contact', 'Contactați Suportul') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { className: "bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-800 transition-colors", children: t('help.cta.chat', 'Chat Live') })] })] })] })] }) }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Help);


/***/ })

}]);