import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  DocumentArrowDownIcon,
} from '@heroicons/react/24/outline';
import { useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';

import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import Input from '../components/ui/Input';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { useExportExpenses } from '../hooks/useExpenses';
import { formatCurrency, formatDate } from '../utils/helpers';

// Tipuri pentru pagina Expenses
interface Expense {
  id: string;
  description: string;
  amount: number;
  category: string;
  date: string;
  paymentMethod: string;
}

interface FilterState {
  search: string;
  category: string;
  dateFrom: string;
  dateTo: string;
}

// Mock data pentru demonstrație
const mockExpenses: Expense[] = [
  {
    id: 'exp_1',
    description: 'Cump<PERSON><PERSON><PERSON><PERSON><PERSON> Carrefour',
    amount: 85.5,
    category: 'Mâncare',
    date: '2024-01-15',
    paymentMethod: 'Card',
  },
  {
    id: 'exp_2',
    description: 'Benzină',
    amount: 120.0,
    category: 'Transport',
    date: '2024-01-14',
    paymentMethod: 'Card',
  },
  {
    id: 'exp_3',
    description: 'Factură electricitate',
    amount: 75.25,
    category: 'Utilități',
    date: '2024-01-13',
    paymentMethod: 'Transfer bancar',
  },
  {
    id: 'exp_4',
    description: 'Cinema',
    amount: 25.0,
    category: 'Divertisment',
    date: '2024-01-12',
    paymentMethod: 'Numerar',
  },
  {
    id: 'exp_5',
    description: 'Prânz restaurant',
    amount: 45.75,
    category: 'Mâncare',
    date: '2024-01-11',
    paymentMethod: 'Card',
  },
];

const Expenses: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showFilters, setShowFilters] = useState<boolean>(false);

  // Hook pentru export
  const exportExpensesMutation = useExportExpenses();

  // Simulare query pentru cheltuieli
  const {
    data: expenses,
    isLoading,
    error,
  } = useQuery<Expense[]>({
    queryKey: ['expenses', searchTerm, selectedCategory],
    queryFn: async (): Promise<Expense[]> => {
      // Simulare API call
      await new Promise(resolve => setTimeout(resolve, 500));

      let filteredExpenses = mockExpenses;

      if (searchTerm) {
        filteredExpenses = filteredExpenses.filter(expense =>
          expense.description.toLowerCase().includes(searchTerm.toLowerCase()),
        );
      }

      if (selectedCategory) {
        filteredExpenses = filteredExpenses.filter(
          expense => expense.category === selectedCategory,
        );
      }

      return filteredExpenses;
    },
  });

  const categories = ['Mâncare', 'Transport', 'Utilități', 'Divertisment', 'Sănătate', 'Educație'];

  const handleAddExpense = (): void => {
    // Aici ar trebui să deschizi un modal sau să navighezi către o pagină de adăugare
    console.log('Adaugă cheltuială nouă');
  };

  const handleExport = async (format: 'csv' | 'pdf' | 'excel'): Promise<void> => {
    try {
      // Pregătește parametrii pentru export bazați pe filtrele curente
      const exportParams: { categoryId?: string } = {};
      if (selectedCategory !== '') {
        exportParams.categoryId = selectedCategory;
      }

      // Apelează funcția de export
      await exportExpensesMutation.mutateAsync({ format, params: exportParams });
    } catch (exportError: unknown) {
      // Eroarea este gestionată în hook
      console.error('Export failed:', exportError);
    }
  };

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Eroare la încărcarea cheltuielilor</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Cheltuieli</h1>
          <p className="text-gray-600">Gestionează și urmărește toate cheltuielile tale</p>
        </div>
        <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
          {/* Butoane de export */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('csv')}
              disabled={exportExpensesMutation.isPending}
              className="flex items-center"
            >
              <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
              CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('pdf')}
              disabled={exportExpensesMutation.isPending}
              className="flex items-center"
            >
              <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
              PDF
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('excel')}
              disabled={exportExpensesMutation.isPending}
              className="flex items-center"
            >
              <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
              Excel
            </Button>
          </div>
          <Button onClick={handleAddExpense} variant="primary" className="flex items-center">
            <PlusIcon className="h-5 w-5 mr-2" />
            Adaugă cheltuială
          </Button>
        </div>
      </div>

      {/* Filtre și căutare */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Caută cheltuieli..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          <div className="flex gap-2">
            <select
              value={selectedCategory}
              onChange={e => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Toate categoriile</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center"
            >
              <FunnelIcon className="h-5 w-5 mr-2" />
              Filtre
            </Button>
          </div>
        </div>

        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Data de la</label>
                <input
                  type="date"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Data până la</label>
                <input
                  type="date"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Suma minimă</label>
                <input
                  type="number"
                  placeholder="0.00"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Lista cheltuielilor */}
      <Card>
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : expenses && expenses.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Descriere
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Categorie
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Suma
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Metoda de plată
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Acțiuni
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {expenses.map(expense => (
                  <tr key={expense.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{expense.description}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {expense.category}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatCurrency(expense.amount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(expense.date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {expense.paymentMethod}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button className="text-primary-600 hover:text-primary-900">
                          Editează
                        </button>
                        <button className="text-red-600 hover:text-red-900">Șterge</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">Nu au fost găsite cheltuieli</p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default Expenses;
