"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[792],{

/***/ 250:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  nc: () => (/* binding */ useAuthStore)
});

// UNUSED EXPORTS: useAuth, useAuthActions, usePermissions

// EXTERNAL MODULE: ./node_modules/react-hot-toast/dist/index.mjs + 1 modules
var dist = __webpack_require__(888);
// EXTERNAL MODULE: ./node_modules/zustand/esm/index.mjs + 1 modules
var esm = __webpack_require__(1621);
// EXTERNAL MODULE: ./node_modules/zustand/esm/middleware.mjs
var middleware = __webpack_require__(7134);
;// ./src/config/auth.ts
// Configurări pentru sistemul de autentificare
const AUTH_CONFIG = {
    // Timpul de expirare pentru verificarea activității utilizatorului (în milisecunde)
    ACTIVITY_CHECK_INTERVAL: 60000, // 1 minut
    // Timpul de inactivitate după care utilizatorul este deconectat (în milisecunde)
    INACTIVITY_TIMEOUT: 30 * 60 * 1000, // 30 de minute
    // Marja de timp pentru verificarea expirării token-ului (în secunde)
    TOKEN_EXPIRY_MARGIN: 30,
    // Numărul maxim de încercări de refresh token
    MAX_REFRESH_ATTEMPTS: 3,
    // Timpul de așteptare pentru cererile de autentificare (în milisecunde)
    REQUEST_TIMEOUT: 10000, // 10 secunde
    // Chei pentru localStorage
    STORAGE_KEYS: {
        ACCESS_TOKEN: 'accessToken',
        REFRESH_TOKEN: 'refreshToken',
        USER: 'user',
        AUTH_STORAGE: 'auth-storage',
    },
    // Mesaje de eroare
    ERROR_MESSAGES: {
        NO_REFRESH_TOKEN: 'Nu există refresh token',
        TOKEN_REFRESH_FAILED: 'Eroare la reîmprospătarea token-ului',
        INVALID_CREDENTIALS: 'Credențiale invalide',
        NETWORK_ERROR: 'Eroare de rețea',
        SESSION_EXPIRED: 'Sesiunea a expirat',
        UNAUTHORIZED: 'Acces neautorizat',
    },
    // Rute care nu necesită autentificare
    PUBLIC_ROUTES: [
        '/login',
        '/register',
        '/forgot-password',
        '/reset-password',
        '/verify-email',
    ],
    // Rute care necesită autentificare
    PROTECTED_ROUTES: [
        '/dashboard',
        '/profile',
        '/expenses',
        '/categories',
        '/reports',
        '/settings',
    ],
};
/* harmony default export */ const auth = (AUTH_CONFIG);

// EXTERNAL MODULE: ./node_modules/axios/index.js + 49 modules
var axios = __webpack_require__(4447);
// EXTERNAL MODULE: ./src/utils/constants.ts
var constants = __webpack_require__(4126);
;// ./src/services/authService.ts


// Configurare axios instance pentru autentificare
const authAPI = axios/* default */.Ay.create({
    baseURL: constants/* API_BASE_URL */.JR,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    },
});
// Variabilă pentru stocarea token-ului curent
let currentToken = null;
// Interceptor pentru request-uri - adaugă token-ul de autentificare
authAPI.interceptors.request.use(config => {
    // Folosește token-ul setat prin setAuthToken în loc de localStorage direct
    if (currentToken && config.headers) {
        config.headers.Authorization = `Bearer ${currentToken}`;
    }
    return config;
}, (error) => {
    return Promise.reject(error);
});
// Interceptor pentru response-uri - gestionează erorile de autentificare
authAPI.interceptors.response.use((response) => {
    return response;
}, async (error) => {
    const originalRequest = error.config;
    // Dacă primim 401 și nu am încercat deja să reîmprospătăm token-ul
    if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        console.log('🔄 Attempting token refresh due to 401 error');
        try {
            const refreshToken = localStorage.getItem('refreshToken');
            if (refreshToken) {
                console.log('🔑 Found refresh token, attempting refresh...');
                // Apelează direct funcția de refresh din authService
                const refreshResponse = await authAPI.post(constants/* API_ENDPOINTS */.Sn.AUTH.REFRESH, {
                    refreshToken,
                });
                if (refreshResponse.data.success) {
                    const { tokens } = refreshResponse.data.data;
                    const { accessToken, refreshToken: newRefreshToken } = tokens;
                    console.log('✅ Token refresh successful');
                    // Actualizează token-urile
                    currentToken = accessToken;
                    authAPI.defaults.headers.Authorization = `Bearer ${accessToken}`;
                    localStorage.setItem('accessToken', accessToken);
                    if (newRefreshToken) {
                        localStorage.setItem('refreshToken', newRefreshToken);
                    }
                    // Retrimite cererea originală cu noul token
                    if (originalRequest.headers) {
                        originalRequest.headers.Authorization = `Bearer ${accessToken}`;
                    }
                    return authAPI(originalRequest);
                }
                else {
                    console.log('❌ Token refresh failed - invalid response');
                }
            }
            else {
                console.log('❌ No refresh token found');
            }
        }
        catch (refreshError) {
            console.log('❌ Token refresh error:', refreshError.response?.data?.message || refreshError.message);
            // Refresh token invalid, curăță token-urile și redirecționează
            currentToken = null;
            delete authAPI.defaults.headers.Authorization;
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            // Evită redirecționarea dacă suntem deja pe pagina de login
            if (!window.location.pathname.includes('/login')) {
                window.location.href = '/login';
            }
            return Promise.reject(refreshError);
        }
        // Dacă nu s-a putut reîmprospăta, curăță token-urile
        console.log('❌ Token refresh failed - clearing auth state');
        currentToken = null;
        delete authAPI.defaults.headers.Authorization;
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        // Evită redirecționarea dacă suntem deja pe pagina de login
        if (!window.location.pathname.includes('/login')) {
            window.location.href = '/login';
        }
    }
    return Promise.reject(error);
});
// Serviciul de autentificare
const authService = {
    // Setează token-ul de autentificare
    setAuthToken: (token) => {
        if (token) {
            currentToken = token;
            authAPI.defaults.headers.Authorization = `Bearer ${token}`;
            localStorage.setItem('accessToken', token);
        }
        else {
            currentToken = null;
            delete authAPI.defaults.headers.Authorization;
            localStorage.removeItem('accessToken');
        }
    },
    // Șterge token-ul de autentificare
    clearAuthToken: () => {
        currentToken = null;
        delete authAPI.defaults.headers.Authorization;
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
    },
    // Înregistrare utilizator nou
    register: async (userData) => {
        try {
            const response = await authAPI.post(constants/* API_ENDPOINTS */.Sn.AUTH.REGISTER, {
                firstName: userData.firstName,
                lastName: userData.lastName,
                email: userData.email,
                password: userData.password,
                currency: userData.currency,
                timezone: userData.timezone,
            });
            return {
                success: true,
                data: response.data.data,
                message: response.data.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Eroare la înregistrare',
                errors: error.response?.data?.errors || null,
            };
        }
    },
    // Autentificare utilizator
    login: async (credentials) => {
        try {
            const response = await authAPI.post(constants/* API_ENDPOINTS */.Sn.AUTH.LOGIN, {
                email: credentials.email,
                password: credentials.password,
            });
            const { user, tokens } = response.data.data;
            const { accessToken, refreshToken } = tokens;
            // Salvează token-urile în localStorage
            localStorage.setItem('accessToken', accessToken);
            localStorage.setItem('refreshToken', refreshToken);
            // Setează token-ul pentru cererile viitoare
            authService.setAuthToken(accessToken);
            return {
                success: true,
                data: { user, tokens: { accessToken, refreshToken } },
                message: response.data.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Eroare la autentificare',
                errors: error.response?.data?.errors || null,
            };
        }
    },
    // Deconectare utilizator
    logout: async (refreshToken) => {
        try {
            await authAPI.post(constants/* API_ENDPOINTS */.Sn.AUTH.LOGOUT, {
                refreshToken,
            });
            authService.clearAuthToken();
            return {
                success: true,
                message: 'Deconectare reușită',
            };
        }
        catch (error) {
            // Chiar dacă logout-ul de pe server eșuează, curățăm token-urile locale
            authService.clearAuthToken();
            return {
                success: false,
                message: error.response?.data?.message || 'Eroare la deconectare',
            };
        }
    },
    // Reîmprospătează token-ul de acces
    refreshToken: async (refreshToken) => {
        try {
            const response = await authAPI.post(constants/* API_ENDPOINTS */.Sn.AUTH.REFRESH, {
                refreshToken,
            });
            const { tokens } = response.data.data;
            const { accessToken, refreshToken: newRefreshToken } = tokens;
            // Actualizează token-urile folosind setAuthToken pentru consistență
            authService.setAuthToken(accessToken);
            if (newRefreshToken) {
                localStorage.setItem('refreshToken', newRefreshToken);
            }
            return {
                success: true,
                data: { accessToken, refreshToken: newRefreshToken || refreshToken },
                message: response.data.message,
            };
        }
        catch (error) {
            authService.clearAuthToken();
            return {
                success: false,
                message: error.response?.data?.message || 'Eroare la reîmprospătarea token-ului',
            };
        }
    },
    // Obține profilul utilizatorului curent
    getProfile: async () => {
        try {
            const response = await authAPI.get(constants/* API_ENDPOINTS */.Sn.AUTH.PROFILE);
            return {
                success: true,
                data: response.data.data,
                message: response.data.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Eroare la obținerea profilului',
            };
        }
    },
    // Actualizează profilul utilizatorului
    updateProfile: async (profileData) => {
        try {
            const response = await authAPI.put(constants/* API_ENDPOINTS */.Sn.AUTH.PROFILE, profileData);
            return {
                success: true,
                data: response.data.data,
                message: response.data.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Eroare la actualizarea profilului',
                errors: error.response?.data?.errors || null,
            };
        }
    },
    // Schimbă parola utilizatorului
    changePassword: async (passwordData) => {
        try {
            const response = await authAPI.put(constants/* API_ENDPOINTS */.Sn.AUTH.CHANGE_PASSWORD, {
                currentPassword: passwordData.currentPassword,
                newPassword: passwordData.newPassword,
                confirmPassword: passwordData.confirmPassword,
            });
            return {
                success: true,
                message: response.data.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Eroare la schimbarea parolei',
                errors: error.response?.data?.errors || null,
            };
        }
    },
    // Trimite email pentru resetarea parolei
    forgotPassword: async (email) => {
        try {
            const response = await authAPI.post(constants/* API_ENDPOINTS */.Sn.AUTH.FORGOT_PASSWORD, {
                email,
            });
            return {
                success: true,
                message: response.data.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Eroare la trimiterea email-ului',
                errors: error.response?.data?.errors || null,
            };
        }
    },
    // Resetează parola cu token-ul primit pe email
    resetPassword: async (token, newPassword) => {
        try {
            const response = await authAPI.post(constants/* API_ENDPOINTS */.Sn.AUTH.RESET_PASSWORD, {
                token,
                password: newPassword,
                confirmPassword: newPassword,
            });
            return {
                success: true,
                message: response.data.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Eroare la resetarea parolei',
                errors: error.response?.data?.errors || null,
            };
        }
    },
    // Verifică email-ul utilizatorului
    verifyEmail: async (token) => {
        try {
            const response = await authAPI.post(constants/* API_ENDPOINTS */.Sn.AUTH.VERIFY_EMAIL, {
                token,
            });
            return {
                success: true,
                message: response.data.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Eroare la verificarea email-ului',
            };
        }
    },
    // Retrimite email-ul de verificare
    resendVerificationEmail: async () => {
        try {
            const response = await authAPI.post(constants/* API_ENDPOINTS */.Sn.AUTH.RESEND_VERIFICATION);
            return {
                success: true,
                message: response.data.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.response?.data?.message || 'Eroare la retrimiterea email-ului',
            };
        }
    },
    // Verifică dacă utilizatorul este autentificat
    isAuthenticated: () => {
        const token = localStorage.getItem('accessToken');
        return !!token;
    },
    // Obține token-ul de acces din localStorage
    getAccessToken: () => {
        return localStorage.getItem('accessToken');
    },
    // Obține refresh token-ul din localStorage
    getRefreshToken: () => {
        return localStorage.getItem('refreshToken');
    },
    // Verifică dacă token-ul a expirat
    isTokenExpired: (token) => {
        if (!token)
            return true;
        try {
            // Decodează payload-ul JWT fără verificarea semnăturii
            const parts = token.split('.');
            if (parts.length !== 3)
                return true;
            const payload = JSON.parse(atob(parts[1]));
            const currentTime = Math.floor(Date.now() / 1000);
            // Verifică dacă token-ul a expirat (cu o marjă de 30 de secunde)
            return payload.exp < currentTime + 30;
        }
        catch (error) {
            console.error('Error decoding token:', error);
            return true; // Consideră token-ul expirat dacă nu poate fi decodat
        }
    },
    // Obține informații despre utilizator din token
    getUserFromToken: (token) => {
        if (!token)
            return null;
        try {
            const parts = token.split('.');
            if (parts.length !== 3)
                return null;
            const payload = JSON.parse(atob(parts[1]));
            return {
                id: payload.userId,
                email: payload.email,
                role: payload.role,
            };
        }
        catch (error) {
            return null;
        }
    },
};
/* harmony default export */ const services_authService = ((/* unused pure expression or super */ null && (authService)));

;// ./src/store/authStore.ts





// Tipuri de acțiuni pentru reducer
const AUTH_ACTIONS = {
    SET_LOADING: 'SET_LOADING',
    SET_USER: 'SET_USER',
    SET_TOKENS: 'SET_TOKENS',
    CLEAR_AUTH: 'CLEAR_AUTH',
    SET_ERROR: 'SET_ERROR',
    CLEAR_ERROR: 'CLEAR_ERROR',
};
// Starea inițială
const initialState = {
    user: null,
    accessToken: null,
    refreshToken: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
    lastActivity: null,
};
// Store pentru autentificare
const useAuthStore = (0,esm/* create */.vt)()((0,middleware/* persist */.Zr)((set, get) => ({
    // Starea inițială
    ...initialState,
    // Acțiuni
    setLoading: loading => {
        set({ isLoading: loading });
    },
    setError: error => {
        set({ error });
        if (error) {
            dist/* default */.Ay.error(error);
        }
    },
    clearError: () => {
        set({ error: null });
    },
    setUser: user => {
        set({
            user,
            isAuthenticated: !!user,
            lastActivity: Date.now(),
        });
    },
    setTokens: (accessToken, refreshToken) => {
        set({
            accessToken,
            refreshToken,
            lastActivity: Date.now(),
        });
        // Setează token-ul în authService pentru cererile viitoare
        if (accessToken) {
            authService.setAuthToken(accessToken);
        }
    },
    clearAuth: () => {
        console.log('🧹 Clearing auth state...');
        // Curăță token-urile din authService și localStorage
        authService.clearAuthToken();
        // Resetează starea de autentificare
        set(initialState);
        // Șterge și din localStorage
        localStorage.removeItem('auth-storage');
        console.log('✅ Auth state cleared successfully');
    },
    // Funcții pentru autentificare
    login: async (credentials) => {
        try {
            set({ isLoading: true, error: null });
            const response = await authService.login(credentials);
            if (response.success) {
                const { user, tokens } = response.data;
                const { accessToken, refreshToken } = tokens;
                set({
                    user,
                    accessToken,
                    refreshToken,
                    isAuthenticated: true,
                    isLoading: false,
                    lastActivity: Date.now(),
                });
                authService.setAuthToken(accessToken);
                dist/* default */.Ay.success('Autentificare reușită!');
                return { success: true };
            }
            else {
                set({ error: response.message || 'Eroare la autentificare', isLoading: false });
                return { success: false, message: response.message || 'Eroare necunoscută' };
            }
        }
        catch (error) {
            const errorMessage = error.response?.data?.message || 'Eroare la autentificare';
            set({ error: errorMessage, isLoading: false });
            return { success: false, message: errorMessage };
        }
    },
    register: async (userData) => {
        try {
            set({ isLoading: true, error: null });
            const response = await authService.register(userData);
            if (response.success) {
                set({ isLoading: false });
                dist/* default */.Ay.success('Cont creat cu succes! Te poți autentifica acum.');
                return { success: true };
            }
            else {
                set({ error: response.message || 'Eroare la înregistrare', isLoading: false });
                return { success: false, message: response.message || 'Eroare la înregistrare' };
            }
        }
        catch (error) {
            const errorMessage = error.response?.data?.message || 'Eroare la înregistrare';
            set({ error: errorMessage, isLoading: false });
            return { success: false, message: errorMessage };
        }
    },
    logout: async () => {
        try {
            const { refreshToken } = get();
            if (refreshToken) {
                await authService.logout(refreshToken);
            }
            get().clearAuth();
            dist/* default */.Ay.success('Deconectare reușită!');
        }
        catch (error) {
            // Chiar dacă logout-ul de pe server eșuează, curățăm starea locală
            get().clearAuth();
            console.error('Eroare la logout:', error);
        }
    },
    refreshAccessToken: async () => {
        try {
            const { refreshToken } = get();
            console.log('🔄 Attempting to refresh access token...', {
                hasRefreshToken: !!refreshToken,
            });
            if (!refreshToken) {
                console.log('❌ No refresh token available');
                get().clearAuth();
                return { success: false, message: 'Nu există refresh token' };
            }
            const response = await authService.refreshToken(refreshToken);
            if (response.success) {
                const { accessToken: newAccessToken, refreshToken: newRefreshToken } = response.data;
                console.log('✅ Token refresh successful');
                // Actualizează token-urile în store
                set({
                    accessToken: newAccessToken,
                    refreshToken: newRefreshToken,
                    lastActivity: Date.now(),
                });
                // Actualizează token-ul în authService
                authService.setAuthToken(newAccessToken);
                return { success: true };
            }
            else {
                console.log('❌ Token refresh failed:', response.message);
                // Refresh token invalid sau expirat
                get().clearAuth();
                return { success: false, message: response.message || 'Eroare la refresh token' };
            }
        }
        catch (error) {
            console.error('❌ Error during token refresh:', error);
            get().clearAuth();
            return { success: false, message: 'Eroare la reîmprospătarea token-ului' };
        }
    },
    updateProfile: async (profileData) => {
        try {
            set({ isLoading: true, error: null });
            const response = await authService.updateProfile(profileData);
            if (response.success) {
                const currentUser = get().user;
                if (currentUser) {
                    set({
                        user: { ...currentUser, ...response.data },
                        isLoading: false,
                    });
                }
                dist/* default */.Ay.success('Profil actualizat cu succes!');
                return { success: true };
            }
            else {
                set({ error: response.message || 'Eroare la actualizare profil', isLoading: false });
                return { success: false, message: response.message || 'Eroare la actualizare profil' };
            }
        }
        catch (error) {
            const errorMessage = error.response?.data?.message || 'Eroare la actualizarea profilului';
            set({ error: errorMessage, isLoading: false });
            return { success: false, message: errorMessage };
        }
    },
    changePassword: async (passwordData) => {
        try {
            set({ isLoading: true, error: null });
            const response = await authService.changePassword(passwordData);
            if (response.success) {
                set({ isLoading: false });
                dist/* default */.Ay.success('Parola a fost schimbată cu succes!');
                return { success: true };
            }
            else {
                set({ error: response.message || 'Eroare la schimbarea parolei', isLoading: false });
                return { success: false, message: response.message || 'Eroare la schimbarea parolei' };
            }
        }
        catch (error) {
            const errorMessage = error.response?.data?.message || 'Eroare la schimbarea parolei';
            set({ error: errorMessage, isLoading: false });
            return { success: false, message: errorMessage };
        }
    },
    forgotPassword: async (email) => {
        try {
            set({ isLoading: true, error: null });
            const response = await authService.forgotPassword(email);
            if (response.success) {
                set({ isLoading: false });
                dist/* default */.Ay.success('Link-ul de resetare a fost trimis pe email!');
                return { success: true };
            }
            else {
                set({ error: response.message || 'Eroare la trimiterea email-ului', isLoading: false });
                return {
                    success: false,
                    message: response.message || 'Eroare la trimiterea email-ului',
                };
            }
        }
        catch (error) {
            const errorMessage = error.response?.data?.message || 'Eroare la trimiterea email-ului';
            set({ error: errorMessage, isLoading: false });
            return { success: false, message: errorMessage };
        }
    },
    resetPassword: async (token, newPassword) => {
        try {
            set({ isLoading: true, error: null });
            const response = await authService.resetPassword(token, newPassword);
            if (response.success) {
                set({ isLoading: false });
                dist/* default */.Ay.success('Parola a fost resetată cu succes!');
                return { success: true };
            }
            else {
                set({ error: response.message || 'Eroare la resetarea parolei', isLoading: false });
                return { success: false, message: response.message || 'Eroare la resetarea parolei' };
            }
        }
        catch (error) {
            const errorMessage = error.response?.data?.message || 'Eroare la resetarea parolei';
            set({ error: errorMessage, isLoading: false });
            return { success: false, message: errorMessage };
        }
    },
    // Inițializează autentificarea la pornirea aplicației
    initializeAuth: async () => {
        try {
            set({ isLoading: true });
            const { accessToken, refreshToken, user } = get();
            console.log('🚀 Initializing auth...', {
                hasAccessToken: !!accessToken,
                hasRefreshToken: !!refreshToken,
                hasUser: !!user,
            });
            if (accessToken && refreshToken && user) {
                // Verifică dacă token-ul este expirat
                if (authService.isTokenExpired(accessToken)) {
                    console.log('🔄 Access token expired, attempting refresh...');
                    // Token expirat, încearcă să-l reîmprospătezi
                    const refreshResult = await get().refreshAccessToken();
                    if (refreshResult.success) {
                        console.log('✅ Token refreshed successfully during init');
                        set({
                            isAuthenticated: true,
                            isLoading: false,
                            lastActivity: Date.now(),
                        });
                    }
                    else {
                        console.log('❌ Token refresh failed during init');
                        get().clearAuth();
                        set({ isLoading: false });
                    }
                }
                else {
                    // Token-ul pare valid, setează-l și verifică cu serverul
                    authService.setAuthToken(accessToken);
                    try {
                        // Verifică validitatea token-ului cu serverul
                        const profileResponse = await authService.getProfile();
                        if (profileResponse.success && profileResponse.data) {
                            console.log('✅ Auth initialized successfully with valid token');
                            set({
                                user: profileResponse.data,
                                isAuthenticated: true,
                                isLoading: false,
                                lastActivity: Date.now(),
                            });
                        }
                        else {
                            console.log('❌ Profile fetch failed, token might be invalid');
                            // Token invalid pe server, încearcă să-l reîmprospătezi
                            const refreshResult = await get().refreshAccessToken();
                            if (!refreshResult.success) {
                                get().clearAuth();
                            }
                            set({ isLoading: false });
                        }
                    }
                    catch (error) {
                        console.log('❌ Error during profile fetch:', error.message);
                        // Eroare la verificarea profilului, încearcă refresh
                        if (refreshToken) {
                            const refreshResult = await get().refreshAccessToken();
                            if (!refreshResult.success) {
                                get().clearAuth();
                            }
                        }
                        else {
                            get().clearAuth();
                        }
                        set({ isLoading: false });
                    }
                }
            }
            else {
                console.log('❌ Missing auth data, clearing auth state');
                get().clearAuth();
                set({ isLoading: false });
            }
        }
        catch (error) {
            console.error('❌ Error during auth initialization:', error);
            get().clearAuth();
            set({ isLoading: false });
        }
    },
    // Verifică activitatea utilizatorului
    checkUserActivity: () => {
        const { lastActivity, isAuthenticated } = get();
        if (isAuthenticated && lastActivity) {
            const now = Date.now();
            const timeSinceLastActivity = now - lastActivity;
            console.log('⏰ Checking user activity...', {
                timeSinceLastActivity: `${Math.floor(timeSinceLastActivity / 1000)}s`,
                inactivityTimeout: `${Math.floor(auth.INACTIVITY_TIMEOUT / 1000)}s`,
            });
            // Deconectează utilizatorul după perioada de inactivitate configurată
            if (timeSinceLastActivity > auth.INACTIVITY_TIMEOUT) {
                console.log('⏰ Session expired due to inactivity');
                get().logout();
                dist/* default */.Ay.error(`${auth.ERROR_MESSAGES.SESSION_EXPIRED} din cauza inactivității`);
            }
        }
    },
    // Actualizează ultima activitate
    updateActivity: () => {
        const { isAuthenticated } = get();
        if (isAuthenticated) {
            set({ lastActivity: Date.now() });
        }
    },
}), {
    name: 'auth-storage',
    storage: (0,middleware/* createJSONStorage */.KU)(() => localStorage),
    partialize: state => ({
        user: state.user,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
        lastActivity: state.lastActivity,
    }),
    onRehydrateStorage: () => state => {
        if (state?.accessToken) {
            authService.setAuthToken(state.accessToken);
        }
    },
}));
// Selector hooks pentru performanță optimă
const useAuth = () => {
    const store = useAuthStore();
    return {
        user: store.user,
        isAuthenticated: store.isAuthenticated,
        isLoading: store.isLoading,
        error: store.error,
    };
};
const useAuthActions = () => {
    const store = useAuthStore();
    return {
        login: store.login,
        register: store.register,
        logout: store.logout,
        refreshAccessToken: store.refreshAccessToken,
        updateProfile: store.updateProfile,
        changePassword: store.changePassword,
        forgotPassword: store.forgotPassword,
        resetPassword: store.resetPassword,
        clearError: store.clearError,
        updateActivity: store.updateActivity,
    };
};
// Hook pentru verificarea permisiunilor
const usePermissions = () => {
    const { user } = useAuth();
    return {
        isAdmin: user?.role === 'admin',
        canManageUsers: user?.role === 'admin',
        canExportData: true, // Toți utilizatorii pot exporta propriile date
        canImportData: true, // Toți utilizatorii pot importa date
    };
};


/***/ }),

/***/ 1134:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {


// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(4848);
// EXTERNAL MODULE: ./node_modules/@tanstack/react-query/build/modern/index.js + 21 modules
var modern = __webpack_require__(8035);
// EXTERNAL MODULE: ./node_modules/@tanstack/react-query-devtools/build/modern/index.js + 3 modules
var build_modern = __webpack_require__(5118);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(6540);
var react_default = /*#__PURE__*/__webpack_require__.n(react);
// EXTERNAL MODULE: ./node_modules/react-dom/client.js
var client = __webpack_require__(5338);
// EXTERNAL MODULE: ./node_modules/react-hot-toast/dist/index.mjs + 1 modules
var dist = __webpack_require__(888);
// EXTERNAL MODULE: ./node_modules/react-router-dom/dist/index.js
var react_router_dom_dist = __webpack_require__(4976);
// EXTERNAL MODULE: ./src/store/authStore.ts + 2 modules
var authStore = __webpack_require__(250);
// EXTERNAL MODULE: ./src/components/ui/LoadingSpinner.tsx
var LoadingSpinner = __webpack_require__(9264);
;// ./src/components/auth/ProtectedRoute.tsx





/**
 * Componentă pentru protejarea rutelor care necesită autentificare
 * Redirecționează utilizatorii neautentificați către pagina de login
 */
const ProtectedRoute = ({ children, roles = [], requireAdmin = false, redirectTo = '/login', fallback = null, }) => {
    const { isAuthenticated, isLoading, user } = (0,authStore/* useAuthStore */.nc)();
    const location = (0,react_router_dom_dist/* useLocation */.zy)();
    // Afișează loader în timpul verificării autentificării
    if (isLoading) {
        return fallback || ((0,jsx_runtime.jsx)("div", { className: "min-h-screen flex items-center justify-center bg-gray-50", children: (0,jsx_runtime.jsxs)("div", { className: "text-center", children: [(0,jsx_runtime.jsx)(LoadingSpinner/* default */.Ay, { size: "lg" }), (0,jsx_runtime.jsx)("p", { className: "mt-4 text-gray-600", children: "Verificare autentificare..." })] }) }));
    }
    // Redirecționează către login dacă utilizatorul nu este autentificat
    if (!isAuthenticated) {
        return ((0,jsx_runtime.jsx)(react_router_dom_dist/* Navigate */.C5, { to: redirectTo, state: { from: location.pathname }, replace: true }));
    }
    // Verifică dacă este necesar rol de admin
    if (requireAdmin && user) {
        const userRole = user.role || 'user';
        if (userRole !== 'admin') {
            // Redirecționează către dashboard dacă nu este admin
            return ((0,jsx_runtime.jsx)(react_router_dom_dist/* Navigate */.C5, { to: "/app/dashboard", state: {
                    error: 'Nu ai permisiunea să accesezi această pagină. Doar administratorii pot accesa această secțiune.',
                    from: location.pathname,
                }, replace: true }));
        }
    }
    // Verifică rolurile dacă sunt specificate
    if (roles.length > 0 && user) {
        const userRole = user.role || 'user';
        const hasRequiredRole = roles.includes(userRole);
        if (!hasRequiredRole) {
            // Redirecționează către dashboard-ul corespunzător rolului utilizatorului
            const dashboardUrl = userRole === 'admin' ? '/app/admin/dashboard' : '/app/dashboard';
            return ((0,jsx_runtime.jsx)(react_router_dom_dist/* Navigate */.C5, { to: dashboardUrl, state: {
                    error: 'Nu ai permisiunea să accesezi această pagină.',
                    from: location.pathname,
                }, replace: true }));
        }
    }
    // Utilizatorul este autentificat și are rolurile necesare
    return children;
};
/**
 * Hook pentru verificarea permisiunilor utilizatorului
 */
const usePermissions = (requiredRoles = []) => {
    const { user, isAuthenticated } = useAuthStore();
    const userRole = user?.role || '';
    const hasRequiredRole = requiredRoles.length === 0 ||
        requiredRoles.includes(userRole);
    const isAdmin = userRole === 'admin';
    const isUser = userRole === 'user';
    return {
        isAuthenticated,
        userRole,
        hasRequiredRole,
        canAccess: hasRequiredRole,
        isAdmin,
        isUser,
    };
};
/**
 * Componentă pentru protejarea bazată pe permisiuni
 */
const PermissionGuard = ({ children, roles = [], fallback = null }) => {
    const { canAccess } = usePermissions(roles);
    return canAccess ? children : fallback;
};
/**
 * Componentă specializată pentru rute de administrator
 */
const AdminRoute = ({ children, fallback = null }) => {
    return (_jsx(ProtectedRoute, { roles: ['admin'], children: children }));
};
/**
 * Componentă pentru verificarea dacă utilizatorul este proprietarul unei resurse
 */
const OwnershipGuard = ({ resourceUserId, children, fallback = null }) => {
    const { user } = useAuthStore();
    const isOwner = user && (user.id === resourceUserId || user.role === 'admin');
    return isOwner ? children : fallback;
};
/**
 * HOC pentru protejarea componentelor
 */
const withAuth = (Component, roles = []) => {
    return function AuthenticatedComponent(props) {
        return (_jsx(ProtectedRoute, { roles: roles, children: _jsx(Component, { ...props }) }));
    };
};
/**
 * Hook pentru verificarea stării de autentificare
 */
const useAuthStatus = () => {
    const { isAuthenticated, isLoading, user, error } = useAuthStore();
    return {
        isAuthenticated,
        isLoading,
        user,
        error,
        isGuest: !isAuthenticated && !isLoading,
    };
};
/* harmony default export */ const auth_ProtectedRoute = (ProtectedRoute);

;// ./src/components/auth/PublicRoute.tsx





/**
 * Componentă pentru rutele publice care trebuie să fie accesibile doar utilizatorilor neautentificați
 * Redirecționează utilizatorii autentificați către dashboard sau o altă pagină specificată
 */
const PublicRoute = ({ children, redirectTo = '/app/dashboard', fallback = null, allowAuthenticated = false, }) => {
    const { isAuthenticated, isLoading, user } = (0,authStore/* useAuthStore */.nc)();
    const location = (0,react_router_dom_dist/* useLocation */.zy)();
    // Afișează loader în timpul verificării autentificării
    if (isLoading) {
        return fallback || ((0,jsx_runtime.jsx)("div", { className: "min-h-screen flex items-center justify-center bg-gray-50", children: (0,jsx_runtime.jsxs)("div", { className: "text-center", children: [(0,jsx_runtime.jsx)(LoadingSpinner/* default */.Ay, { size: "lg" }), (0,jsx_runtime.jsx)("p", { className: "mt-4 text-gray-600", children: "Verificare autentificare..." })] }) }));
    }
    // Dacă utilizatorul este autentificat și nu permitem accesul autentificat
    if (isAuthenticated && !allowAuthenticated) {
        // Determină destinația în funcție de rolul utilizatorului
        let defaultRedirect = redirectTo;
        if (user?.role === 'admin' && redirectTo === '/app/dashboard') {
            defaultRedirect = '/app/admin/dashboard';
        }
        // Verifică dacă există o destinație salvată în state
        const from = location.state?.from || defaultRedirect;
        return ((0,jsx_runtime.jsx)(react_router_dom_dist/* Navigate */.C5, { to: from, replace: true }));
    }
    // Utilizatorul nu este autentificat sau permitem accesul autentificat
    return children;
};
/**
 * Componentă pentru rutele de autentificare (login, register, etc.)
 * Acestea sunt accesibile doar utilizatorilor neautentificați
 */
const AuthRoute = ({ children }) => {
    return (_jsx(PublicRoute, { children: children }));
};
/**
 * Componentă pentru rutele care sunt accesibile tuturor utilizatorilor
 * indiferent de starea de autentificare (ex: pagina principală, despre noi, etc.)
 */
const GuestRoute = ({ children }) => {
    return (_jsx(PublicRoute, { allowAuthenticated: true, children: children }));
};
/**
 * Componentă pentru afișarea condițională a conținutului bazat pe starea de autentificare
 */
const ConditionalRender = ({ authenticated = null, unauthenticated = null, loading = null, }) => {
    const { isAuthenticated, isLoading } = useAuthStore();
    if (isLoading && loading) {
        return loading;
    }
    if (isAuthenticated && authenticated) {
        return authenticated;
    }
    if (!isAuthenticated && unauthenticated) {
        return unauthenticated;
    }
    return null;
};
/**
 * Hook pentru verificarea dacă utilizatorul este guest (neautentificat)
 */
const useGuestStatus = () => {
    const { isAuthenticated, isLoading } = useAuthStore();
    return {
        isGuest: !isAuthenticated && !isLoading,
        isLoading,
        isAuthenticated,
        canAccessAuthRoutes: !isAuthenticated && !isLoading,
    };
};
/**
 * HOC pentru rutele publice
 */
const withPublicRoute = (Component, options = {}) => {
    return function PublicRouteComponent(props) {
        return (_jsx(PublicRoute, { ...options, children: _jsx(Component, { ...props }) }));
    };
};
/**
 * Componentă pentru rutele de recuperare a parolei
 * Accesibilă doar utilizatorilor neautentificați
 */
const PasswordRecoveryRoute = ({ children }) => {
    const { user } = useAuthStore();
    const redirectTo = user?.role === 'admin' ? '/app/admin/dashboard' : '/app/dashboard';
    return (_jsx(PublicRoute, { redirectTo: redirectTo, children: children }));
};
/**
 * Hook pentru obținerea URL-ului de redirecționare în funcție de rolul utilizatorului
 */
const useRedirectUrl = () => {
    const { user } = (0,authStore/* useAuthStore */.nc)();
    return user?.role === 'admin' ? '/app/admin/dashboard' : '/app/dashboard';
};
/**
 * Componentă pentru rutele de verificare a email-ului
 * Accesibilă doar utilizatorilor neautentificați
 */
const EmailVerificationRoute = ({ children }) => {
    return (_jsx(PublicRoute, { allowAuthenticated: true, children: children }));
};
/* harmony default export */ const auth_PublicRoute = (PublicRoute);

// EXTERNAL MODULE: ./src/utils/helpers.ts
var helpers = __webpack_require__(3658);
// EXTERNAL MODULE: ./node_modules/@heroicons/react/24/outline/esm/index.js + 324 modules
var esm = __webpack_require__(3740);
;// ./src/components/layout/Footer.tsx





/**
 * Componenta Footer pentru partea de jos a aplicației
 */
const Footer = ({ className = '', minimal = false, showLinks = true, showContact = false, }) => {
    const currentYear = new Date().getFullYear();
    // Link-uri pentru footer
    const footerLinks = {
        product: [
            { name: 'Funcționalități', href: '/features' },
            { name: 'Prețuri', href: '/pricing' },
            { name: 'Actualizări', href: '/updates' },
            { name: 'Roadmap', href: '/roadmap' },
        ],
        support: [
            { name: 'Documentație', href: '/docs' },
            { name: 'Ghiduri', href: '/guides' },
            { name: 'Suport', href: '/support' },
            { name: 'FAQ', href: '/faq' },
        ],
        company: [
            { name: 'Despre noi', href: '/about' },
            { name: 'Blog', href: '/blog' },
            { name: 'Cariere', href: '/careers' },
            { name: 'Contact', href: '/contact' },
        ],
        legal: [
            { name: 'Confidențialitate', href: '/privacy' },
            { name: 'Termeni', href: '/terms' },
            { name: 'Cookies', href: '/cookies' },
            { name: 'Licențe', href: '/licenses' },
        ],
    };
    // Informații de contact
    const contactInfo = [
        {
            icon: esm/* EnvelopeIcon */.u6c,
            label: 'Email',
            value: '<EMAIL>',
            href: 'mailto:<EMAIL>',
        },
        {
            icon: esm/* PhoneIcon */.EsG,
            label: 'Telefon',
            value: '+40 ***********',
            href: 'tel:+40123456789',
        },
        {
            icon: esm/* MapPinIcon */.TbR,
            label: 'Adresă',
            value: 'București, România',
            href: null,
        },
    ];
    // Social media links
    const socialLinks = [
        {
            name: 'Facebook',
            href: 'https://facebook.com/expensetracker',
            icon: (props) => ((0,jsx_runtime.jsx)("svg", { fill: "currentColor", viewBox: "0 0 24 24", ...props, children: (0,jsx_runtime.jsx)("path", { d: "M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" }) })),
        },
        {
            name: 'Twitter',
            href: 'https://twitter.com/expensetracker',
            icon: (props) => ((0,jsx_runtime.jsx)("svg", { fill: "currentColor", viewBox: "0 0 24 24", ...props, children: (0,jsx_runtime.jsx)("path", { d: "M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" }) })),
        },
        {
            name: 'LinkedIn',
            href: 'https://linkedin.com/company/expensetracker',
            icon: (props) => ((0,jsx_runtime.jsx)("svg", { fill: "currentColor", viewBox: "0 0 24 24", ...props, children: (0,jsx_runtime.jsx)("path", { d: "M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" }) })),
        },
        {
            name: 'GitHub',
            href: 'https://github.com/expensetracker',
            icon: (props) => ((0,jsx_runtime.jsx)("svg", { fill: "currentColor", viewBox: "0 0 24 24", ...props, children: (0,jsx_runtime.jsx)("path", { d: "M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" }) })),
        },
    ];
    if (minimal) {
        return ((0,jsx_runtime.jsx)("footer", { className: (0,helpers.cn)('bg-white border-t border-gray-200 py-4', className), children: (0,jsx_runtime.jsx)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: (0,jsx_runtime.jsxs)("div", { className: "flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0", children: [(0,jsx_runtime.jsxs)("div", { className: "flex items-center space-x-2 text-sm text-gray-600", children: [(0,jsx_runtime.jsxs)("span", { children: ["\u00A9 ", currentYear, " Expense Tracker."] }), (0,jsx_runtime.jsx)("span", { children: "Toate drepturile rezervate." })] }), (0,jsx_runtime.jsxs)("div", { className: "flex items-center space-x-1 text-sm text-gray-600", children: [(0,jsx_runtime.jsx)("span", { children: "F\u0103cut cu" }), (0,jsx_runtime.jsx)(esm/* HeartIcon */.C3E, { className: "h-4 w-4 text-red-500" }), (0,jsx_runtime.jsx)("span", { children: "\u00EEn Rom\u00E2nia" })] })] }) }) }));
    }
    return ((0,jsx_runtime.jsx)("footer", { className: (0,helpers.cn)('bg-gray-50 border-t border-gray-200', className), children: (0,jsx_runtime.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12", children: [(0,jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8", children: [(0,jsx_runtime.jsxs)("div", { className: "col-span-1 lg:col-span-1", children: [(0,jsx_runtime.jsxs)("div", { className: "flex items-center space-x-2 mb-4", children: [(0,jsx_runtime.jsx)("div", { className: "w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center", children: (0,jsx_runtime.jsx)("span", { className: "text-white font-bold text-sm", children: "ET" }) }), (0,jsx_runtime.jsx)("span", { className: "font-bold text-xl text-gray-900", children: "Expense Tracker" })] }), (0,jsx_runtime.jsx)("p", { className: "text-gray-600 text-sm mb-4", children: "Aplica\u021Bia ta de \u00EEncredere pentru gestionarea cheltuielilor personale. Simplu, eficient \u0219i sigur." }), (0,jsx_runtime.jsx)("div", { className: "flex space-x-4", children: socialLinks.map((item) => ((0,jsx_runtime.jsx)("a", { href: item.href, target: "_blank", rel: "noopener noreferrer", className: "text-gray-400 hover:text-primary-600 transition-colors", "aria-label": item.name, children: (0,jsx_runtime.jsx)(item.icon, { className: "h-5 w-5" }) }, item.name))) })] }), showLinks && ((0,jsx_runtime.jsxs)(jsx_runtime.Fragment, { children: [(0,jsx_runtime.jsxs)("div", { children: [(0,jsx_runtime.jsx)("h3", { className: "font-semibold text-gray-900 mb-4", children: "Produs" }), (0,jsx_runtime.jsx)("ul", { className: "space-y-2", children: footerLinks.product.map((link) => ((0,jsx_runtime.jsx)("li", { children: (0,jsx_runtime.jsx)(react_router_dom_dist/* Link */.N_, { to: link.href, className: "text-gray-600 hover:text-primary-600 text-sm transition-colors", children: link.name }) }, link.name))) })] }), (0,jsx_runtime.jsxs)("div", { children: [(0,jsx_runtime.jsx)("h3", { className: "font-semibold text-gray-900 mb-4", children: "Suport" }), (0,jsx_runtime.jsx)("ul", { className: "space-y-2", children: footerLinks.support.map((link) => ((0,jsx_runtime.jsx)("li", { children: (0,jsx_runtime.jsx)(react_router_dom_dist/* Link */.N_, { to: link.href, className: "text-gray-600 hover:text-primary-600 text-sm transition-colors", children: link.name }) }, link.name))) })] }), (0,jsx_runtime.jsxs)("div", { children: [(0,jsx_runtime.jsx)("h3", { className: "font-semibold text-gray-900 mb-4", children: "Companie" }), (0,jsx_runtime.jsx)("ul", { className: "space-y-2", children: footerLinks.company.map((link) => ((0,jsx_runtime.jsx)("li", { children: (0,jsx_runtime.jsx)(react_router_dom_dist/* Link */.N_, { to: link.href, className: "text-gray-600 hover:text-primary-600 text-sm transition-colors", children: link.name }) }, link.name))) })] })] })), showContact && ((0,jsx_runtime.jsxs)("div", { children: [(0,jsx_runtime.jsx)("h3", { className: "font-semibold text-gray-900 mb-4", children: "Contact" }), (0,jsx_runtime.jsx)("ul", { className: "space-y-3", children: contactInfo.map((item) => ((0,jsx_runtime.jsxs)("li", { className: "flex items-center space-x-3", children: [(0,jsx_runtime.jsx)(item.icon, { className: "h-4 w-4 text-gray-400 flex-shrink-0" }), item.href ? ((0,jsx_runtime.jsx)("a", { href: item.href, className: "text-gray-600 hover:text-primary-600 text-sm transition-colors", children: item.value })) : ((0,jsx_runtime.jsx)("span", { className: "text-gray-600 text-sm", children: item.value }))] }, item.label))) })] }))] }), (0,jsx_runtime.jsx)("div", { className: "mt-8 pt-8 border-t border-gray-200", children: (0,jsx_runtime.jsxs)("div", { className: "flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0", children: [(0,jsx_runtime.jsx)("div", { className: "flex items-center space-x-4 text-sm text-gray-600", children: (0,jsx_runtime.jsxs)("span", { children: ["\u00A9 ", currentYear, " Expense Tracker. Toate drepturile rezervate."] }) }), (0,jsx_runtime.jsx)("div", { className: "flex items-center space-x-6", children: footerLinks.legal.map((link) => ((0,jsx_runtime.jsx)(react_router_dom_dist/* Link */.N_, { to: link.href, className: "text-gray-600 hover:text-primary-600 text-sm transition-colors", children: link.name }, link.name))) }), (0,jsx_runtime.jsxs)("div", { className: "flex items-center space-x-1 text-sm text-gray-600", children: [(0,jsx_runtime.jsx)("span", { children: "F\u0103cut cu" }), (0,jsx_runtime.jsx)(esm/* HeartIcon */.C3E, { className: "h-4 w-4 text-red-500" }), (0,jsx_runtime.jsx)("span", { children: "\u00EEn Rom\u00E2nia" })] })] }) })] }) }));
};
/**
 * Footer simplu pentru pagini de autentificare
 */
const AuthFooter = () => {
    return (_jsx(Footer, { minimal: true, className: "fixed bottom-0 left-0 right-0" }));
};
/**
 * Footer pentru pagini publice
 */
const PublicFooter = () => {
    return (_jsx(Footer, { showLinks: true, showContact: true }));
};
/* harmony default export */ const layout_Footer = (Footer);

// EXTERNAL MODULE: ./src/utils/cn.ts
var cn = __webpack_require__(9050);
// EXTERNAL MODULE: ./src/components/ui/UserAvatar.tsx
var UserAvatar = __webpack_require__(5649);
;// ./src/components/layout/Header.tsx








/**
 * Componenta Header pentru navigația principală
 */
const Header = ({ onSidebarToggle, showSidebarToggle = true, sidebarOpen = false, }) => {
    const { user, logout, isAuthenticated } = (0,authStore/* useAuthStore */.nc)();
    const navigate = (0,react_router_dom_dist/* useNavigate */.Zp)();
    const location = (0,react_router_dom_dist/* useLocation */.zy)();
    const [userMenuOpen, setUserMenuOpen] = (0,react.useState)(false);
    const [notificationsOpen, setNotificationsOpen] = (0,react.useState)(false);
    const [searchQuery, setSearchQuery] = (0,react.useState)('');
    const userMenuRef = (0,react.useRef)(null);
    const notificationsRef = (0,react.useRef)(null);
    // Închide meniurile când se face click în afara lor
    (0,react.useEffect)(() => {
        const handleClickOutside = (event) => {
            if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
                setUserMenuOpen(false);
            }
            if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {
                setNotificationsOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    // Închide meniurile la schimbarea rutei
    (0,react.useEffect)(() => {
        setUserMenuOpen(false);
        setNotificationsOpen(false);
    }, [location.pathname]);
    const handleLogout = async () => {
        try {
            await logout();
            navigate('/login');
        }
        catch (error) {
            dist/* toast */.oR.error('Eroare la deconectare');
        }
    };
    const handleSearch = (e) => {
        e.preventDefault();
        if (searchQuery.trim()) {
            navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
            setSearchQuery('');
        }
    };
    const getPageTitle = () => {
        const path = location.pathname;
        const titles = {
            '/dashboard': 'Dashboard',
            '/expenses': 'Cheltuieli',
            '/categories': 'Categorii',
            '/reports': 'Rapoarte',
            '/profile': 'Profil',
            '/settings': 'Setări',
        };
        return titles[path] || 'Expense Tracker';
    };
    // Notificări mock - în viitor vor veni din API
    const notifications = [
        {
            id: 1,
            title: 'Cheltuială adăugată',
            message: 'Ai adăugat o cheltuială de 150 RON',
            time: '2 min',
            read: false,
        },
        {
            id: 2,
            title: 'Raport lunar gata',
            message: 'Raportul pentru decembrie este disponibil',
            time: '1 oră',
            read: true,
        },
    ];
    const unreadCount = notifications.filter(n => !n.read).length;
    return ((0,jsx_runtime.jsx)("header", { className: "bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30", children: (0,jsx_runtime.jsx)("div", { className: "px-4 sm:px-6 lg:px-8", children: (0,jsx_runtime.jsxs)("div", { className: "flex justify-between items-center h-16", children: [(0,jsx_runtime.jsxs)("div", { className: "flex items-center space-x-2", children: [showSidebarToggle && ((0,jsx_runtime.jsx)("button", { onClick: onSidebarToggle, className: "lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500", "aria-label": "Deschide meniul", children: (0,jsx_runtime.jsx)(esm/* Bars3Icon */.tKh, { className: "h-6 w-6" }) })), (0,jsx_runtime.jsx)(react_router_dom_dist/* Link */.N_, { to: "/", className: "flex items-center text-primary-600 hover:text-primary-700 transition-colors", children: (0,jsx_runtime.jsx)("div", { className: "w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center", children: (0,jsx_runtime.jsx)("span", { className: "text-white font-bold text-sm", children: "ET" }) }) }), isAuthenticated && ((0,jsx_runtime.jsx)("div", { className: "hidden md:block", children: (0,jsx_runtime.jsx)("h1", { className: "text-xl font-semibold text-gray-900", children: getPageTitle() }) }))] }), isAuthenticated && ((0,jsx_runtime.jsx)("div", { className: "hidden md:flex flex-1 max-w-md mx-8", children: (0,jsx_runtime.jsx)("form", { onSubmit: handleSearch, className: "w-full", children: (0,jsx_runtime.jsxs)("div", { className: "relative", children: [(0,jsx_runtime.jsx)(esm/* MagnifyingGlassIcon */.$p$, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" }), (0,jsx_runtime.jsx)("input", { type: "text", value: searchQuery, onChange: (e) => setSearchQuery(e.target.value), placeholder: "Caut\u0103 cheltuieli, categorii...", className: "w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent" })] }) }) })), (0,jsx_runtime.jsx)("div", { className: "flex items-center space-x-4", children: isAuthenticated ? ((0,jsx_runtime.jsxs)(jsx_runtime.Fragment, { children: [(0,jsx_runtime.jsxs)("div", { className: "relative", ref: notificationsRef, children: [(0,jsx_runtime.jsxs)("button", { onClick: () => setNotificationsOpen(!notificationsOpen), className: "relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500", "aria-label": "Notific\u0103ri", children: [(0,jsx_runtime.jsx)(esm/* BellIcon */.XFE, { className: "h-6 w-6" }), unreadCount > 0 && ((0,jsx_runtime.jsx)("span", { className: "absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center", children: unreadCount }))] }), notificationsOpen && ((0,jsx_runtime.jsxs)("div", { className: "absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50", children: [(0,jsx_runtime.jsx)("div", { className: "px-4 py-2 border-b border-gray-200", children: (0,jsx_runtime.jsx)("h3", { className: "font-semibold text-gray-900", children: "Notific\u0103ri" }) }), (0,jsx_runtime.jsx)("div", { className: "max-h-64 overflow-y-auto", children: notifications.length > 0 ? (notifications.map((notification) => ((0,jsx_runtime.jsx)("div", { className: (0,cn.cn)('px-4 py-3 hover:bg-gray-50 cursor-pointer border-l-4', notification.read
                                                            ? 'border-transparent'
                                                            : 'border-primary-500 bg-primary-50'), children: (0,jsx_runtime.jsxs)("div", { className: "flex justify-between items-start", children: [(0,jsx_runtime.jsxs)("div", { className: "flex-1", children: [(0,jsx_runtime.jsx)("p", { className: "font-medium text-gray-900", children: notification.title }), (0,jsx_runtime.jsx)("p", { className: "text-sm text-gray-600 mt-1", children: notification.message })] }), (0,jsx_runtime.jsx)("span", { className: "text-xs text-gray-500 ml-2", children: notification.time })] }) }, notification.id)))) : ((0,jsx_runtime.jsx)("div", { className: "px-4 py-8 text-center text-gray-500", children: "Nu ai notific\u0103ri noi" })) }), (0,jsx_runtime.jsx)("div", { className: "px-4 py-2 border-t border-gray-200", children: (0,jsx_runtime.jsx)("button", { className: "text-sm text-primary-600 hover:text-primary-700", children: "Vezi toate notific\u0103rile" }) })] }))] }), (0,jsx_runtime.jsxs)("div", { className: "relative", ref: userMenuRef, children: [(0,jsx_runtime.jsxs)("button", { onClick: () => setUserMenuOpen(!userMenuOpen), className: "flex items-center space-x-2 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 max-w-xs", "aria-label": "Meniul utilizatorului", children: [(0,jsx_runtime.jsx)(UserAvatar/* default */.A, { user: user, size: "md", showBadge: true }), (0,jsx_runtime.jsx)("span", { className: "hidden md:block font-medium text-gray-900 truncate", title: user ? `${user.firstName} ${user.lastName}` : 'Utilizator', children: user ? `${user.firstName} ${user.lastName}` : 'Utilizator' }), (0,jsx_runtime.jsx)(esm/* ChevronDownIcon */.D3D, { className: "h-4 w-4 flex-shrink-0" })] }), userMenuOpen && ((0,jsx_runtime.jsxs)("div", { className: "absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50", children: [(0,jsx_runtime.jsxs)("div", { className: "px-4 py-2 border-b border-gray-200", children: [(0,jsx_runtime.jsx)("p", { className: "font-medium text-gray-900 truncate", title: user ? `${user.firstName} ${user.lastName}` : '', children: user ? `${user.firstName} ${user.lastName}` : '' }), (0,jsx_runtime.jsx)("p", { className: "text-sm text-gray-600 truncate", title: user?.email || '', children: user?.email })] }), (0,jsx_runtime.jsxs)(react_router_dom_dist/* Link */.N_, { to: "/app/profile", className: "flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100", children: [(0,jsx_runtime.jsx)(esm/* UserIcon */.nys, { className: "h-4 w-4" }), (0,jsx_runtime.jsx)("span", { children: "Profil" })] }), (0,jsx_runtime.jsxs)(react_router_dom_dist/* Link */.N_, { to: "/app/settings", className: "flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100", children: [(0,jsx_runtime.jsx)(esm/* Cog6ToothIcon */.Vy, { className: "h-4 w-4" }), (0,jsx_runtime.jsx)("span", { children: "Set\u0103ri" })] }), (0,jsx_runtime.jsx)("hr", { className: "my-2" }), (0,jsx_runtime.jsxs)("button", { onClick: handleLogout, className: "flex items-center space-x-2 px-4 py-2 text-red-700 hover:bg-red-50 w-full text-left", children: [(0,jsx_runtime.jsx)(esm/* ArrowRightOnRectangleIcon */.RzF, { className: "h-4 w-4" }), (0,jsx_runtime.jsx)("span", { children: "Deconectare" })] })] }))] })] })) : (
                        /* Guest Navigation */
                        (0,jsx_runtime.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,jsx_runtime.jsx)(react_router_dom_dist/* Link */.N_, { to: "/login", className: "text-gray-600 hover:text-gray-900 font-medium", children: "Conectare" }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Link */.N_, { to: "/register", className: "bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 font-medium transition-colors", children: "\u00CEnregistrare" })] })) })] }) }) }));
};
/* harmony default export */ const layout_Header = (Header);

// EXTERNAL MODULE: ./node_modules/@heroicons/react/24/solid/esm/index.js + 324 modules
var solid_esm = __webpack_require__(3355);
;// ./src/components/layout/Sidebar.tsx







/**
 * Componenta Sidebar pentru navigația laterală
 */
const Sidebar = ({ isOpen = false, isCollapsed = false, onClose, onCollapse, }) => {
    const { user } = (0,authStore/* useAuthStore */.nc)();
    const location = (0,react_router_dom_dist/* useLocation */.zy)();
    // Definirea elementelor de navigație bazat pe rolul utilizatorului
    const getNavigationItems = () => {
        if (user?.role === 'admin') {
            return [
                {
                    name: 'Dashboard Admin',
                    href: '/app/admin/dashboard',
                    icon: esm/* HomeIcon */.fAJ,
                    iconSolid: solid_esm/* HomeIcon */.fAJ,
                    description: 'Panou administrare',
                },
                {
                    name: 'Utilizatori',
                    href: '/app/admin/users',
                    icon: esm/* UserIcon */.nys,
                    iconSolid: solid_esm/* UserIcon */.nys,
                    description: 'Gestionează utilizatorii',
                },
                {
                    name: 'Abonamente',
                    href: '/app/admin/subscriptions',
                    icon: esm/* CreditCardIcon */.BFk,
                    iconSolid: solid_esm/* CreditCardIcon */.BFk,
                    description: 'Gestionează abonamentele',
                },
                {
                    name: 'Venituri',
                    href: '/app/admin/revenue',
                    icon: esm/* CurrencyDollarIcon */.xmO,
                    iconSolid: esm/* CurrencyDollarIcon */.xmO,
                    description: 'Analize financiare',
                },
                {
                    name: 'Statistici',
                    href: '/app/admin/stats',
                    icon: esm/* ChartBarIcon */.r95,
                    iconSolid: solid_esm/* ChartBarIcon */.r95,
                    description: 'Statistici detaliate',
                },
                {
                    name: 'Activitate',
                    href: '/app/admin/activity',
                    icon: esm/* DocumentChartBarIcon */.OEj,
                    iconSolid: esm/* DocumentChartBarIcon */.OEj,
                    description: 'Monitorizare activitate',
                },
            ];
        }
        return [
            {
                name: 'Dashboard',
                href: '/app/dashboard',
                icon: esm/* HomeIcon */.fAJ,
                iconSolid: solid_esm/* HomeIcon */.fAJ,
                description: 'Vedere generală',
            },
            {
                name: 'Cheltuieli',
                href: '/app/expenses',
                icon: esm/* CreditCardIcon */.BFk,
                iconSolid: solid_esm/* CreditCardIcon */.BFk,
                description: 'Gestionează cheltuielile',
                badge: 'Nou',
            },
            {
                name: 'Categorii',
                href: '/app/categories',
                icon: esm/* TagIcon */.gqV,
                iconSolid: solid_esm/* TagIcon */.gqV,
                description: 'Organizează categoriile',
            },
            {
                name: 'Rapoarte',
                href: '/app/reports',
                icon: esm/* ChartBarIcon */.r95,
                iconSolid: solid_esm/* ChartBarIcon */.r95,
                description: 'Analize și statistici',
            },
        ];
    };
    const navigationItems = getNavigationItems();
    const getSecondaryItems = () => {
        return [];
    };
    const secondaryItems = getSecondaryItems();
    // Verifică dacă o rută este activă
    const isActiveRoute = (href) => {
        return (location.pathname === href || (href !== '/dashboard' && location.pathname.startsWith(href)));
    };
    // Componenta pentru un element de navigație
    const NavigationItem = ({ item, isActive, }) => {
        const IconComponent = isActive ? item.iconSolid : item.icon;
        return ((0,jsx_runtime.jsxs)(react_router_dom_dist/* Link */.N_, { to: item.href, onClick: () => onClose && onClose(), className: (0,helpers.cn)('group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200', isActive
                ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900', isCollapsed ? 'justify-center' : 'justify-start'), title: isCollapsed ? item.name : '', children: [(0,jsx_runtime.jsx)(IconComponent, { className: (0,helpers.cn)('flex-shrink-0 h-5 w-5 transition-colors', isActive ? 'text-primary-600' : 'text-gray-500 group-hover:text-gray-700', isCollapsed ? '' : 'mr-3') }), !isCollapsed && ((0,jsx_runtime.jsxs)(jsx_runtime.Fragment, { children: [(0,jsx_runtime.jsx)("span", { className: "flex-1 truncate", children: item.name }), item.badge && ((0,jsx_runtime.jsx)("span", { className: "ml-2 px-2 py-0.5 text-xs bg-primary-100 text-primary-700 rounded-full", children: item.badge }))] })), isCollapsed && ((0,jsx_runtime.jsxs)("div", { className: "absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap", children: [item.name, item.description && ((0,jsx_runtime.jsx)("div", { className: "text-gray-300 text-xs mt-1", children: item.description }))] }))] }));
    };
    // Componenta pentru separatorul de secțiune
    const SectionSeparator = ({ title }) => {
        if (isCollapsed) {
            return (0,jsx_runtime.jsx)("div", { className: "my-4 border-t border-gray-200" });
        }
        return ((0,jsx_runtime.jsx)("div", { className: "my-4", children: (0,jsx_runtime.jsxs)("div", { className: "flex items-center", children: [(0,jsx_runtime.jsx)("div", { className: "flex-1 border-t border-gray-200" }), (0,jsx_runtime.jsx)("span", { className: "px-3 text-xs font-medium text-gray-500 uppercase tracking-wider", children: title }), (0,jsx_runtime.jsx)("div", { className: "flex-1 border-t border-gray-200" })] }) }));
    };
    return ((0,jsx_runtime.jsx)(jsx_runtime.Fragment, { children: (0,jsx_runtime.jsxs)("div", { className: (0,helpers.cn)('fixed left-0 z-40 flex flex-col bg-white border-r border-gray-200 transition-all duration-300 ease-in-out', 
            // Mobile and desktop styles - start below header
            'top-16 bottom-0 lg:translate-x-0', isOpen ? 'translate-x-0' : '-translate-x-full', 
            // Desktop styles
            isCollapsed ? 'lg:w-16' : 'lg:w-64', 
            // Mobile width
            'w-64'), children: [(0,jsx_runtime.jsxs)("div", { className: (0,helpers.cn)('flex items-center justify-between h-16 px-4 border-b border-gray-200 lg:hidden'), children: [(0,jsx_runtime.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,jsx_runtime.jsx)("div", { className: "w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center", children: (0,jsx_runtime.jsx)("span", { className: "text-white font-bold text-sm", children: "ET" }) }), (0,jsx_runtime.jsx)("span", { className: "font-semibold text-gray-900", children: "Menu" })] }), (0,jsx_runtime.jsx)("button", { onClick: onClose, className: "p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100", "aria-label": "\u00CEnchide meniul", children: (0,jsx_runtime.jsx)(esm/* XMarkIcon */.fKY, { className: "h-5 w-5" }) })] }), (0,jsx_runtime.jsx)("div", { className: "hidden lg:flex justify-end p-2", children: (0,jsx_runtime.jsx)("button", { onClick: onCollapse, className: "p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100", "aria-label": isCollapsed ? 'Extinde meniul' : 'Restrânge meniul', children: isCollapsed ? ((0,jsx_runtime.jsx)(esm/* ChevronRightIcon */.vKP, { className: "h-4 w-4" })) : ((0,jsx_runtime.jsx)(esm/* ChevronLeftIcon */.YJP, { className: "h-4 w-4" })) }) }), !isCollapsed && ((0,jsx_runtime.jsx)("div", { className: "p-4 border-b border-gray-200", children: (0,jsx_runtime.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,jsx_runtime.jsx)("div", { className: "w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center", children: (0,jsx_runtime.jsx)("span", { className: "text-white font-medium", children: user?.firstName?.charAt(0)?.toUpperCase() || 'U' }) }), (0,jsx_runtime.jsxs)("div", { className: "flex-1 min-w-0", children: [(0,jsx_runtime.jsx)("p", { className: "font-medium text-gray-900 truncate", children: user ? `${user.firstName} ${user.lastName}` : 'Utilizator' }), (0,jsx_runtime.jsx)("p", { className: "text-sm text-gray-600 truncate", children: user?.email || '<EMAIL>' })] })] }) })), isCollapsed && ((0,jsx_runtime.jsx)("div", { className: "hidden lg:flex justify-center p-2 border-b border-gray-200", children: (0,jsx_runtime.jsx)("div", { className: "w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center", children: (0,jsx_runtime.jsx)("span", { className: "text-white font-medium text-sm", children: user?.firstName?.charAt(0)?.toUpperCase() || 'U' }) }) })), (0,jsx_runtime.jsxs)("nav", { className: "flex-1 px-4 py-4 space-y-1 overflow-y-auto", children: [(0,jsx_runtime.jsx)("div", { className: "space-y-1", children: navigationItems.map(item => ((0,jsx_runtime.jsx)(NavigationItem, { item: item, isActive: isActiveRoute(item.href) }, item.name))) }), secondaryItems.length > 0 && ((0,jsx_runtime.jsxs)(jsx_runtime.Fragment, { children: [(0,jsx_runtime.jsx)(SectionSeparator, { title: "Admin" }), (0,jsx_runtime.jsx)("div", { className: "space-y-1", children: secondaryItems.map(item => ((0,jsx_runtime.jsx)(NavigationItem, { item: item, isActive: isActiveRoute(item.href) }, item.name))) })] }))] }), !isCollapsed && ((0,jsx_runtime.jsx)("div", { className: "p-4 border-t border-gray-200", children: (0,jsx_runtime.jsxs)("div", { className: "text-center", children: [(0,jsx_runtime.jsx)("p", { className: "text-xs text-gray-500", children: "Expense Tracker v1.0" }), (0,jsx_runtime.jsx)("p", { className: "text-xs text-gray-400 mt-1", children: "\u00A9 2025 Toate drepturile rezervate" })] }) }))] }) }));
};
/**
 * Hook pentru controlul sidebar-ului
 * @returns {Object} - Stări și funcții pentru sidebar
 */
const useSidebar = () => {
    const [isOpen, setIsOpen] = React.useState(false);
    const [isCollapsed, setIsCollapsed] = React.useState(() => {
        const saved = localStorage.getItem('sidebar-collapsed');
        return saved ? JSON.parse(saved) : false;
    });
    const toggle = () => setIsOpen(!isOpen);
    const close = () => setIsOpen(false);
    const open = () => setIsOpen(true);
    const collapse = () => {
        const newCollapsed = !isCollapsed;
        setIsCollapsed(newCollapsed);
        localStorage.setItem('sidebar-collapsed', JSON.stringify(newCollapsed));
    };
    return {
        isOpen,
        isCollapsed,
        toggle,
        close,
        open,
        collapse,
    };
};
/* harmony default export */ const layout_Sidebar = (Sidebar);

;// ./src/components/layout/Layout.tsx








/**
 * Componenta principală de layout pentru aplicație
 * Gestionează structura generală: header, sidebar, conținut principal și footer
 */
const Layout = ({ children, showSidebar = null, showFooter = true, className = '', }) => {
    const { isAuthenticated, user } = (0,authStore/* useAuthStore */.nc)();
    const location = (0,react_router_dom_dist/* useLocation */.zy)();
    const [sidebarOpen, setSidebarOpen] = (0,react.useState)(false);
    const [sidebarCollapsed, setSidebarCollapsed] = (0,react.useState)(false);
    // Determină dacă să afișeze sidebar-ul
    const shouldShowSidebar = showSidebar !== null ? showSidebar : isAuthenticated;
    // Închide sidebar-ul mobil la schimbarea rutei
    (0,react.useEffect)(() => {
        setSidebarOpen(false);
    }, [location.pathname]);
    // Gestionează redimensionarea ferestrei pentru sidebar
    (0,react.useEffect)(() => {
        const handleResize = () => {
            if (window.innerWidth >= 1024) {
                setSidebarOpen(false); // Închide overlay-ul pe desktop
            }
        };
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);
    // Salvează preferința de collapse în localStorage
    (0,react.useEffect)(() => {
        const savedCollapsed = localStorage.getItem('sidebar-collapsed');
        if (savedCollapsed !== null) {
            setSidebarCollapsed(JSON.parse(savedCollapsed));
        }
    }, []);
    const handleSidebarToggle = () => {
        setSidebarOpen(!sidebarOpen);
    };
    const handleSidebarCollapse = () => {
        const newCollapsed = !sidebarCollapsed;
        setSidebarCollapsed(newCollapsed);
        localStorage.setItem('sidebar-collapsed', JSON.stringify(newCollapsed));
    };
    const handleSidebarClose = () => {
        setSidebarOpen(false);
    };
    return ((0,jsx_runtime.jsxs)("div", { className: (0,helpers.cn)('min-h-screen bg-gray-50 flex flex-col', className), children: [(0,jsx_runtime.jsx)(layout_Header, { onSidebarToggle: handleSidebarToggle, showSidebarToggle: shouldShowSidebar, sidebarOpen: sidebarOpen }), (0,jsx_runtime.jsxs)("div", { className: "flex flex-1 relative", children: [shouldShowSidebar && ((0,jsx_runtime.jsxs)(jsx_runtime.Fragment, { children: [sidebarOpen && ((0,jsx_runtime.jsx)("div", { className: "fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden", onClick: handleSidebarClose, "aria-hidden": "true" })), (0,jsx_runtime.jsx)(layout_Sidebar, { isOpen: sidebarOpen, isCollapsed: sidebarCollapsed, onClose: handleSidebarClose, onCollapse: handleSidebarCollapse })] })), (0,jsx_runtime.jsxs)("main", { className: (0,helpers.cn)('flex-1 flex flex-col min-h-0 transition-all duration-300 ease-in-out', shouldShowSidebar && {
                            'lg:ml-64': !sidebarCollapsed,
                            'lg:ml-16': sidebarCollapsed,
                        }), children: [(0,jsx_runtime.jsx)("div", { className: "flex-1 p-4 lg:p-6 xl:p-8", children: (0,jsx_runtime.jsx)("div", { className: "max-w-7xl mx-auto", children: children || (0,jsx_runtime.jsx)(react_router_dom_dist/* Outlet */.sv, {}) }) }), showFooter && ((0,jsx_runtime.jsx)(layout_Footer, { className: (0,helpers.cn)(shouldShowSidebar && {
                                    'lg:ml-64': !sidebarCollapsed,
                                    'lg:ml-16': sidebarCollapsed,
                                }), minimal: true, showLinks: false, showContact: false }))] })] }), (0,jsx_runtime.jsx)("div", { className: "sr-only", children: (0,jsx_runtime.jsx)("a", { href: "#main-content", className: "skip-link", children: "Sari la con\u021Binutul principal" }) })] }));
};
/**
 * Layout simplu fără sidebar pentru pagini publice
 */
const PublicLayout = ({ children }) => {
    return (_jsx(Layout, { showSidebar: false, showFooter: true, className: "bg-white", children: children }));
};
/**
 * Layout pentru pagini de autentificare
 */
const AuthLayout = ({ children }) => {
    return (_jsx("div", { className: "min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4", children: _jsx("div", { className: "w-full max-w-md", children: children }) }));
};
/**
 * Layout pentru pagini de eroare
 */
const ErrorLayout = ({ children }) => {
    return (_jsx("div", { className: "min-h-screen bg-gray-50 flex items-center justify-center p-4", children: _jsx("div", { className: "text-center", children: children }) }));
};
/**
 * Layout pentru printare
 */
const PrintLayout = ({ children }) => {
    return (_jsx("div", { className: "print:block hidden", children: _jsx("div", { className: "max-w-none mx-auto p-4", children: children }) }));
};
/**
 * Hook pentru controlul layout-ului
 */
const useLayout = () => {
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {
        const saved = localStorage.getItem('sidebar-collapsed');
        return saved ? JSON.parse(saved) : false;
    });
    const toggleSidebar = () => setSidebarOpen(!sidebarOpen);
    const closeSidebar = () => setSidebarOpen(false);
    const toggleCollapse = () => {
        const newCollapsed = !sidebarCollapsed;
        setSidebarCollapsed(newCollapsed);
        localStorage.setItem('sidebar-collapsed', JSON.stringify(newCollapsed));
    };
    return {
        sidebarOpen,
        sidebarCollapsed,
        toggleSidebar,
        closeSidebar,
        toggleCollapse,
    };
};
/* harmony default export */ const layout_Layout = (Layout);

;// ./src/pages/NotFound.tsx





const NotFound = () => {
    const dashboardUrl = useRedirectUrl();
    return ((0,jsx_runtime.jsx)("div", { className: "min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8", children: (0,jsx_runtime.jsxs)("div", { className: "sm:mx-auto sm:w-full sm:max-w-md", children: [(0,jsx_runtime.jsxs)("div", { className: "text-center", children: [(0,jsx_runtime.jsx)("h1", { className: "text-9xl font-bold text-primary-600 mb-4", children: "404" }), (0,jsx_runtime.jsx)("h2", { className: "text-3xl font-bold text-gray-900 mb-4", children: "Pagina nu a fost g\u0103sit\u0103" }), (0,jsx_runtime.jsx)("p", { className: "text-lg text-gray-600 mb-8", children: "Ne pare r\u0103u, dar pagina pe care o c\u0103uta\u021Bi nu exist\u0103 sau a fost mutat\u0103." }), (0,jsx_runtime.jsxs)("div", { className: "flex flex-col sm:flex-row gap-4 justify-center", children: [(0,jsx_runtime.jsxs)(react_router_dom_dist/* Link */.N_, { to: dashboardUrl, className: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors", children: [(0,jsx_runtime.jsx)(esm/* HomeIcon */.fAJ, { className: "w-5 h-5 mr-2" }), "\u00CEnapoi la Dashboard"] }), (0,jsx_runtime.jsxs)("button", { onClick: () => window.history.back(), className: "inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors", children: [(0,jsx_runtime.jsx)(esm/* ArrowLeftIcon */.A60, { className: "w-5 h-5 mr-2" }), "\u00CEnapoi"] })] })] }), (0,jsx_runtime.jsxs)("div", { className: "mt-12 text-center", children: [(0,jsx_runtime.jsx)("div", { className: "inline-flex items-center justify-center w-32 h-32 bg-primary-100 rounded-full mb-4", children: (0,jsx_runtime.jsx)("svg", { className: "w-16 h-16 text-primary-600", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", xmlns: "http://www.w3.org/2000/svg", children: (0,jsx_runtime.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 1.5, d: "M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.291-1.007-5.691-2.709M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" }) }) }), (0,jsx_runtime.jsx)("p", { className: "text-sm text-gray-500", children: "Dac\u0103 problema persist\u0103, v\u0103 rug\u0103m s\u0103 contacta\u021Bi echipa de suport." })] })] }) }));
};
/* harmony default export */ const pages_NotFound = (NotFound);

;// ./src/App.tsx









// Lazy loading pentru optimizarea performanței
const Landing = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(911)]).then(__webpack_require__.bind(__webpack_require__, 2911)));
const Login = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(76), __webpack_require__.e(500)]).then(__webpack_require__.bind(__webpack_require__, 6500)));
const Register = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(76), __webpack_require__.e(968)]).then(__webpack_require__.bind(__webpack_require__, 2968)));
const ForgotPassword = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(76), __webpack_require__.e(353)]).then(__webpack_require__.bind(__webpack_require__, 3353)));
const ResetPassword = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(76), __webpack_require__.e(589)]).then(__webpack_require__.bind(__webpack_require__, 5589)));
const Dashboard = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(998)]).then(__webpack_require__.bind(__webpack_require__, 5998)));
const Expenses = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(671)]).then(__webpack_require__.bind(__webpack_require__, 2671)));
const Categories = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(628)]).then(__webpack_require__.bind(__webpack_require__, 6628)));
const Reports = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(560)]).then(__webpack_require__.bind(__webpack_require__, 8179)));
const Profile = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(76), __webpack_require__.e(69)]).then(__webpack_require__.bind(__webpack_require__, 9069)));
const Settings = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(179)]).then(__webpack_require__.bind(__webpack_require__, 560)));
// Pagini administrare
const AdminDashboard = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(176), __webpack_require__.e(96), __webpack_require__.e(76), __webpack_require__.e(552)]).then(__webpack_require__.bind(__webpack_require__, 4552)));
const AdminStats = (0,react.lazy)(() => __webpack_require__.e(/* import() */ 76).then(__webpack_require__.bind(__webpack_require__, 8598)));
const RevenueCharts = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(176), __webpack_require__.e(96), __webpack_require__.e(76)]).then(__webpack_require__.bind(__webpack_require__, 6392)));
const UsersList = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(76)]).then(__webpack_require__.bind(__webpack_require__, 7530)));
const SubscriptionManager = (0,react.lazy)(() => __webpack_require__.e(/* import() */ 76).then(__webpack_require__.bind(__webpack_require__, 714)));
const ActivityFeed = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(167)]).then(__webpack_require__.bind(__webpack_require__, 7167)));
// Pagini legale
const Terms = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(457)]).then(__webpack_require__.bind(__webpack_require__, 6457)));
const Privacy = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(424)]).then(__webpack_require__.bind(__webpack_require__, 6424)));
const Cookies = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(957)]).then(__webpack_require__.bind(__webpack_require__, 7957)));
// Pagini produs
const Features = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(245)]).then(__webpack_require__.bind(__webpack_require__, 6245)));
const Pricing = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(834)]).then(__webpack_require__.bind(__webpack_require__, 5834)));
// Pagini suport
const Documentation = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(192)]).then(__webpack_require__.bind(__webpack_require__, 8192)));
const Contact = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(350)]).then(__webpack_require__.bind(__webpack_require__, 5350)));
const Help = (0,react.lazy)(() => Promise.all(/* import() */[__webpack_require__.e(76), __webpack_require__.e(777)]).then(__webpack_require__.bind(__webpack_require__, 7777)));
// Componenta de loading pentru Suspense
const PageLoader = () => ((0,jsx_runtime.jsx)("div", { className: "min-h-screen flex items-center justify-center bg-gray-50", children: (0,jsx_runtime.jsxs)("div", { className: "text-center", children: [(0,jsx_runtime.jsx)(LoadingSpinner/* default */.Ay, { size: "lg" }), (0,jsx_runtime.jsx)("p", { className: "mt-4 text-gray-600", children: "Se \u00EEncarc\u0103..." })] }) }));
// Componenta principală App
const App = () => {
    const { isAuthenticated, isLoading, initializeAuth } = (0,authStore/* useAuthStore */.nc)(state => ({
        isAuthenticated: state.isAuthenticated,
        isLoading: state.isLoading,
        initializeAuth: state.initializeAuth,
    }));
    // Inițializează autentificarea la pornirea aplicației
    (0,react.useEffect)(() => {
        initializeAuth();
    }, [initializeAuth]);
    // Afișează loader în timpul verificării autentificării
    if (isLoading) {
        return (0,jsx_runtime.jsx)(PageLoader, {});
    }
    return ((0,jsx_runtime.jsx)("div", { className: "App", children: (0,jsx_runtime.jsx)(react.Suspense, { fallback: (0,jsx_runtime.jsx)(PageLoader, {}), children: (0,jsx_runtime.jsxs)(react_router_dom_dist/* Routes */.BV, { children: [(0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { allowAuthenticated: true, children: (0,jsx_runtime.jsx)(Landing, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/login", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { children: (0,jsx_runtime.jsx)(Login, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/register", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { children: (0,jsx_runtime.jsx)(Register, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/forgot-password", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { children: (0,jsx_runtime.jsx)(ForgotPassword, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/reset-password", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { children: (0,jsx_runtime.jsx)(ResetPassword, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/terms", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { allowAuthenticated: true, children: (0,jsx_runtime.jsx)(Terms, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/privacy", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { allowAuthenticated: true, children: (0,jsx_runtime.jsx)(Privacy, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/cookies", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { allowAuthenticated: true, children: (0,jsx_runtime.jsx)(Cookies, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/features", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { allowAuthenticated: true, children: (0,jsx_runtime.jsx)(Features, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/pricing", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { allowAuthenticated: true, children: (0,jsx_runtime.jsx)(Pricing, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/documentation", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { allowAuthenticated: true, children: (0,jsx_runtime.jsx)(Documentation, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/contact", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { allowAuthenticated: true, children: (0,jsx_runtime.jsx)(Contact, {}) }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "/help", element: (0,jsx_runtime.jsx)(auth_PublicRoute, { allowAuthenticated: true, children: (0,jsx_runtime.jsx)(Help, {}) }) }), (0,jsx_runtime.jsxs)(react_router_dom_dist/* Route */.qh, { path: "/app", element: (0,jsx_runtime.jsx)(auth_ProtectedRoute, { children: (0,jsx_runtime.jsx)(layout_Layout, {}) }), children: [(0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { index: true, element: (0,jsx_runtime.jsx)(react_router_dom_dist/* Navigate */.C5, { to: isAuthenticated && authStore/* useAuthStore */.nc.getState().user?.role === 'admin'
                                        ? '/app/admin/dashboard'
                                        : '/app/dashboard', replace: true }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "dashboard", element: (0,jsx_runtime.jsx)(Dashboard, {}) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "expenses", element: (0,jsx_runtime.jsx)(Expenses, {}) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "categories", element: (0,jsx_runtime.jsx)(Categories, {}) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "reports", element: (0,jsx_runtime.jsx)(Reports, {}) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "profile", element: (0,jsx_runtime.jsx)(Profile, {}) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "settings", element: (0,jsx_runtime.jsx)(Settings, {}) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "admin/*", element: (0,jsx_runtime.jsx)(auth_ProtectedRoute, { roles: ['admin'], children: (0,jsx_runtime.jsxs)(react_router_dom_dist/* Routes */.BV, { children: [(0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { index: true, element: (0,jsx_runtime.jsx)(react_router_dom_dist/* Navigate */.C5, { to: "/app/admin/dashboard", replace: true }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "dashboard", element: (0,jsx_runtime.jsx)(AdminDashboard, {}) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "stats", element: (0,jsx_runtime.jsx)(AdminStats, { dashboardStats: {
                                                        users: {
                                                            total: 0,
                                                            active: 0,
                                                            newThisMonth: 0,
                                                        },
                                                        subscriptions: {
                                                            total: 0,
                                                            active: 0,
                                                            cancelled: 0,
                                                        },
                                                        expenses: {
                                                            total: 0,
                                                            thisMonth: 0,
                                                        },
                                                        revenue: {
                                                            monthly: 0,
                                                            annual: 0,
                                                        },
                                                    } }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "revenue", element: (0,jsx_runtime.jsx)(RevenueCharts, { dashboardStats: {
                                                        users: {
                                                            total: 0,
                                                            active: 0,
                                                            newThisMonth: 0,
                                                        },
                                                        subscriptions: {
                                                            total: 0,
                                                            active: 0,
                                                            cancelled: 0,
                                                        },
                                                        expenses: {
                                                            total: 0,
                                                            thisMonth: 0,
                                                        },
                                                        revenue: {
                                                            monthly: 0,
                                                            annual: 0,
                                                        },
                                                    } }) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "users", element: (0,jsx_runtime.jsx)(UsersList, {}) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "subscriptions", element: (0,jsx_runtime.jsx)(SubscriptionManager, {}) }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "activity", element: (0,jsx_runtime.jsx)(ActivityFeed, {}) })] }) }) })] }), (0,jsx_runtime.jsx)(react_router_dom_dist/* Route */.qh, { path: "*", element: (0,jsx_runtime.jsx)(pages_NotFound, {}) })] }) }) }));
};
/* harmony default export */ const src_App = (App);

// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js
var injectStylesIntoStyleTag = __webpack_require__(5072);
var injectStylesIntoStyleTag_default = /*#__PURE__*/__webpack_require__.n(injectStylesIntoStyleTag);
// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleDomAPI.js
var styleDomAPI = __webpack_require__(7825);
var styleDomAPI_default = /*#__PURE__*/__webpack_require__.n(styleDomAPI);
// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertBySelector.js
var insertBySelector = __webpack_require__(7659);
var insertBySelector_default = /*#__PURE__*/__webpack_require__.n(insertBySelector);
// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js
var setAttributesWithoutAttributes = __webpack_require__(5056);
var setAttributesWithoutAttributes_default = /*#__PURE__*/__webpack_require__.n(setAttributesWithoutAttributes);
// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/insertStyleElement.js
var insertStyleElement = __webpack_require__(540);
var insertStyleElement_default = /*#__PURE__*/__webpack_require__.n(insertStyleElement);
// EXTERNAL MODULE: ./node_modules/style-loader/dist/runtime/styleTagTransform.js
var styleTagTransform = __webpack_require__(1113);
var styleTagTransform_default = /*#__PURE__*/__webpack_require__.n(styleTagTransform);
// EXTERNAL MODULE: ./node_modules/css-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js!./src/styles/index.css
var styles = __webpack_require__(7805);
;// ./src/styles/index.css

      
      
      
      
      
      
      
      
      

var options = {};

options.styleTagTransform = (styleTagTransform_default());
options.setAttributes = (setAttributesWithoutAttributes_default());
options.insert = insertBySelector_default().bind(null, "head");
options.domAPI = (styleDomAPI_default());
options.insertStyleElement = (insertStyleElement_default());

var update = injectStylesIntoStyleTag_default()(styles/* default */.A, options);




       /* harmony default export */ const src_styles = (styles/* default */.A && styles/* default */.A.locals ? styles/* default */.A.locals : undefined);

// EXTERNAL MODULE: ./src/i18n.ts + 2 modules
var i18n = __webpack_require__(5518);
;// ./src/main.tsx









// Import i18n configuration

// Configurare React Query
const queryClient = new modern/* QueryClient */.E1({
    defaultOptions: {
        queries: {
            staleTime: 1000 * 60 * 5, // 5 minute
            gcTime: 1000 * 60 * 10, // 10 minute
            retry: (failureCount, error) => {
                // Nu reîncerca pentru erorile de autentificare
                if (error?.response?.status === 401 || error?.response?.status === 403) {
                    return false;
                }
                // Reîncearcă maximum 3 ori pentru alte erori
                return failureCount < 3;
            },
            refetchOnWindowFocus: false,
            refetchOnMount: true,
            refetchOnReconnect: true,
        },
        mutations: {
            retry: false,
        },
    },
});
// Configurare toast notifications
const toastOptions = {
    duration: 4000,
    position: 'top-right',
    style: {
        background: '#363636',
        color: '#fff',
        fontSize: '14px',
        borderRadius: '8px',
        padding: '12px 16px',
        maxWidth: '400px',
        marginTop: '60px', // Poziționează sub header (64px + 16px margin)
    },
    success: {
        iconTheme: {
            primary: '#22c55e',
            secondary: '#fff',
        },
    },
    error: {
        iconTheme: {
            primary: '#ef4444',
            secondary: '#fff',
        },
        duration: 6000,
    },
    loading: {
        iconTheme: {
            primary: '#3b82f6',
            secondary: '#fff',
        },
    },
};
class ErrorBoundary extends react.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null, errorInfo: null };
    }
    static getDerivedStateFromError(error) {
        return { hasError: true };
    }
    componentDidCatch(error, errorInfo) {
        this.setState({
            error,
            errorInfo,
        });
        // Log error pentru debugging
        console.error('React Error Boundary:', error, errorInfo);
        // Aici poți adăuga logging către un serviciu extern (Sentry, LogRocket, etc.)
    }
    render() {
        if (this.state.hasError) {
            return ((0,jsx_runtime.jsx)("div", { className: "min-h-screen flex items-center justify-center bg-gray-50", children: (0,jsx_runtime.jsxs)("div", { className: "max-w-md w-full bg-white shadow-lg rounded-lg p-6", children: [(0,jsx_runtime.jsx)("div", { className: "flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4", children: (0,jsx_runtime.jsx)("svg", { className: "w-6 h-6 text-red-600", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: (0,jsx_runtime.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" }) }) }), (0,jsx_runtime.jsx)("h2", { className: "text-lg font-semibold text-gray-900 text-center mb-2", children: "Oops! Ceva nu a mers bine" }), (0,jsx_runtime.jsx)("p", { className: "text-gray-600 text-center mb-4", children: "A ap\u0103rut o eroare nea\u0219teptat\u0103. Te rug\u0103m s\u0103 re\u00EEmprosp\u0103tezi pagina." }), (0,jsx_runtime.jsxs)("div", { className: "flex space-x-3", children: [(0,jsx_runtime.jsx)("button", { onClick: () => window.location.reload(), className: "flex-1 bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 transition-colors", children: "Re\u00EEmprosp\u0103teaz\u0103" }), (0,jsx_runtime.jsx)("button", { onClick: () => this.setState({ hasError: false, error: null, errorInfo: null }), className: "flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors", children: "\u00CEncearc\u0103 din nou" })] }),  true && this.state.error && ((0,jsx_runtime.jsxs)("details", { className: "mt-4 p-3 bg-gray-100 rounded text-sm", children: [(0,jsx_runtime.jsx)("summary", { className: "cursor-pointer font-medium text-gray-700 mb-2", children: "Detalii eroare (development)" }), (0,jsx_runtime.jsxs)("pre", { className: "whitespace-pre-wrap text-xs text-gray-600 overflow-auto max-h-32", children: [this.state.error.toString(), this.state.errorInfo?.componentStack] })] }))] }) }));
        }
        return this.props.children;
    }
}
const AppProviders = ({ children }) => {
    return ((0,jsx_runtime.jsx)(ErrorBoundary, { children: (0,jsx_runtime.jsx)(modern/* QueryClientProvider */.Ht, { client: queryClient, children: (0,jsx_runtime.jsxs)(react_router_dom_dist/* BrowserRouter */.Kd, { future: {
                    v7_startTransition: true,
                    v7_relativeSplatPath: true,
                }, children: [children, (0,jsx_runtime.jsx)(dist/* Toaster */.l$, { toastOptions: toastOptions }),  true && ((0,jsx_runtime.jsx)(build_modern/* ReactQueryDevtools */.E, { initialIsOpen: false }))] }) }) }));
};
// Render aplicația
const rootElement = document.getElementById('root');
if (!rootElement)
    throw new Error('Root element not found');
client.createRoot(rootElement).render((0,jsx_runtime.jsx)((react_default()).StrictMode, { children: (0,jsx_runtime.jsx)(AppProviders, { children: (0,jsx_runtime.jsx)(src_App, {}) }) }));


/***/ }),

/***/ 1664:
/***/ ((module) => {

module.exports = "data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%236b7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e";

/***/ }),

/***/ 2031:
/***/ ((module) => {

module.exports = "data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e";

/***/ }),

/***/ 3569:
/***/ ((module) => {

module.exports = "data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e";

/***/ }),

/***/ 3658:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Yq: () => (/* binding */ formatDate),
/* harmony export */   cn: () => (/* binding */ cn),
/* harmony export */   vv: () => (/* binding */ formatCurrency),
/* harmony export */   wI: () => (/* binding */ formatRelativeDate)
/* harmony export */ });
/* unused harmony exports daysBetween, isPastDate, isToday, formatNumber, formatPercentage, truncateText, capitalize, kebabCase, camelCase, generateId, isValidEmail, isValidPhoneNumber, debounce, throttle, copyToClipboard, downloadFile, formatFileSize, getCategoryColor, formatPaymentMethod, isEmpty, sortBy, groupBy */
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4164);
/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(856);
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(4126);



/**
 * Combină și optimizează clasele CSS folosind clsx și tailwind-merge
 */
function cn(...inputs) {
    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__/* .twMerge */ .QP)((0,clsx__WEBPACK_IMPORTED_MODULE_0__/* .clsx */ .$)(inputs));
}
/**
 * Formatează o sumă de bani cu simbolul valuței
 */
function formatCurrency(amount, currency = 'RON', options = {}) {
    const { minimumFractionDigits = 2, maximumFractionDigits = 2, showSymbol = true, locale = 'ro-RO', } = options;
    if (typeof amount !== 'number' || isNaN(amount)) {
        return '0,00';
    }
    const formatter = new Intl.NumberFormat(locale, {
        minimumFractionDigits,
        maximumFractionDigits,
    });
    const formattedAmount = formatter.format(Math.abs(amount));
    const symbol = showSymbol
        ? _constants__WEBPACK_IMPORTED_MODULE_2__/* .CURRENCY_SYMBOLS */ .uM[currency] || currency
        : '';
    const sign = amount < 0 ? '-' : '';
    if (currency === 'RON') {
        return `${sign}${formattedAmount} ${symbol}`;
    }
    return `${sign}${symbol}${formattedAmount}`;
}
/**
 * Formatează o dată în format românesc
 */
function formatDate(date, options = {}) {
    const { format = 'short', // 'short', 'medium', 'long', 'full'
    includeTime = false, locale = 'ro-RO', } = options;
    if (!date)
        return '';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) {
        return 'Dată invalidă';
    }
    const formatOptions = {
        short: {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
        },
        medium: {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
        },
        long: {
            day: '2-digit',
            month: 'long',
            year: 'numeric',
        },
        full: {
            weekday: 'long',
            day: '2-digit',
            month: 'long',
            year: 'numeric',
        },
    };
    let dateFormatOptions = formatOptions[format] || formatOptions.short;
    if (includeTime) {
        dateFormatOptions = {
            ...dateFormatOptions,
            hour: '2-digit',
            minute: '2-digit',
        };
    }
    return new Intl.DateTimeFormat(locale, dateFormatOptions).format(dateObj);
}
/**
 * Formatează o dată relativă (acum, acum 2 ore, ieri, etc.)
 */
function formatRelativeDate(date, locale = 'ro-RO') {
    if (!date)
        return '';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) {
        return 'Dată invalidă';
    }
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInSeconds < 60) {
        return 'acum';
    }
    if (diffInMinutes < 60) {
        return `acum ${diffInMinutes} ${diffInMinutes === 1 ? 'minut' : 'minute'}`;
    }
    if (diffInHours < 24) {
        return `acum ${diffInHours} ${diffInHours === 1 ? 'oră' : 'ore'}`;
    }
    if (diffInDays === 1) {
        return 'ieri';
    }
    if (diffInDays < 7) {
        return `acum ${diffInDays} zile`;
    }
    if (diffInDays < 30) {
        const weeks = Math.floor(diffInDays / 7);
        return `acum ${weeks} ${weeks === 1 ? 'săptămână' : 'săptămâni'}`;
    }
    if (diffInDays < 365) {
        const months = Math.floor(diffInDays / 30);
        return `acum ${months} ${months === 1 ? 'lună' : 'luni'}`;
    }
    const years = Math.floor(diffInDays / 365);
    return `acum ${years} ${years === 1 ? 'an' : 'ani'}`;
}
/**
 * Calculează numărul de zile între două date
 */
function daysBetween(startDate, endDate) {
    const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
    const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
    if (!start || !end)
        return 0;
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}
/**
 * Verifică dacă o dată este în trecut
 */
function isPastDate(date) {
    if (!date)
        return false;
    const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    dateObj.setHours(0, 0, 0, 0);
    return dateObj < today;
}
/**
 * Verifică dacă o dată este astăzi
 */
function isToday(date) {
    if (!date)
        return false;
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    return (dateObj.getDate() === today.getDate() &&
        dateObj.getMonth() === today.getMonth() &&
        dateObj.getFullYear() === today.getFullYear());
}
/**
 * Formatează un număr cu separatori de mii
 */
function formatNumber(number, locale = 'ro-RO') {
    if (typeof number !== 'number' || isNaN(number)) {
        return '0';
    }
    return new Intl.NumberFormat(locale).format(number);
}
/**
 * Formatează un procent
 */
function formatPercentage(value, total, decimals = 1) {
    if (!total || total === 0)
        return '0%';
    const percentage = (value / total) * 100;
    return `${percentage.toFixed(decimals)}%`;
}
/**
 * Scurtează un text la o lungime specificată
 */
function truncateText(text, maxLength, suffix = '...') {
    if (!text || text.length <= maxLength) {
        return text || '';
    }
    return text.substring(0, maxLength - suffix.length) + suffix;
}
/**
 * Capitalizează prima literă a unui string
 */
function capitalize(str) {
    if (!str)
        return '';
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}
/**
 * Convertește un string în format kebab-case
 */
function kebabCase(str) {
    if (!str)
        return '';
    return str
        .replace(/([a-z])([A-Z])/g, '$1-$2')
        .replace(/[\s_]+/g, '-')
        .toLowerCase();
}
/**
 * Convertește un string în format camelCase
 */
function camelCase(str) {
    if (!str)
        return '';
    return str
        .replace(/[-_\s]+(.)?/g, (_, char) => (char ? char.toUpperCase() : ''))
        .replace(/^[A-Z]/, char => char.toLowerCase());
}
/**
 * Generează un ID unic
 */
function generateId(prefix = 'id') {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 8);
    return `${prefix}_${timestamp}_${randomStr}`;
}
/**
 * Validează o adresă de email
 */
function isValidEmail(email) {
    if (!email)
        return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
/**
 * Validează un număr de telefon românesc
 */
function isValidPhoneNumber(phone) {
    if (!phone)
        return false;
    const phoneRegex = /^(\+40|0040|0)[2-9]\d{8}$/;
    return phoneRegex.test(phone.replace(/[\s-]/g, ''));
}
/**
 * Debounce pentru funcții
 */
function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate)
                func(...args);
        };
        const callNow = immediate && !timeout;
        if (timeout)
            clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow)
            func(...args);
    };
}
/**
 * Throttle pentru funcții
 */
function throttle(func, limit) {
    let inThrottle;
    return function (...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => (inThrottle = false), limit);
        }
    };
}
/**
 * Copiază text în clipboard
 */
async function copyToClipboard(text) {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            return true;
        }
        else {
            // Fallback pentru browsere mai vechi
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            const result = document.execCommand('copy');
            textArea.remove();
            return result;
        }
    }
    catch (error) {
        console.error('Eroare la copierea în clipboard:', error);
        return false;
    }
}
/**
 * Descarcă un fișier
 */
function downloadFile(data, filename, type = 'text/plain') {
    const blob = data instanceof Blob ? data : new Blob([data], { type });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}
/**
 * Formatează dimensiunea unui fișier
 */
function formatFileSize(bytes, decimals = 2) {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}
/**
 * Obține culoarea pentru o categorie bazată pe numele acesteia
 */
function getCategoryColor(categoryName) {
    const colors = [
        'bg-blue-100 text-blue-800',
        'bg-green-100 text-green-800',
        'bg-yellow-100 text-yellow-800',
        'bg-red-100 text-red-800',
        'bg-purple-100 text-purple-800',
        'bg-pink-100 text-pink-800',
        'bg-indigo-100 text-indigo-800',
        'bg-gray-100 text-gray-800',
    ];
    if (!categoryName)
        return colors[0];
    // Generează un index bazat pe numele categoriei
    let hash = 0;
    for (let i = 0; i < categoryName.length; i++) {
        hash = categoryName.charCodeAt(i) + ((hash << 5) - hash);
    }
    const colorIndex = Math.abs(hash) % colors.length;
    return colors[colorIndex];
}
/**
 * Formatează metoda de plată
 */
function formatPaymentMethod(paymentMethod) {
    return (PAYMENT_METHOD_LABELS[paymentMethod] || paymentMethod);
}
/**
 * Verifică dacă un obiect este gol
 */
function isEmpty(obj) {
    if (obj == null)
        return true;
    if (Array.isArray(obj) || typeof obj === 'string')
        return obj.length === 0;
    return Object.keys(obj).length === 0;
}
/**
 * Sortează un array de obiecte după o proprietate
 */
function sortBy(array, key, direction = 'asc') {
    return [...array].sort((a, b) => {
        const aVal = a[key];
        const bVal = b[key];
        if (aVal < bVal)
            return direction === 'asc' ? -1 : 1;
        if (aVal > bVal)
            return direction === 'asc' ? 1 : -1;
        return 0;
    });
}
/**
 * Grupează un array de obiecte după o proprietate
 */
function groupBy(array, key) {
    return array.reduce((groups, item) => {
        const group = String(item[key]);
        groups[group] = groups[group] || [];
        groups[group].push(item);
        return groups;
    }, {});
}


/***/ }),

/***/ 4126:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   JR: () => (/* binding */ API_BASE_URL),
/* harmony export */   Sn: () => (/* binding */ API_ENDPOINTS),
/* harmony export */   uM: () => (/* binding */ CURRENCY_SYMBOLS)
/* harmony export */ });
/* unused harmony exports ENV, API_VERSION, PAGINATION, VALIDATION, PAYMENT_METHODS, PAYMENT_METHOD_LABELS, PAYMENT_METHOD_ICONS, RECURRING_FREQUENCIES, RECURRING_FREQUENCY_LABELS, CURRENCIES, LANGUAGES, LANGUAGE_LABELS, TIMEZONES, TIMEZONE_LABELS, CHART_TYPES, REPORT_PERIODS, REPORT_PERIOD_LABELS, SORT_ORDERS, EXPENSE_SORT_FIELDS, EXPENSE_SORT_FIELD_LABELS, TOAST_CONFIG, STORAGE_KEYS, CACHE_CONFIG, DEBOUNCE_DELAYS, ANIMATION_DURATIONS, BREAKPOINTS, FILE_CONFIG, ERROR_MESSAGES, SUCCESS_MESSAGES, EXPORT_CONFIG, NOTIFICATION_CONFIG */
// Configurări de mediu
const ENV = {
    DEVELOPMENT: 'development',
    PRODUCTION: 'production',
    TEST: 'test',
};
// URL-ul de bază pentru API
const API_BASE_URL = "http://localhost:3000/api" || 0;
// Versiunea API
const API_VERSION = 'v1';
// Endpoint-urile API
const API_ENDPOINTS = {
    // Autentificare
    AUTH: {
        REGISTER: '/auth/register',
        LOGIN: '/auth/login',
        LOGOUT: '/auth/logout',
        REFRESH: '/auth/refresh',
        PROFILE: '/auth/profile',
        CHANGE_PASSWORD: '/auth/change-password',
        FORGOT_PASSWORD: '/auth/forgot-password',
        RESET_PASSWORD: '/auth/reset-password',
        VERIFY_EMAIL: '/auth/verify-email',
        RESEND_VERIFICATION: '/auth/resend-verification',
    },
    // Categorii
    CATEGORIES: {
        BASE: '/categories',
        BY_ID: (id) => `/categories/${id}`,
        STATS: '/categories/stats',
        REORDER: '/categories/reorder',
        SET_DEFAULT: '/categories/set-default',
    },
    // Cheltuieli
    EXPENSES: {
        BASE: '/expenses',
        BY_ID: (id) => `/expenses/${id}`,
        STATS: '/expenses/stats',
        TRENDS: '/expenses/trends',
        TAGS: '/expenses/tags',
        BULK_DELETE: '/expenses/bulk-delete',
        ADD_TAG: (id) => `/expenses/${id}/tags`,
        REMOVE_TAG: (id, tag) => `/expenses/${id}/tags/${tag}`,
    },
};
// Configurări pentru paginare
const PAGINATION = {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 20,
    MAX_LIMIT: 100,
    LIMITS: [10, 20, 50, 100],
};
// Configurări pentru validare
const VALIDATION = {
    PASSWORD: {
        MIN_LENGTH: 8,
        MAX_LENGTH: 128,
        REQUIRE_UPPERCASE: true,
        REQUIRE_LOWERCASE: true,
        REQUIRE_NUMBERS: true,
        REQUIRE_SPECIAL_CHARS: true,
    },
    EMAIL: {
        MAX_LENGTH: 255,
    },
    NAME: {
        MIN_LENGTH: 2,
        MAX_LENGTH: 100,
    },
    EXPENSE: {
        MIN_AMOUNT: 0.01,
        MAX_AMOUNT: 999999.99,
        MAX_DESCRIPTION_LENGTH: 500,
        MAX_NOTES_LENGTH: 1000,
        MAX_LOCATION_LENGTH: 200,
    },
    CATEGORY: {
        MIN_NAME_LENGTH: 2,
        MAX_NAME_LENGTH: 50,
        MAX_DESCRIPTION_LENGTH: 200,
    },
};
// Tipuri de plată
const PAYMENT_METHODS = {
    CASH: 'cash',
    CARD: 'card',
    BANK_TRANSFER: 'bank_transfer',
    DIGITAL_WALLET: 'digital_wallet',
    CHECK: 'check',
    OTHER: 'other',
};
// Etichete pentru tipurile de plată
const PAYMENT_METHOD_LABELS = {
    [PAYMENT_METHODS.CASH]: 'Numerar',
    [PAYMENT_METHODS.CARD]: 'Card',
    [PAYMENT_METHODS.BANK_TRANSFER]: 'Transfer bancar',
    [PAYMENT_METHODS.DIGITAL_WALLET]: 'Portofel digital',
    [PAYMENT_METHODS.CHECK]: 'Cec',
    [PAYMENT_METHODS.OTHER]: 'Altele',
};
// Icoane pentru tipurile de plată
const PAYMENT_METHOD_ICONS = {
    [PAYMENT_METHODS.CASH]: '💵',
    [PAYMENT_METHODS.CARD]: '💳',
    [PAYMENT_METHODS.BANK_TRANSFER]: '🏦',
    [PAYMENT_METHODS.DIGITAL_WALLET]: '📱',
    [PAYMENT_METHODS.CHECK]: '📝',
    [PAYMENT_METHODS.OTHER]: '💰',
};
// Frecvențe pentru cheltuielile recurente
const RECURRING_FREQUENCIES = {
    DAILY: 'daily',
    WEEKLY: 'weekly',
    MONTHLY: 'monthly',
    QUARTERLY: 'quarterly',
    YEARLY: 'yearly',
};
// Etichete pentru frecvențe
const RECURRING_FREQUENCY_LABELS = {
    [RECURRING_FREQUENCIES.DAILY]: 'Zilnic',
    [RECURRING_FREQUENCIES.WEEKLY]: 'Săptămânal',
    [RECURRING_FREQUENCIES.MONTHLY]: 'Lunar',
    [RECURRING_FREQUENCIES.QUARTERLY]: 'Trimestrial',
    [RECURRING_FREQUENCIES.YEARLY]: 'Anual',
};
// Valute suportate
const CURRENCIES = {
    RON: 'RON',
    EUR: 'EUR',
    USD: 'USD',
    GBP: 'GBP',
};
// Simboluri pentru valute
const CURRENCY_SYMBOLS = {
    [CURRENCIES.RON]: 'lei',
    [CURRENCIES.EUR]: '€',
    [CURRENCIES.USD]: '$',
    [CURRENCIES.GBP]: '£',
};
// Limbi suportate
const LANGUAGES = {
    RO: 'ro',
    EN: 'en',
};
// Etichete pentru limbi
const LANGUAGE_LABELS = {
    [LANGUAGES.RO]: 'Română',
    [LANGUAGES.EN]: 'English',
};
// Fusuri orare
const TIMEZONES = {
    BUCHAREST: 'Europe/Bucharest',
    LONDON: 'Europe/London',
    NEW_YORK: 'America/New_York',
    LOS_ANGELES: 'America/Los_Angeles',
};
// Etichete pentru fusuri orare
const TIMEZONE_LABELS = {
    [TIMEZONES.BUCHAREST]: 'București (UTC+2)',
    [TIMEZONES.LONDON]: 'Londra (UTC+0)',
    [TIMEZONES.NEW_YORK]: 'New York (UTC-5)',
    [TIMEZONES.LOS_ANGELES]: 'Los Angeles (UTC-8)',
};
// Tipuri de grafice pentru rapoarte
const CHART_TYPES = {
    LINE: 'line',
    BAR: 'bar',
    PIE: 'pie',
    DOUGHNUT: 'doughnut',
    AREA: 'area',
};
// Perioade pentru rapoarte
const REPORT_PERIODS = {
    LAST_7_DAYS: 'last_7_days',
    LAST_30_DAYS: 'last_30_days',
    LAST_3_MONTHS: 'last_3_months',
    LAST_6_MONTHS: 'last_6_months',
    LAST_YEAR: 'last_year',
    THIS_MONTH: 'this_month',
    THIS_YEAR: 'this_year',
    CUSTOM: 'custom',
};
// Etichete pentru perioade
const REPORT_PERIOD_LABELS = {
    [REPORT_PERIODS.LAST_7_DAYS]: 'Ultimele 7 zile',
    [REPORT_PERIODS.LAST_30_DAYS]: 'Ultimele 30 zile',
    [REPORT_PERIODS.LAST_3_MONTHS]: 'Ultimele 3 luni',
    [REPORT_PERIODS.LAST_6_MONTHS]: 'Ultimele 6 luni',
    [REPORT_PERIODS.LAST_YEAR]: 'Ultimul an',
    [REPORT_PERIODS.THIS_MONTH]: 'Luna aceasta',
    [REPORT_PERIODS.THIS_YEAR]: 'Anul acesta',
    [REPORT_PERIODS.CUSTOM]: 'Perioadă personalizată',
};
// Tipuri de sortare
const SORT_ORDERS = {
    ASC: 'asc',
    DESC: 'desc',
};
// Câmpuri pentru sortarea cheltuielilor
const EXPENSE_SORT_FIELDS = {
    DATE: 'date',
    AMOUNT: 'amount',
    DESCRIPTION: 'description',
    CATEGORY: 'category',
    PAYMENT_METHOD: 'paymentMethod',
    CREATED_AT: 'createdAt',
};
// Etichete pentru câmpurile de sortare
const EXPENSE_SORT_FIELD_LABELS = {
    [EXPENSE_SORT_FIELDS.DATE]: 'Data',
    [EXPENSE_SORT_FIELDS.AMOUNT]: 'Suma',
    [EXPENSE_SORT_FIELDS.DESCRIPTION]: 'Descriere',
    [EXPENSE_SORT_FIELDS.CATEGORY]: 'Categorie',
    [EXPENSE_SORT_FIELDS.PAYMENT_METHOD]: 'Metoda de plată',
    [EXPENSE_SORT_FIELDS.CREATED_AT]: 'Data creării',
};
// Configurări pentru toast notifications
const TOAST_CONFIG = {
    DURATION: {
        SHORT: 2000,
        MEDIUM: 4000,
        LONG: 6000,
    },
    POSITION: {
        TOP_LEFT: 'top-left',
        TOP_CENTER: 'top-center',
        TOP_RIGHT: 'top-right',
        BOTTOM_LEFT: 'bottom-left',
        BOTTOM_CENTER: 'bottom-center',
        BOTTOM_RIGHT: 'bottom-right',
    },
};
// Configurări pentru localStorage
const STORAGE_KEYS = {
    AUTH_TOKEN: 'accessToken',
    REFRESH_TOKEN: 'refreshToken',
    USER_PREFERENCES: 'userPreferences',
    THEME: 'theme',
    LANGUAGE: 'language',
    CURRENCY: 'currency',
    LAST_VISITED_PAGE: 'lastVisitedPage',
};
// Configurări pentru cache
const CACHE_CONFIG = {
    STALE_TIME: {
        SHORT: 1000 * 60, // 1 minut
        MEDIUM: 1000 * 60 * 5, // 5 minute
        LONG: 1000 * 60 * 30, // 30 minute
    },
    CACHE_TIME: {
        SHORT: 1000 * 60 * 5, // 5 minute
        MEDIUM: 1000 * 60 * 10, // 10 minute
        LONG: 1000 * 60 * 60, // 1 oră
    },
};
// Configurări pentru debounce
const DEBOUNCE_DELAYS = {
    SEARCH: 300,
    INPUT: 500,
    RESIZE: 250,
    SCROLL: 100,
};
// Configurări pentru animații
const ANIMATION_DURATIONS = {
    FAST: 150,
    NORMAL: 250,
    SLOW: 350,
    VERY_SLOW: 500,
};
// Breakpoint-uri pentru responsive design
const BREAKPOINTS = {
    XS: 480,
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    '2XL': 1536,
};
// Configurări pentru fișiere
const FILE_CONFIG = {
    MAX_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_TYPES: {
        IMAGES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        DOCUMENTS: ['application/pdf', 'text/csv', 'application/vnd.ms-excel'],
    },
    ALLOWED_EXTENSIONS: {
        IMAGES: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
        DOCUMENTS: ['.pdf', '.csv', '.xls', '.xlsx'],
    },
};
// Mesaje de eroare comune
const ERROR_MESSAGES = {
    NETWORK_ERROR: 'Eroare de rețea. Verifică conexiunea la internet.',
    SERVER_ERROR: 'Eroare de server. Te rugăm să încerci din nou.',
    UNAUTHORIZED: 'Nu ești autorizat să accesezi această resursă.',
    FORBIDDEN: 'Nu ai permisiunea să efectuezi această acțiune.',
    NOT_FOUND: 'Resursa solicitată nu a fost găsită.',
    VALIDATION_ERROR: 'Datele introduse nu sunt valide.',
    TIMEOUT_ERROR: 'Cererea a expirat. Te rugăm să încerci din nou.',
};
// Mesaje de succes comune
const SUCCESS_MESSAGES = {
    CREATED: 'Creat cu succes!',
    UPDATED: 'Actualizat cu succes!',
    DELETED: 'Șters cu succes!',
    SAVED: 'Salvat cu succes!',
    SENT: 'Trimis cu succes!',
};
// Configurări pentru export/import
const EXPORT_CONFIG = {
    FORMATS: {
        CSV: 'csv',
        JSON: 'json',
        PDF: 'pdf',
    },
    MAX_RECORDS: 10000,
};
// Configurări pentru notificări
const NOTIFICATION_CONFIG = {
    TYPES: {
        INFO: 'info',
        SUCCESS: 'success',
        WARNING: 'warning',
        ERROR: 'error',
    },
    AUTO_DISMISS: {
        INFO: 4000,
        SUCCESS: 3000,
        WARNING: 5000,
        ERROR: 6000,
    },
};


/***/ }),

/***/ 5270:
/***/ ((module) => {

module.exports = "data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e";

/***/ }),

/***/ 5518:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  v2: () => (/* binding */ changeLanguage),
  UK: () => (/* binding */ getCurrentLanguage),
  qX: () => (/* binding */ getSupportedLanguages)
});

// UNUSED EXPORTS: default

// EXTERNAL MODULE: ./node_modules/i18next/dist/esm/i18next.js
var i18next = __webpack_require__(2635);
// EXTERNAL MODULE: ./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js
var i18nextBrowserLanguageDetector = __webpack_require__(4997);
// EXTERNAL MODULE: ./node_modules/react-i18next/dist/es/index.js + 15 modules
var es = __webpack_require__(2389);
;// ./src/locales/en/translation.json
const translation_namespaceObject = /*#__PURE__*/JSON.parse('{"common":{"save":"Save","cancel":"Cancel","delete":"Delete","edit":"Edit","add":"Add","close":"Close","confirm":"Confirm","loading":"Loading...","error":"Error","success":"Success","warning":"Warning","info":"Information","back":"Back"},"navigation":{"dashboard":"Dashboard","expenses":"Expenses","categories":"Categories","reports":"Reports","profile":"Profile","settings":"Settings","logout":"Logout"},"auth":{"login":"Login","register":"Register","email":"Email","password":"Password","confirmPassword":"Confirm Password","forgotPassword":"Forgot Password?","rememberMe":"Remember Me","loginSuccess":"Successfully logged in!","logoutSuccess":"You have been logged out successfully!","loginError":"Login error","registerSuccess":"Account created successfully!"},"settings":{"title":"Settings","subtitle":"Customize your app experience","general":{"title":"General Settings","currency":"Currency","language":"Language","theme":"Theme","dateFormat":"Date Format","themes":{"light":"Light","dark":"Dark","auto":"Auto"}},"notifications":{"title":"Notifications","email":{"title":"Email Notifications","description":"Receive notifications via email"},"push":{"title":"Push Notifications","description":"Receive notifications in browser"},"weeklyReport":{"title":"Weekly Reports","description":"Receive a weekly summary of your activity"},"budgetAlerts":{"title":"Budget Alerts","description":"Receive alerts when you exceed your budget"}},"privacy":{"title":"Privacy","dataSharing":{"title":"Data Sharing","description":"Allow data sharing for improvements"},"analytics":{"title":"Analytics","description":"Allow usage data collection"},"marketing":{"title":"Marketing","description":"Receive marketing communications"}},"export":{"title":"Export Data","description":"Download a copy of all your data in JSON format","button":"Export Data","allData":"Export All Data","success":"Data exported successfully!","error":"Error exporting data"},"dangerZone":{"title":"Danger Zone","logout":{"title":"Logout","description":"Sign out of your account","button":"Logout","confirmTitle":"Confirm Logout","confirmMessage":"Are you sure you want to logout?","confirmButton":"Logout"},"deleteAccount":{"title":"Delete Account","description":"Permanently delete your account and all data","button":"Delete Account","confirmTitle":"Confirm Account Deletion","warningTitle":"Warning! This action is irreversible.","warningMessage":"All your data will be permanently deleted and cannot be recovered.","confirmInstructions":"To confirm, type DELETE in the field below:","confirmPlaceholder":"Type DELETE to confirm","confirmButton":"Delete Account","success":"Account deleted successfully!","error":"Error deleting account"}},"saveButton":"Save Settings","saveSuccess":"Settings saved successfully!","saveError":"Error saving settings"},"currencies":{"RON":"RON - Romanian Leu","EUR":"EUR - Euro","USD":"USD - US Dollar","GBP":"GBP - British Pound"},"languages":{"ro":"Română","en":"English","fr":"Français","de":"Deutsch"},"landing":{"header":{"features":"Features","testimonials":"Testimonials","pricing":"Pricing","login":"Login","register":"Register","startFree":"Start Free"},"hero":{"title":{"part1":"Take Control of Your","part2":"Finances with Ease"},"subtitle":"FinanceFlow is the modern app that helps you track expenses, analyze financial trends, and achieve your savings goals.","cta":{"primary":"Start Free","secondary":"View Demo"},"startFree":"Start Free","viewDemo":"View Demo","disclaimer":"No credit card required • 2-minute setup • Cancel anytime","features":"No credit card required • 2-minute setup • Cancel anytime"},"features":{"title":"Powerful Features","subtitle":"All the tools you need to manage your finances in one application.","items":{"analytics":{"title":"Advanced Analytics","description":"Visualize spending trends with interactive charts and detailed reports."},"categories":{"title":"Category Management","description":"Organize expenses into customizable categories for better control."},"security":{"title":"Maximum Security","description":"Your data is protected with advanced encryption and secure authentication."},"mobile":{"title":"Mobile Access","description":"The app is optimized for all devices - desktop, tablet, and mobile."},"realtime":{"title":"Real Time","description":"Track expenses in real time and receive notifications for your budget."},"sharing":{"title":"Easy Sharing","description":"Share reports with family or accountant for collaborative management."}}},"stats":{"activeUsers":{"number":"10,000+","label":"Active Users"},"expensesTracked":{"number":"1M+","label":"Expenses Tracked"},"averageSavings":{"number":"25%","label":"Average Savings"},"uptime":{"number":"99.9%","label":"Uptime"}},"testimonials":{"title":"What Users Say","subtitle":"Thousands of people already manage their finances with FinanceFlow","items":{"maria":{"name":"Maria Popescu","role":"Entrepreneur","content":"FinanceFlow helped me reduce my expenses by 30% in just 3 months. The interface is intuitive and the reports are very useful."},"alexandru":{"name":"Alexandru Ionescu","role":"IT Manager","content":"The best financial tracking app I\'ve ever used. Data security and advanced features are impressive."},"elena":{"name":"Elena Dumitrescu","role":"Freelancer","content":"Perfect for freelancers! I can track all business expenses and generate reports for accounting."}}},"pricing":{"title":"Simple and Transparent Plans","subtitle":"Choose the plan that fits your needs","popular":"Most popular","perMonth":"per month","plans":{"free":{"name":"Free","period":"per month","features":{"transactions":"Up to 50 transactions/month","categories":"3 custom categories","reports":"Basic reports","support":"Email support"}},"pro":{"name":"Pro","period":"per month","description":"Most popular","features":{"transactions":"Unlimited transactions","categories":"Unlimited categories","reports":"Advanced reports","export":"Excel/PDF export","support":"Priority support","backup":"Automatic backup"}},"business":{"name":"Business","period":"per month","description":"For teams and companies","features":{"allPro":"All Pro features","multipleAccounts":"Multiple accounts","apiAccess":"API access","integrations":"Advanced integrations","manager":"Dedicated manager","sla":"99.9% SLA"}}},"currency":"USD","buttons":{"free":"Start Free","choose":"Choose Plan"},"choosePlan":"Choose Plan"},"cta":{"title":"Ready to Transform Your Finances?","subtitle":"Join thousands of users who have improved their financial situation with FinanceFlow.","button":"Start Free Now","features":"2-minute setup • No obligations • Cancel anytime","disclaimer":"2-minute setup • No obligations • Cancel anytime"},"footer":{"description":"The modern app for personal finance management.","product":{"title":"Product","features":"Features","pricing":"Pricing","api":"API","integrations":"Integrations"},"support":{"title":"Support","documentation":"Documentation","guides":"Guides","contact":"Contact","status":"Status"},"legal":{"title":"Legal","terms":"Terms","privacy":"Privacy","cookies":"Cookies","gdpr":"GDPR"},"copyright":"© 2024 FinanceFlow. All rights reserved."}},"legal":{"terms":{"title":"Terms and Conditions","subtitle":"Terms and conditions for using the FinanceFlow service","lastUpdated":"Last updated: December 15, 2024","sections":{"acceptance":{"title":"1. Acceptance of Terms","content":"By accessing and using the FinanceFlow service, you agree to be bound by these terms and conditions. If you do not agree with any of these terms, please do not use our service."},"service":{"title":"2. Service Description","content":"FinanceFlow is a personal finance management application that allows you to track expenses, analyze financial trends, and manage your budget. The service is provided \'as is\' and may be modified or discontinued at any time."},"account":{"title":"3. User Account","content":"To use certain features of the service, you must create an account. You are responsible for maintaining the confidentiality of your password and for all activities that occur under your account."},"data":{"title":"4. User Data","content":"You retain rights to the data you enter into the service. By using the service, you grant us a limited license to process this data for the purpose of providing the service."},"prohibited":{"title":"5. Prohibited Uses","content":"You may not use the service for illegal activities, to violate the rights of others, or to compromise system security. We reserve the right to suspend or close accounts that violate these terms."},"liability":{"title":"6. Limitation of Liability","content":"FinanceFlow will not be liable for direct, indirect, incidental, or consequential damages resulting from the use or inability to use the service."},"changes":{"title":"7. Changes to Terms","content":"We reserve the right to modify these terms at any time. Changes will be communicated via email or in-app notification at least 30 days before taking effect."},"termination":{"title":"8. Service Termination","content":"You may terminate use of the service at any time by deleting your account. We reserve the right to suspend or close accounts that violate these terms."},"contact":{"title":"9. Contact","content":"For questions about these terms, you can contact <NAME_EMAIL> or through the contact form in the application."}}},"privacy":{"title":"Privacy Policy","subtitle":"How we collect, use, and protect your personal information","lastUpdated":"Last updated: December 15, 2024","sections":{"introduction":{"title":"1. Introduction","content":"This privacy policy describes how FinanceFlow collects, uses, and protects your personal information when you use our service."},"collection":{"title":"2. Information We Collect","content":"We collect information you provide directly to us (such as name, email, and financial data), information about service usage, and technical information about your device."},"usage":{"title":"3. How We Use Information","content":"We use information to provide and improve the service, to contact you regarding your account, and to comply with legal obligations."},"sharing":{"title":"4. Information Sharing","content":"We do not sell, rent, or share your personal information with third parties, except as provided in this policy or when we have your consent."},"security":{"title":"5. Data Security","content":"We implement technical and organizational security measures to protect your information against unauthorized access, modification, disclosure, or destruction."},"retention":{"title":"6. Data Retention","content":"We retain your personal information only as long as necessary to fulfill the purposes for which it was collected or as required by legal requirements."},"rights":{"title":"7. Your Rights","content":"You have the right to access, correct, delete, or restrict the processing of your personal information. You also have the right to data portability."},"cookies":{"title":"8. Cookies and Similar Technologies","content":"We use cookies and similar technologies to improve your experience, analyze service usage, and personalize content."},"contact":{"title":"9. Contact","content":"For questions about this privacy policy, you can contact <NAME_EMAIL>."}}},"cookies":{"title":"Cookie Policy","subtitle":"How we use cookies and similar technologies","lastUpdated":"Last updated: December 15, 2024","manage":{"title":"Manage Cookie Preferences","description":"You can control what types of cookies you accept:","necessary":{"title":"Necessary Cookies","description":"Essential for website functionality","required":"(Required)"},"functional":{"title":"Functional Cookies","description":"Enhance user experience"},"analytics":{"title":"Analytics Cookies","description":"Help us understand how you use the site"},"marketing":{"title":"Marketing Cookies","description":"Used for personalized advertising"},"savePreferences":"Save Preferences","acceptAll":"Accept All","rejectAll":"Reject All"},"sections":{"what":{"title":"1. What are Cookies?","content":"Cookies are small text files that are stored on your device when you visit a website. They help us provide you with a better experience and understand how you use our service."},"how":{"title":"2. How We Use Cookies","content":"We use cookies to authenticate you, remember your preferences, analyze site traffic, and improve service functionality."},"types":{"title":"3. Types of Cookies","content":"We use necessary cookies (for basic functionality), functional cookies (for experience enhancement), analytics cookies (for statistics), and marketing cookies (for relevant advertising)."},"thirdParty":{"title":"4. Third-Party Cookies","content":"Some cookies are set by third-party services we use, such as Google Analytics for analysis and payment services for transaction processing."},"control":{"title":"5. Cookie Control","content":"You can control and delete cookies through your browser settings. You can also use our preferences panel to manage the types of cookies you accept."},"contact":{"title":"6. Contact","content":"For questions about cookie usage, you can contact <NAME_EMAIL>."}}}},"product":{"features":{"title":"Features","subtitle":"Discover all the powerful features of FinanceFlow","hero":{"title":"Complete Features for Financial Management","subtitle":"From expense tracking to advanced analytics, FinanceFlow provides all the tools you need to control your finances."},"sections":{"analytics":{"title":"Advanced Analytics","description":"Get detailed insights into your spending with interactive charts and customizable reports.","features":["Interactive charts and visualizations","Customizable reports","Trend analysis","Period comparisons","Export in multiple formats"]},"transactions":{"title":"Transaction Management","description":"Add, edit, and organize transactions with ease using our intuitive interface.","features":["Quick transaction entry","Customizable categories","Tags and notes","Advanced search and filtering","CSV file import"]},"notifications":{"title":"Smart Notifications","description":"Stay on top of your spending with personalized notifications and budget alerts.","features":["Budget alerts","Large expense notifications","Weekly reports","Bill reminders","Push and email notifications"]},"security":{"title":"Security and Privacy","description":"Your data is protected with the highest industry security standards.","features":["End-to-end encryption","Two-factor authentication","Automatic backup","GDPR compliance","Regular security audits"]},"budgeting":{"title":"Smart Budgeting","description":"Create and manage budgets for different categories and track progress in real-time.","features":["Category budgets","Real-time tracking","Overspend alerts","Recurring budgets","Performance analysis"]},"reports":{"title":"Detailed Reports","description":"Generate comprehensive reports to better understand your financial habits.","features":["Monthly and annual reports","Category analysis","Previous period comparisons","PDF and Excel export","Custom reports"]},"collaboration":{"title":"Family Collaboration","description":"Share budgets and expenses with family for collaborative financial management.","features":["Family accounts","Granular permissions","Shared budgets","Member notifications","Consolidated reports"]},"sync":{"title":"Cloud Sync","description":"Access your data from any device with automatic cloud synchronization.","features":["Real-time sync","Multi-device access","Automatic backup","Version history","Offline functionality"]},"mobile":{"title":"Mobile App","description":"Manage finances on the go with our optimized mobile application.","features":["Responsive design","Quick expense entry","Push notifications","Home screen widget","Offline functionality"]}},"integration":{"title":"Powerful Integrations","subtitle":"Connect FinanceFlow with your favorite services","description":"Integrate with banks, payment services, and accounting applications for a complete experience."},"cta":{"title":"Ready to Get Started?","subtitle":"Try all these features free for 30 days.","button":"Start Free Trial"}},"pricing":{"title":"Pricing","subtitle":"Simple and transparent plans for all needs","hero":{"title":"Choose the Perfect Plan for You","subtitle":"From individual users to large teams, we have the right plan for every need."},"billing":{"monthly":"Monthly","annually":"Annually","save":"Save 20%"},"plans":{"free":{"name":"Free","description":"Perfect for beginners","price":"0","period":"per month","features":["Up to 50 transactions/month","3 custom categories","Basic reports","Email support","Mobile app"],"button":"Start Free"},"personal":{"name":"Personal","description":"For active users","price":"29","priceAnnual":"24","period":"per month","popular":"Most popular","features":["Unlimited transactions","Unlimited categories","Advanced reports","Budgets and goals","Excel/PDF export","Priority support","Automatic backup"],"button":"Choose Personal"},"family":{"name":"Family","description":"For families and couples","price":"49","priceAnnual":"39","period":"per month","features":["All Personal features","Up to 5 members","Shared budgets","Granular permissions","Consolidated reports","In-app chat","Family manager"],"button":"Choose Family"},"business":{"name":"Business","description":"For teams and companies","price":"99","priceAnnual":"79","period":"per month","features":["All Family features","Unlimited members","API access","Advanced integrations","SSO and SAML","Dedicated manager","99.9% SLA"],"button":"Contact Sales"}},"comparison":{"title":"Detailed Comparison","features":{"transactions":"Transactions","categories":"Categories","reports":"Reports","budgets":"Budgets","export":"Data Export","support":"Support","backup":"Backup","members":"Members","api":"API Access","sso":"SSO/SAML","manager":"Dedicated Manager","sla":"SLA"},"values":{"limited":"Limited","unlimited":"Unlimited","basic":"Basic","advanced":"Advanced","email":"Email","priority":"Priority","dedicated":"Dedicated","yes":"Yes","no":"No"}},"faq":{"title":"Frequently Asked Questions","items":{"trial":{"question":"Is there a free trial?","answer":"Yes, we offer a 30-day free trial for all paid plans. No credit card required."},"cancel":{"question":"Can I cancel my subscription anytime?","answer":"Absolutely! You can cancel your subscription anytime from your account settings. No penalties or cancellation fees."},"upgrade":{"question":"Can I change my plan anytime?","answer":"Yes, you can upgrade or downgrade your plan anytime. Changes apply immediately and billing is adjusted proportionally."},"data":{"question":"What happens to my data if I cancel?","answer":"Your data remains available for 90 days after cancellation. You can export all data before permanent deletion."},"security":{"question":"How secure is my data?","answer":"We use bank-level encryption and comply with all industry security standards. Data is stored in certified data centers."},"support":{"question":"What type of support do you offer?","answer":"We offer email support for all plans, priority support for paid plans, and dedicated manager for Business plan."}}},"cta":{"title":"Ready to Get Started?","subtitle":"Join thousands of users managing their finances with FinanceFlow.","button":"Start Free Trial"}}},"support":{"documentation":{"title":"Documentation","subtitle":"Complete guides and technical documentation for FinanceFlow","search":{"placeholder":"Search documentation...","button":"Search"},"categories":{"all":"All","getting_started":"Getting Started","features":"Features","api":"API","security":"Security","faq":"FAQ"},"sections":{"getting_started":{"title":"Getting Started","description":"Everything you need to know to get started with FinanceFlow","articles":{"setup":{"title":"Account Setup","description":"Step-by-step guide to setting up your first account","readTime":"5 min"},"first_transaction":{"title":"Adding Your First Transaction","description":"Learn to add and manage transactions","readTime":"3 min"},"categories":{"title":"Organizing with Categories","description":"How to create and use categories effectively","readTime":"7 min"}}},"features":{"title":"Features","description":"Detailed guides for all features","articles":{"budgets":{"title":"Budget Management","description":"Create and monitor effective budgets","readTime":"10 min"},"reports":{"title":"Report Generation","description":"How to generate useful and customized reports","readTime":"8 min"},"notifications":{"title":"Notification Setup","description":"Set up personalized alerts and notifications","readTime":"5 min"}}},"api":{"title":"API Documentation","description":"Integrate FinanceFlow into your applications","articles":{"authentication":{"title":"Authentication","description":"How to authenticate and manage tokens","readTime":"15 min"},"endpoints":{"title":"Available Endpoints","description":"Complete list of API endpoints","readTime":"20 min"},"examples":{"title":"Code Examples","description":"Practical examples in different languages","readTime":"25 min"}}},"security":{"title":"Security","description":"Information about data security and privacy","articles":{"data_protection":{"title":"Data Protection","description":"How we protect your data and personal information","readTime":"12 min"},"two_factor":{"title":"Two-Factor Authentication","description":"Setting up and using 2FA","readTime":"6 min"},"best_practices":{"title":"Best Practices","description":"Recommendations for account security","readTime":"8 min"}}},"faq":{"title":"Frequently Asked Questions","description":"Answers to the most common questions","articles":{"general":{"title":"General Questions","description":"Frequently asked questions about the service","readTime":"10 min"},"billing":{"title":"Billing and Payments","description":"Questions about plans and billing","readTime":"8 min"},"technical":{"title":"Technical Issues","description":"Solutions for common technical problems","readTime":"15 min"}}}},"quickStart":{"title":"Quick Start","subtitle":"Start using FinanceFlow in just a few steps","steps":["Create your free account","Add your first transaction","Set up categories","Create your first budget"],"button":"Start Now"},"help":{"title":"Need Help?","subtitle":"Can\'t find what you\'re looking for? Our team is here to help.","contact":"Contact Support","chat":"Live Chat"}},"contact":{"title":"Contact","subtitle":"We\'re here to help! Choose the contact method that works best for you."},"help":{"title":"Help Center","subtitle":"Find answers to your questions and learn to use FinanceFlow to its fullest."}},"contact":{"categories":{"general":"General Question","technical":"Technical Issue","billing":"Billing","feature":"Feature Request"},"priority":{"low":"Low","medium":"Medium","high":"High"},"methods":{"title":"Contact Methods","chat":{"title":"Live Chat","description":"Immediate response for urgent questions","availability":"Monday - Friday, 9:00 AM - 6:00 PM","action":"Start Chat"},"email":{"title":"Email","description":"For detailed questions and documentation","availability":"Response within 24 hours"},"phone":{"title":"Phone","description":"Phone support for Premium customers","availability":"Monday - Friday, 10:00 AM - 5:00 PM"}},"office":{"title":"Our Office","address":{"title":"Address","line1":"123 Example Street","line2":"Sector 1, Bucharest","line3":"Romania, 010101"},"hours":{"title":"Hours","weekdays":"Monday - Friday: 9:00 AM - 6:00 PM","weekend":"Saturday - Sunday: Closed"}},"form":{"title":"Send us a Message","name":"Full Name","name_placeholder":"Enter your name","email":"Email","email_placeholder":"<EMAIL>","subject":"Subject","subject_placeholder":"Briefly describe the issue","category":"Category","priority":"Priority","message":"Message","message_placeholder":"Describe your issue or question in detail...","required":"Required fields","send":"Send Message","sending":"Sending...","success":"Message sent successfully! We\'ll get back to you soon.","error":"An error occurred. Please try again."},"faq":{"title":"Frequently Asked Questions","subtitle":"You might find the answer here before contacting us","response_time":{"question":"How long does it take to get a response?","answer":"We typically respond within 24 hours for email and immediately for live chat during business hours."},"technical_support":{"question":"Do you provide technical support?","answer":"Yes, our technical team is available to help you with any technical issues."},"billing_support":{"question":"Can I get help with billing?","answer":"Absolutely! Our billing team can help you with any account and payment related questions."},"feature_request":{"question":"How can I request a new feature?","answer":"Use the contact form and select \\"Feature Request\\" as the category. We appreciate feedback!"}}},"help":{"categories":{"all":"All","getting_started":"Getting Started","features":"Features","settings":"Settings","security":"Security","billing":"Billing","mobile":"Mobile App"},"search":{"placeholder":"Search help center..."},"sections":{"quick_start":{"title":"Quick Start","description":"Guides to get started quickly with FinanceFlow","items":{"setup":"Account Setup","setup_desc":"How to set up your account in 5 minutes","first_transaction":"First Transaction","first_transaction_desc":"Add your first transaction","categories":"Organizing Categories","categories_desc":"Create and manage categories"}},"features":{"title":"Features","description":"Explore all available features","items":{"budgets":"Budget Management","budgets_desc":"Create and monitor budgets","reports":"Reports and Analytics","reports_desc":"Generate detailed reports","goals":"Financial Goals","goals_desc":"Set and track goals","notifications":"Smart Notifications","notifications_desc":"Configure personalized alerts"}},"troubleshooting":{"title":"Troubleshooting","description":"Solutions for common problems","items":{"sync":"Sync Issues","sync_desc":"Resolve synchronization problems","login":"Login Issues","login_desc":"Can\'t log into your account?","performance":"App Performance","performance_desc":"App running slowly?"}},"advanced":{"title":"Advanced Features","description":"For experienced users","items":{"api":"API Integration","api_desc":"Connect external applications","automation":"Automation","automation_desc":"Set up automatic rules","export":"Data Export","export_desc":"Export data in various formats"}}},"popular":{"title":"Popular Articles","views":"views","article1":{"title":"How to set up my first budget?"},"article2":{"title":"Connecting bank accounts"},"article3":{"title":"Financial data security"},"article4":{"title":"Using the mobile app"},"article5":{"title":"Managing subscriptions"}},"quick_actions":{"title":"Quick Actions","contact":"Contact Support","contact_desc":"Get personalized help","docs":"API Documentation","docs_desc":"For developers","video_tour":"Video Tour","video_tour_desc":"General overview"},"cta":{"title":"Didn\'t find what you were looking for?","description":"Our support team is ready to help you with any question or issue.","contact":"Contact Support","chat":"Live Chat"}}}');
;// ./src/locales/ro/translation.json
const ro_translation_namespaceObject = /*#__PURE__*/JSON.parse('{"common":{"save":"Salvează","cancel":"Anulează","delete":"Șterge","edit":"Editează","add":"Adaugă","close":"Închide","confirm":"Confirmă","loading":"Se încarcă...","error":"Eroare","success":"Succes","warning":"Atenție","info":"Informație","back":"Înapoi"},"navigation":{"dashboard":"Tablou de bord","expenses":"Cheltuieli","categories":"Categorii","reports":"Rapoarte","profile":"Profil","settings":"Setări","logout":"Deconectare"},"auth":{"login":"Autentificare","logout":"Deconectare","register":"Înregistrare","email":"Email","password":"Parolă","confirmPassword":"Confirmă parola","forgotPassword":"Ai uitat parola?","loginButton":"Conectează-te","registerButton":"Înregistrează-te","loginError":"Eroare la autentificare","registerError":"Eroare la înregistrare","logoutSuccess":"Te-ai deconectat cu succes!"},"settings":{"title":"Setări","subtitle":"Personalizează experiența aplicației","general":{"title":"Setări generale","currency":"Moneda","language":"Limba","theme":"Tema","dateFormat":"Format dată","themes":{"light":"Luminos","dark":"Întunecat","auto":"Automat"}},"notifications":{"title":"Notificări","email":{"title":"Notificări email","description":"Primește notificări prin email"},"push":{"title":"Notificări push","description":"Primește notificări în browser"},"weeklyReport":{"title":"Rapoarte săptămânale","description":"Primește un raport săptămânal cu activitatea ta"},"budgetAlerts":{"title":"Alerte buget","description":"Primește alerte când depășești bugetul"}},"privacy":{"title":"Confidențialitate","dataSharing":{"title":"Partajare date","description":"Permite partajarea datelor pentru îmbunătățiri"},"analytics":{"title":"Analytics","description":"Permite colectarea datelor de utilizare"},"marketing":{"title":"Marketing","description":"Primește comunicări de marketing"}},"export":{"title":"Export date","description":"Descarcă o copie a tuturor datelor tale într-un format JSON","button":"Exportă datele","allData":"Exportă toate datele","success":"Datele au fost exportate cu succes!","error":"Eroare la exportul datelor"},"dangerZone":{"title":"Zonă periculoasă","logout":{"title":"Deconectare","description":"Deconectează-te din cont","button":"Deconectare","confirmTitle":"Confirmare deconectare","confirmMessage":"Ești sigur că vrei să te deconectezi?","confirmButton":"Deconectează-te"},"deleteAccount":{"title":"Șterge contul","description":"Șterge permanent contul și toate datele","button":"Șterge contul","confirmTitle":"Confirmare ștergere cont","warningTitle":"Atenție! Această acțiune este ireversibilă.","warningMessage":"Toate datele tale vor fi șterse permanent și nu vor putea fi recuperate.","confirmInstructions":"Pentru a confirma, scrie ȘTERGE în câmpul de mai jos:","confirmPlaceholder":"Scrie ȘTERGE pentru a confirma","confirmButton":"Șterge contul","success":"Contul a fost șters cu succes!","error":"Eroare la ștergerea contului"}},"saveButton":"Salvează setările","saveSuccess":"Setările au fost salvate cu succes!","saveError":"Eroare la salvarea setărilor"},"currencies":{"RON":"RON - Leu românesc","EUR":"EUR - Euro","USD":"USD - Dolar american","GBP":"GBP - Liră sterlină"},"languages":{"ro":"Română","en":"English","fr":"Français","de":"Deutsch"},"landing":{"header":{"features":"Funcționalități","testimonials":"Testimoniale","pricing":"Prețuri","login":"Conectare","register":"Înregistrare","startFree":"Începe Gratuit"},"hero":{"title":{"part1":"Controlează-ți","part2":"Finanțele cu Ușurință"},"subtitle":"FinanceFlow este aplicația modernă care te ajută să urmărești cheltuielile, să analizezi tendințele financiare și să îți atingi obiectivele de economisire.","cta":{"primary":"Începe Gratuit","secondary":"Vezi Demo"},"startFree":"Începe Gratuit","viewDemo":"Vezi Demo","disclaimer":"Fără card de credit • Configurare în 2 minute • Anulare oricând","features":"Fără card de credit • Configurare în 2 minute • Anulare oricând"},"features":{"title":"Funcționalități Puternice","subtitle":"Toate instrumentele de care ai nevoie pentru a-ți gestiona finanțele într-o singură aplicație.","items":{"analytics":{"title":"Analiză Avansată","description":"Vizualizează tendințele cheltuielilor cu grafice interactive și rapoarte detaliate."},"categories":{"title":"Gestionare Categorii","description":"Organizează cheltuielile pe categorii personalizabile pentru un control mai bun."},"security":{"title":"Securitate Maximă","description":"Datele tale sunt protejate cu criptare avansată și autentificare securizată."},"mobile":{"title":"Acces Mobil","description":"Aplicația este optimizată pentru toate dispozitivele - desktop, tabletă și mobil."},"realtime":{"title":"Timp Real","description":"Urmărește cheltuielile în timp real și primește notificări pentru bugetul tău."},"sharing":{"title":"Partajare Facilă","description":"Partajează rapoartele cu familia sau contabilul pentru o gestionare colaborativă."}}},"stats":{"activeUsers":{"number":"10,000+","label":"Utilizatori Activi"},"expensesTracked":{"number":"1M+","label":"Cheltuieli Urmărite"},"averageSavings":{"number":"25%","label":"Economii Medii"},"uptime":{"number":"99.9%","label":"Uptime"}},"testimonials":{"title":"Ce Spun Utilizatorii","subtitle":"Mii de oameni își gestionează deja finanțele cu FinanceFlow","items":{"maria":{"name":"Maria Popescu","role":"Antreprenor","content":"FinanceFlow m-a ajutat să îmi reduc cheltuielile cu 30% în doar 3 luni. Interfața este intuitivă și rapoartele sunt foarte utile."},"alexandru":{"name":"Alexandru Ionescu","role":"Manager IT","content":"Cea mai bună aplicație de tracking financiar pe care am folosit-o. Securitatea datelor și funcționalitățile avansate sunt impresionante."},"elena":{"name":"Elena Dumitrescu","role":"Freelancer","content":"Perfect pentru freelanceri! Pot urmări toate cheltuielile de business și să generez rapoarte pentru contabilitate."}}},"pricing":{"title":"Planuri Simple și Transparente","subtitle":"Alege planul care se potrivește nevoilor tale","popular":"Cel mai popular","perMonth":"pe lună","plans":{"free":{"name":"Gratuit","period":"pe lună","features":{"transactions":"Până la 50 de tranzacții/lună","categories":"3 categorii personalizate","reports":"Rapoarte de bază","support":"Suport email"}},"pro":{"name":"Pro","period":"pe lună","description":"Cel mai popular","features":{"transactions":"Tranzacții nelimitate","categories":"Categorii nelimitate","reports":"Rapoarte avansate","export":"Export în Excel/PDF","support":"Suport prioritar","backup":"Backup automat"}},"business":{"name":"Business","period":"pe lună","description":"Pentru echipe și companii","features":{"allPro":"Toate funcționalitățile Pro","multipleAccounts":"Conturi multiple","apiAccess":"API access","integrations":"Integrări avansate","manager":"Manager dedicat","sla":"SLA 99.9%"}}},"currency":"RON","buttons":{"free":"Începe Gratuit","choose":"Alege Planul"},"choosePlan":"Alege Planul"},"cta":{"title":"Gata să Îți Transformi Finanțele?","subtitle":"Alătură-te miilor de utilizatori care și-au îmbunătățit situația financiară cu FinanceFlow.","button":"Începe Gratuit Acum","features":"Configurare în 2 minute • Fără obligații • Anulare oricând","disclaimer":"Configurare în 2 minute • Fără obligații • Anulare oricând"},"footer":{"description":"Aplicația modernă pentru gestionarea finanțelor personale.","product":{"title":"Produs","features":"Funcționalități","pricing":"Prețuri","api":"API","integrations":"Integrări"},"support":{"title":"Suport","documentation":"Documentație","guides":"Ghiduri","contact":"Contact","status":"Status"},"legal":{"title":"Legal","terms":"Termeni","privacy":"Confidențialitate","cookies":"Cookies","gdpr":"GDPR"},"copyright":"© 2024 FinanceFlow. Toate drepturile rezervate."}},"legal":{"terms":{"title":"Termeni și Condiții","subtitle":"Termenii și condițiile de utilizare a serviciului FinanceFlow","lastUpdated":"Ultima actualizare: 15 decembrie 2024","sections":{"acceptance":{"title":"1. Acceptarea Termenilor","content":"Prin accesarea și utilizarea serviciului FinanceFlow, acceptați să fiți legați de acești termeni și condiții. Dacă nu sunteți de acord cu oricare dintre acești termeni, vă rugăm să nu utilizați serviciul nostru."},"service":{"title":"2. Descrierea Serviciului","content":"FinanceFlow este o aplicație de gestionare a finanțelor personale care vă permite să urmăriți cheltuielile, să analizați tendințele financiare și să vă gestionați bugetul. Serviciul este furnizat \'ca atare\' și poate fi modificat sau întrerupt în orice moment."},"account":{"title":"3. Contul Utilizatorului","content":"Pentru a utiliza anumite funcționalități ale serviciului, trebuie să vă creați un cont. Sunteți responsabil pentru menținerea confidențialității parolei și pentru toate activitățile care au loc sub contul dumneavoastră."},"data":{"title":"4. Datele Utilizatorului","content":"Vă păstrați drepturile asupra datelor pe care le introduceți în serviciu. Prin utilizarea serviciului, ne acordați o licență limitată pentru a procesa aceste date în scopul furnizării serviciului."},"prohibited":{"title":"5. Utilizări Interzise","content":"Nu puteți utiliza serviciul pentru activități ilegale, pentru a încălca drepturile altora sau pentru a compromite securitatea sistemului. Ne rezervăm dreptul de a suspenda sau închide conturile care încalcă acești termeni."},"liability":{"title":"6. Limitarea Răspunderii","content":"FinanceFlow nu va fi responsabil pentru daune directe, indirecte, incidentale sau consecvente rezultate din utilizarea sau incapacitatea de utilizare a serviciului."},"changes":{"title":"7. Modificări ale Termenilor","content":"Ne rezervăm dreptul de a modifica acești termeni în orice moment. Modificările vor fi comunicate prin email sau prin notificare în aplicație cu cel puțin 30 de zile înainte de intrarea în vigoare."},"termination":{"title":"8. Încetarea Serviciului","content":"Puteți înceta utilizarea serviciului în orice moment prin ștergerea contului. Ne rezervăm dreptul de a suspenda sau închide conturile care încalcă acești termeni."},"contact":{"title":"9. Contact","content":"Pentru întrebări despre acești termeni, ne puteți <NAME_EMAIL> sau prin formularul de contact din aplicație."}}},"privacy":{"title":"Politica de Confidențialitate","subtitle":"Cum colectăm, utilizăm și protejăm informațiile dumneavoastră personale","lastUpdated":"Ultima actualizare: 15 decembrie 2024","sections":{"introduction":{"title":"1. Introducere","content":"Această politică de confidențialitate descrie cum FinanceFlow colectează, utilizează și protejează informațiile dumneavoastră personale când utilizați serviciul nostru."},"collection":{"title":"2. Informațiile pe care le Colectăm","content":"Colectăm informații pe care ni le furnizați direct (cum ar fi numele, emailul și datele financiare), informații despre utilizarea serviciului și informații tehnice despre dispozitivul dumneavoastră."},"usage":{"title":"3. Cum Utilizăm Informațiile","content":"Utilizăm informațiile pentru a furniza și îmbunătăți serviciul, pentru a vă contacta cu privire la contul dumneavoastră și pentru a respecta obligațiile legale."},"sharing":{"title":"4. Partajarea Informațiilor","content":"Nu vindem, nu închiriem și nu partajăm informațiile dumneavoastră personale cu terțe părți, cu excepția cazurilor prevăzute în această politică sau când avem consimțământul dumneavoastră."},"security":{"title":"5. Securitatea Datelor","content":"Implementăm măsuri de securitate tehnice și organizatorice pentru a proteja informațiile dumneavoastră împotriva accesului neautorizat, modificării, divulgării sau distrugerii."},"retention":{"title":"6. Păstrarea Datelor","content":"Păstrăm informațiile dumneavoastră personale doar atât timp cât este necesar pentru îndeplinirea scopurilor pentru care au fost colectate sau conform cerințelor legale."},"rights":{"title":"7. Drepturile Dumneavoastră","content":"Aveți dreptul să accesați, să corectați, să ștergeți sau să restricționați procesarea informațiilor dumneavoastră personale. De asemenea, aveți dreptul la portabilitatea datelor."},"cookies":{"title":"8. Cookies și Tehnologii Similare","content":"Utilizăm cookies și tehnologii similare pentru a îmbunătăți experiența dumneavoastră, pentru a analiza utilizarea serviciului și pentru a personaliza conținutul."},"contact":{"title":"9. Contact","content":"Pentru întrebări despre această politică de confidențialitate, ne puteți <NAME_EMAIL>."}}},"cookies":{"title":"Politica Cookies","subtitle":"Cum utilizăm cookies-urile și tehnologiile similare","lastUpdated":"Ultima actualizare: 15 decembrie 2024","manage":{"title":"Gestionează Preferințele Cookies","description":"Puteți controla ce tipuri de cookies acceptați:","necessary":{"title":"Cookies Necesare","description":"Esențiale pentru funcționarea site-ului","required":"(Obligatorii)"},"functional":{"title":"Cookies Funcționale","description":"Îmbunătățesc experiența utilizatorului"},"analytics":{"title":"Cookies de Analiză","description":"Ne ajută să înțelegem cum utilizați site-ul"},"marketing":{"title":"Cookies de Marketing","description":"Utilizate pentru publicitate personalizată"},"savePreferences":"Salvează Preferințele","acceptAll":"Acceptă Toate","rejectAll":"Respinge Toate"},"sections":{"what":{"title":"1. Ce sunt Cookies-urile?","content":"Cookies-urile sunt fișiere mici de text care sunt stocate pe dispozitivul dumneavoastră când vizitați un site web. Ele ne ajută să vă oferim o experiență mai bună și să înțelegem cum utilizați serviciul nostru."},"how":{"title":"2. Cum Utilizăm Cookies-urile","content":"Utilizăm cookies-urile pentru a vă autentifica, a vă reține preferințele, a analiza traficul pe site și a îmbunătăți funcționalitatea serviciului."},"types":{"title":"3. Tipuri de Cookies","content":"Utilizăm cookies necesare (pentru funcționarea de bază), cookies funcționale (pentru îmbunătățirea experienței), cookies de analiză (pentru statistici) și cookies de marketing (pentru publicitate relevantă)."},"thirdParty":{"title":"4. Cookies de la Terțe Părți","content":"Unele cookies sunt setate de servicii terțe pe care le utilizăm, cum ar fi Google Analytics pentru analiză și servicii de plată pentru procesarea tranzacțiilor."},"control":{"title":"5. Controlul Cookies-urilor","content":"Puteți controla și șterge cookies-urile prin setările browserului dumneavoastră. De asemenea, puteți utiliza panoul nostru de preferințe pentru a gestiona tipurile de cookies pe care le acceptați."},"contact":{"title":"6. Contact","content":"Pentru întrebări despre utilizarea cookies-urilor, ne puteți <NAME_EMAIL>."}}}},"product":{"features":{"title":"Funcționalități","subtitle":"Descoperă toate funcționalitățile puternice ale FinanceFlow","hero":{"title":"Funcționalități Complete pentru Gestionarea Finanțelor","subtitle":"De la urmărirea cheltuielilor la analize avansate, FinanceFlow oferă toate instrumentele de care ai nevoie pentru a-ți controla finanțele."},"sections":{"analytics":{"title":"Analiză Avansată","description":"Obține perspective detaliate asupra cheltuielilor tale cu grafice interactive și rapoarte personalizabile.","features":["Grafice interactive și vizualizări","Rapoarte personalizabile","Analiză de tendințe","Comparații pe perioade","Export în multiple formate"]},"transactions":{"title":"Gestionarea Tranzacțiilor","description":"Adaugă, editează și organizează tranzacțiile cu ușurință folosind interfața noastră intuitivă.","features":["Adăugare rapidă de tranzacții","Categorii personalizabile","Etichete și note","Căutare și filtrare avansată","Import din fișiere CSV"]},"notifications":{"title":"Notificări Inteligente","description":"Rămâi la curent cu cheltuielile tale prin notificări personalizate și alerte de buget.","features":["Alerte de buget","Notificări de cheltuieli mari","Rapoarte săptămânale","Reminder-uri pentru facturi","Notificări push și email"]},"security":{"title":"Securitate și Confidențialitate","description":"Datele tale sunt protejate cu cele mai înalte standarde de securitate din industrie.","features":["Criptare end-to-end","Autentificare cu doi factori","Backup automat","Conformitate GDPR","Audit de securitate regulat"]},"budgeting":{"title":"Bugetare Inteligentă","description":"Creează și gestionează bugete pentru diferite categorii și urmărește progresul în timp real.","features":["Bugete pe categorii","Urmărire în timp real","Alerte de depășire","Bugete recurente","Analiză de performanță"]},"reports":{"title":"Rapoarte Detaliate","description":"Generează rapoarte comprehensive pentru a înțelege mai bine obiceiurile tale financiare.","features":["Rapoarte lunare și anuale","Analiză pe categorii","Comparații cu perioadele anterioare","Export în PDF și Excel","Rapoarte personalizate"]},"collaboration":{"title":"Colaborare Familială","description":"Partajează bugete și cheltuieli cu familia pentru o gestionare financiară colaborativă.","features":["Conturi familiale","Permisiuni granulare","Bugete partajate","Notificări pentru membri","Rapoarte consolidate"]},"sync":{"title":"Sincronizare Cloud","description":"Accesează datele tale de pe orice dispozitiv cu sincronizare automată în cloud.","features":["Sincronizare în timp real","Acces multi-dispozitiv","Backup automat","Istoric versiuni","Funcționare offline"]},"mobile":{"title":"Aplicație Mobilă","description":"Gestionează finanțele în mișcare cu aplicația noastră mobilă optimizată.","features":["Design responsive","Adăugare rapidă de cheltuieli","Notificări push","Widget pentru ecranul principal","Funcționare offline"]}},"integration":{"title":"Integrări Puternice","subtitle":"Conectează FinanceFlow cu serviciile tale preferate","description":"Integrează cu băncile, serviciile de plată și aplicațiile de contabilitate pentru o experiență completă."},"cta":{"title":"Gata să Începi?","subtitle":"Încearcă toate aceste funcționalități gratuit timp de 30 de zile.","button":"Începe Perioada de Probă Gratuită"}},"pricing":{"title":"Prețuri","subtitle":"Planuri simple și transparente pentru toate nevoile","hero":{"title":"Alege Planul Perfect pentru Tine","subtitle":"De la utilizatori individuali la echipe mari, avem planul potrivit pentru fiecare nevoie."},"billing":{"monthly":"Lunar","annually":"Anual","save":"Economisești 20%"},"plans":{"free":{"name":"Gratuit","description":"Perfect pentru începători","price":"0","period":"pe lună","features":["Până la 50 de tranzacții/lună","3 categorii personalizate","Rapoarte de bază","Suport prin email","Aplicație mobilă"],"button":"Începe Gratuit"},"personal":{"name":"Personal","description":"Pentru utilizatori activi","price":"29","priceAnnual":"24","period":"pe lună","popular":"Cel mai popular","features":["Tranzacții nelimitate","Categorii nelimitate","Rapoarte avansate","Bugete și obiective","Export în Excel/PDF","Suport prioritar","Backup automat"],"button":"Alege Personal"},"family":{"name":"Familie","description":"Pentru familii și cupluri","price":"49","priceAnnual":"39","period":"pe lună","features":["Toate funcționalitățile Personal","Până la 5 membri","Bugete partajate","Permisiuni granulare","Rapoarte consolidate","Chat în aplicație","Manager de familie"],"button":"Alege Familie"},"business":{"name":"Business","description":"Pentru echipe și companii","price":"99","priceAnnual":"79","period":"pe lună","features":["Toate funcționalitățile Familie","Membri nelimitați","API access","Integrări avansate","SSO și SAML","Manager dedicat","SLA 99.9%"],"button":"Contactează Vânzările"}},"comparison":{"title":"Comparație Detaliată","features":{"transactions":"Tranzacții","categories":"Categorii","reports":"Rapoarte","budgets":"Bugete","export":"Export Date","support":"Suport","backup":"Backup","members":"Membri","api":"API Access","sso":"SSO/SAML","manager":"Manager Dedicat","sla":"SLA"},"values":{"limited":"Limitat","unlimited":"Nelimitat","basic":"De bază","advanced":"Avansat","email":"Email","priority":"Prioritar","dedicated":"Dedicat","yes":"Da","no":"Nu"}},"faq":{"title":"Întrebări Frecvente","items":{"trial":{"question":"Există o perioadă de probă gratuită?","answer":"Da, oferim o perioadă de probă gratuită de 30 de zile pentru toate planurile plătite. Nu este necesar card de credit."},"cancel":{"question":"Pot anula abonamentul oricând?","answer":"Absolut! Poți anula abonamentul oricând din setările contului. Nu există penalități sau taxe de anulare."},"upgrade":{"question":"Pot schimba planul în orice moment?","answer":"Da, poți face upgrade sau downgrade la planul tău oricând. Modificările se aplică imediat și facturarea se ajustează proporțional."},"data":{"question":"Ce se întâmplă cu datele mele dacă anulez?","answer":"Datele tale rămân disponibile timp de 90 de zile după anulare. Poți exporta toate datele înainte de ștergerea definitivă."},"security":{"question":"Cât de sigure sunt datele mele?","answer":"Utilizăm criptare de nivel bancar și respectăm toate standardele de securitate din industrie. Datele sunt stocate în centre de date certificate."},"support":{"question":"Ce tip de suport oferiti?","answer":"Oferim suport prin email pentru toate planurile, suport prioritar pentru planurile plătite și manager dedicat pentru planul Business."}}},"cta":{"title":"Gata să Începi?","subtitle":"Alătură-te miilor de utilizatori care își gestionează finanțele cu FinanceFlow.","button":"Începe Perioada de Probă Gratuită"}}},"support":{"documentation":{"title":"Documentație","subtitle":"Ghiduri complete și documentație tehnică pentru FinanceFlow","search":{"placeholder":"Căutați în documentație...","button":"Căutare"},"categories":{"all":"Toate","getting_started":"Primii Pași","features":"Funcționalități","api":"API","security":"Securitate","faq":"FAQ"},"sections":{"getting_started":{"title":"Primii Pași","description":"Tot ce trebuie să știi pentru a începe cu FinanceFlow","articles":{"setup":{"title":"Configurarea Contului","description":"Ghid pas cu pas pentru configurarea primului tău cont","readTime":"5 min"},"first_transaction":{"title":"Adăugarea Primei Tranzacții","description":"Învață să adaugi și să gestionezi tranzacțiile","readTime":"3 min"},"categories":{"title":"Organizarea cu Categorii","description":"Cum să creezi și să folosești categoriile eficient","readTime":"7 min"}}},"features":{"title":"Funcționalități","description":"Ghiduri detaliate pentru toate funcționalitățile","articles":{"budgets":{"title":"Gestionarea Bugetelor","description":"Creează și monitorizează bugete eficiente","readTime":"10 min"},"reports":{"title":"Generarea Rapoartelor","description":"Cum să generezi rapoarte utile și personalizate","readTime":"8 min"},"notifications":{"title":"Configurarea Notificărilor","description":"Setează alerte și notificări personalizate","readTime":"5 min"}}},"api":{"title":"Documentația API","description":"Integrează FinanceFlow în aplicațiile tale","articles":{"authentication":{"title":"Autentificare","description":"Cum să te autentifici și să gestionezi token-urile","readTime":"15 min"},"endpoints":{"title":"Endpoint-uri Disponibile","description":"Lista completă a endpoint-urilor API","readTime":"20 min"},"examples":{"title":"Exemple de Cod","description":"Exemple practice în diferite limbaje","readTime":"25 min"}}},"security":{"title":"Securitate","description":"Informații despre securitatea și confidențialitatea datelor","articles":{"data_protection":{"title":"Protecția Datelor","description":"Cum îți protejăm datele și informațiile personale","readTime":"12 min"},"two_factor":{"title":"Autentificare cu Doi Factori","description":"Configurarea și utilizarea 2FA","readTime":"6 min"},"best_practices":{"title":"Cele Mai Bune Practici","description":"Recomandări pentru securitatea contului","readTime":"8 min"}}},"faq":{"title":"Întrebări Frecvente","description":"Răspunsuri la cele mai comune întrebări","articles":{"general":{"title":"Întrebări Generale","description":"Întrebări frecvente despre serviciu","readTime":"10 min"},"billing":{"title":"Facturare și Plăți","description":"Întrebări despre planuri și facturare","readTime":"8 min"},"technical":{"title":"Probleme Tehnice","description":"Soluții pentru problemele tehnice comune","readTime":"15 min"}}}},"quickStart":{"title":"Start Rapid","subtitle":"Începe să folosești FinanceFlow în doar câțiva pași","steps":["Creează-ți contul gratuit","Adaugă prima tranzacție","Configurează categoriile","Setează primul buget"],"button":"Începe Acum"},"help":{"title":"Ai Nevoie de Ajutor?","subtitle":"Nu găsești ceea ce cauți? Echipa noastră este aici să te ajute.","contact":"Contactează Suportul","chat":"Chat Live"}},"contact":{"title":"Contact","subtitle":"Suntem aici să vă ajutăm! Alegeți metoda de contact care vi se potrivește cel mai bine."},"help":{"title":"Centrul de Ajutor","subtitle":"Găsiți răspunsuri la întrebările dvs. și învățați să folosiți FinanceFlow la maximum."}},"contact":{"categories":{"general":"Întrebare Generală","technical":"Problemă Tehnică","billing":"Facturare","feature":"Cerere Funcționalitate"},"priority":{"low":"Scăzută","medium":"Medie","high":"Înaltă"},"methods":{"title":"Metode de Contact","chat":{"title":"Chat Live","description":"Răspuns imediat pentru întrebări urgente","availability":"Luni - Vineri, 9:00 - 18:00","action":"Începeți Chat"},"email":{"title":"Email","description":"Pentru întrebări detaliate și documentație","availability":"Răspuns în 24 ore"},"phone":{"title":"Telefon","description":"Suport telefonic pentru clienții Premium","availability":"Luni - Vineri, 10:00 - 17:00"}},"office":{"title":"Biroul Nostru","address":{"title":"Adresa","line1":"Strada Exemplu nr. 123","line2":"Sector 1, București","line3":"România, 010101"},"hours":{"title":"Program","weekdays":"Luni - Vineri: 9:00 - 18:00","weekend":"Sâmbătă - Duminică: Închis"}},"form":{"title":"Trimiteți-ne un Mesaj","name":"Nume Complet","name_placeholder":"Introduceți numele dvs.","email":"Email","email_placeholder":"<EMAIL>","subject":"Subiect","subject_placeholder":"Descrieți pe scurt problema","category":"Categorie","priority":"Prioritate","message":"Mesaj","message_placeholder":"Descrieți detaliat problema sau întrebarea dvs...","required":"Câmpuri obligatorii","send":"Trimite Mesajul","sending":"Se trimite...","success":"Mesajul a fost trimis cu succes! Vă vom răspunde în curând.","error":"A apărut o eroare. Vă rugăm să încercați din nou."},"faq":{"title":"Întrebări Frecvente","subtitle":"Poate găsiți răspunsul aici înainte de a ne contacta","response_time":{"question":"Cât durează să primiți un răspuns?","answer":"De obicei răspundem în 24 de ore pentru email și imediat pentru chat live în timpul programului."},"technical_support":{"question":"Oferiti suport tehnic?","answer":"Da, echipa noastră tehnică este disponibilă pentru a vă ajuta cu orice problemă tehnică."},"billing_support":{"question":"Pot primi ajutor cu facturarea?","answer":"Absolut! Echipa noastră de facturare vă poate ajuta cu orice întrebări legate de cont și plăți."},"feature_request":{"question":"Cum pot cere o funcționalitate nouă?","answer":"Folosiți formularul de contact și selectați \\"Cerere Funcționalitate\\" ca categorie. Apreciem feedback-ul!"}}},"help":{"categories":{"all":"Toate","getting_started":"Primii Pași","features":"Funcționalități","settings":"Setări","security":"Securitate","billing":"Facturare","mobile":"Aplicația Mobilă"},"search":{"placeholder":"Căutați în centrul de ajutor..."},"sections":{"quick_start":{"title":"Start Rapid","description":"Ghiduri pentru a începe rapid cu FinanceFlow","items":{"setup":"Configurarea Contului","setup_desc":"Cum să vă configurați contul în 5 minute","first_transaction":"Prima Tranzacție","first_transaction_desc":"Adăugați prima dvs. tranzacție","categories":"Organizarea Categoriilor","categories_desc":"Creați și gestionați categoriile"}},"features":{"title":"Funcționalități","description":"Explorați toate funcționalitățile disponibile","items":{"budgets":"Gestionarea Bugetelor","budgets_desc":"Creați și monitorizați bugete","reports":"Rapoarte și Analize","reports_desc":"Generați rapoarte detaliate","goals":"Obiective Financiare","goals_desc":"Setați și urmăriți obiective","notifications":"Notificări Inteligente","notifications_desc":"Configurați alertele personalizate"}},"troubleshooting":{"title":"Rezolvarea Problemelor","description":"Soluții pentru problemele comune","items":{"sync":"Probleme de Sincronizare","sync_desc":"Rezolvați problemele de sincronizare","login":"Probleme de Autentificare","login_desc":"Nu vă puteți conecta la cont?","performance":"Performanță Aplicație","performance_desc":"Aplicația funcționează lent?"}},"advanced":{"title":"Funcții Avansate","description":"Pentru utilizatorii experimentați","items":{"api":"Integrare API","api_desc":"Conectați aplicații externe","automation":"Automatizări","automation_desc":"Configurați reguli automate","export":"Export Date","export_desc":"Exportați datele în diverse formate"}}},"popular":{"title":"Articole Populare","views":"vizualizări","article1":{"title":"Cum să configurez primul meu buget?"},"article2":{"title":"Conectarea conturilor bancare"},"article3":{"title":"Securitatea datelor financiare"},"article4":{"title":"Utilizarea aplicației mobile"},"article5":{"title":"Gestionarea abonamentelor"}},"quick_actions":{"title":"Acțiuni Rapide","contact":"Contactați Suportul","contact_desc":"Obțineți ajutor personalizat","docs":"Documentația API","docs_desc":"Pentru dezvoltatori","video_tour":"Tur Video","video_tour_desc":"Prezentare generală"},"cta":{"title":"Nu ați găsit ceea ce căutați?","description":"Echipa noastră de suport este gata să vă ajute cu orice întrebare sau problemă.","contact":"Contactați Suportul","chat":"Chat Live"}}}');
;// ./src/i18n.ts



// Import traducerile


// Configurarea resurselor de traducere
const resources = {
    ro: {
        translation: ro_translation_namespaceObject,
    },
    en: {
        translation: translation_namespaceObject,
    },
};
// Configurarea i18next
i18next/* default.use */.Ay.use(i18nextBrowserLanguageDetector/* default */.A)
    // Conectează cu React
    .use(es/* initReactI18next */.r9)
    // Inițializează i18next
    .init({
    resources,
    // Limba implicită
    fallbackLng: 'ro',
    // Limba implicită pentru dezvoltare
    lng: 'ro',
    // Configurări pentru detectarea limbii
    detection: {
        // Ordinea de detectare a limbii
        order: ['localStorage', 'navigator', 'htmlTag'],
        // Cache limba selectată în localStorage
        caches: ['localStorage'],
        // Cheia pentru localStorage
        lookupLocalStorage: 'i18nextLng',
    },
    // Configurări de debug (doar în dezvoltare)
    debug: "development" === 'development',
    // Configurări de interpolation
    interpolation: {
        // React face deja escape la valori
        escapeValue: false,
    },
    // Configurări pentru React
    react: {
        // Folosește Suspense pentru loading
        useSuspense: false,
    },
});
/* harmony default export */ const src_i18n = ((/* unused pure expression or super */ null && (i18n)));
// Export hook-uri utile
const changeLanguage = (lng) => {
    return i18next/* default.changeLanguage */.Ay.changeLanguage(lng);
};
const getCurrentLanguage = () => {
    return i18next/* default */.Ay.language;
};
const getSupportedLanguages = () => {
    return Object.keys(resources);
};


/***/ }),

/***/ 5649:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3740);
/* harmony import */ var _heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3355);
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9050);





const UserAvatar = ({ user, size = 'md', showBadge = true, className = '', }) => {
    const sizeClasses = {
        sm: 'w-6 h-6',
        md: 'w-8 h-8',
        lg: 'w-10 h-10',
    };
    const iconSizeClasses = {
        sm: 'h-3 w-3',
        md: 'h-4 w-4',
        lg: 'h-6 w-6',
    };
    const badgeSizeClasses = {
        sm: 'w-3 h-3',
        md: 'w-4 h-4',
        lg: 'w-5 h-5',
    };
    const badgePositionClasses = {
        sm: '-bottom-0.5 -right-0.5',
        md: '-bottom-1 -right-1',
        lg: '-bottom-1 -right-1',
    };
    const getPlanBadge = () => {
        const planName = user?.subscription?.plan?.name?.toLowerCase();
        switch (planName) {
            case 'premium':
                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('absolute rounded-full bg-gradient-to-r from-yellow-400 to-yellow-600 flex items-center justify-center shadow-sm border-2 border-white', badgeSizeClasses[size], badgePositionClasses[size]), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("svg", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('text-white', {
                            'h-2 w-2': size === 'sm',
                            'h-2.5 w-2.5': size === 'md',
                            'h-3 w-3': size === 'lg',
                        }), fill: "currentColor", viewBox: "0 0 24 24", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("path", { d: "M5 16L3 6l5.5 4L12 4l3.5 6L21 6l-2 10H5z" }) }) }));
            case 'basic':
                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('absolute rounded-full bg-gradient-to-r from-blue-400 to-blue-600 flex items-center justify-center shadow-sm border-2 border-white', badgeSizeClasses[size], badgePositionClasses[size]), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__/* .StarIcon */ .Gg5, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('text-white', {
                            'h-2 w-2': size === 'sm',
                            'h-2.5 w-2.5': size === 'md',
                            'h-3 w-3': size === 'lg',
                        }) }) }));
            case 'free':
                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('absolute rounded-full bg-gradient-to-r from-gray-400 to-gray-600 flex items-center justify-center shadow-sm border-2 border-white', badgeSizeClasses[size], badgePositionClasses[size]), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("svg", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('text-white', {
                            'h-2 w-2': size === 'sm',
                            'h-2.5 w-2.5': size === 'md',
                            'h-3 w-3': size === 'lg',
                        }), fill: "currentColor", viewBox: "0 0 24 24", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" }) }) }));
            default:
                return null;
        }
    };
    const getInitials = () => {
        if (!user?.firstName)
            return 'U';
        return `${user.firstName?.[0] ?? ''}${user.lastName?.[0] ?? ''}`.toUpperCase();
    };
    // Dacă user este null, afișăm un avatar implicit
    if (!user) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('relative inline-block', className), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('rounded-full bg-gray-400 flex items-center justify-center', sizeClasses[size]), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__/* .UserIcon */ .nys, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('text-white', iconSizeClasses[size]) }) }) }));
    }
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('relative inline-block', className), children: [user?.avatar ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("img", { src: user.avatar, alt: `${user.firstName} ${user.lastName}` || 'Avatar utilizator', className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('rounded-full object-cover', sizeClasses[size]) })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('rounded-full bg-primary-600 flex items-center justify-center', sizeClasses[size]), children: user?.firstName ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('text-white font-medium', {
                        'text-xs': size === 'sm',
                        'text-sm': size === 'md',
                        'text-base': size === 'lg',
                    }), children: getInitials() })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__/* .UserIcon */ .nys, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('text-white', iconSizeClasses[size]) })) })), showBadge && getPlanBadge()] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserAvatar);


/***/ }),

/***/ 7805:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1601);
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6314);
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(4417);
/* harmony import */ var _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2__);
// Imports



var ___CSS_LOADER_URL_IMPORT_0___ = new URL(/* asset import */ __webpack_require__(1664), __webpack_require__.b);
var ___CSS_LOADER_URL_IMPORT_1___ = new URL(/* asset import */ __webpack_require__(2031), __webpack_require__.b);
var ___CSS_LOADER_URL_IMPORT_2___ = new URL(/* asset import */ __webpack_require__(5270), __webpack_require__.b);
var ___CSS_LOADER_URL_IMPORT_3___ = new URL(/* asset import */ __webpack_require__(3569), __webpack_require__.b);
var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
___CSS_LOADER_EXPORT___.push([module.id, "@import url(https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap);"]);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_0___);
var ___CSS_LOADER_URL_REPLACEMENT_1___ = _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_1___);
var ___CSS_LOADER_URL_REPLACEMENT_2___ = _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_2___);
var ___CSS_LOADER_URL_REPLACEMENT_3___ = _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_3___);
// Module
___CSS_LOADER_EXPORT___.push([module.id, `*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured \`sans\` font-family by default.
5. Use the user's configured \`sans\` font-feature-settings by default.
6. Use the user's configured \`sans\` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */ /* 3 */
  tab-size: 4; /* 3 */
  font-family: Inter, system-ui, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from \`html\` so users can set them as a class directly on the \`html\` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured \`mono\` font-family by default.
2. Use the user's configured \`mono\` font-feature-settings by default.
3. Use the user's configured \`mono\` font-variation-settings by default.
4. Correct the odd \`em\` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: JetBrains Mono, Fira Code, monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent \`sub\` and \`sup\` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional \`:invalid\` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to \`inherit\` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements \`display: block\` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add \`vertical-align: middle\` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: inline-flex;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_0___});
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple],[size]:where(select:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_1___});
}

@media (forced-colors: active)  {

  [type='checkbox']:checked {
    -webkit-appearance: auto;
            appearance: auto;
  }
}

[type='radio']:checked {
  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_2___});
}

@media (forced-colors: active)  {

  [type='radio']:checked {
    -webkit-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_3___});
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {

  [type='checkbox']:indeterminate {
    -webkit-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}
  html {
  scroll-behavior: smooth;
}

  body {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  font-family: Inter, system-ui, sans-serif;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
}

  /* Stiluri pentru scrollbar */
  ::-webkit-scrollbar {
  width: 0.5rem;
}

  ::-webkit-scrollbar-track {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

  ::-webkit-scrollbar-thumb {
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

  ::-webkit-scrollbar-thumb:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

  /* Stiluri pentru focus */
  :focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

  :focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
}

  /* Stiluri pentru selecție text */
  ::selection {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

  /* Stiluri pentru input-uri */
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -webkit-appearance: textfield;
            appearance: textfield;
  }
.container {
  width: 100%;
}
@media (min-width: 475px) {

  .container {
    max-width: 475px;
  }
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
@media (min-width: 1600px) {

  .container {
    max-width: 1600px;
  }
}
.prose {
  color: var(--tw-prose-body);
  max-width: 65ch;
}
.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}
.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}
.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}
.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  -webkit-padding-start: 1.625em;
          padding-inline-start: 1.625em;
}
.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
}
.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  -webkit-padding-start: 1.625em;
          padding-inline-start: 1.625em;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}
.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}
.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}
.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\\201C""\\201D""\\2018""\\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  -webkit-padding-start: 1em;
          padding-inline-start: 1em;
}
.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: open-quote;
}
.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: close-quote;
}
.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}
.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 900;
  color: inherit;
}
.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}
.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}
.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}
.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}
.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  -webkit-padding-end: 0.375em;
          padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  -webkit-padding-start: 0.375em;
          padding-inline-start: 0.375em;
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "\`";
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: "\`";
}
.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}
.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}
.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  -webkit-padding-end: 1.1428571em;
          padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  -webkit-padding-start: 1.1428571em;
          padding-inline-start: 1.1428571em;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: none;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: none;
}
.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}
.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}
.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  -webkit-padding-end: 0.5714286em;
          padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  -webkit-padding-start: 0.5714286em;
          padding-inline-start: 0.5714286em;
}
.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}
.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 0;
}
.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: baseline;
}
.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}
.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: top;
}
.prose :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-align: start;
}
.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}
.prose {
  --tw-prose-body: #374151;
  --tw-prose-headings: #111827;
  --tw-prose-lead: #4b5563;
  --tw-prose-links: #111827;
  --tw-prose-bold: #111827;
  --tw-prose-counters: #6b7280;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #e5e7eb;
  --tw-prose-quotes: #111827;
  --tw-prose-quote-borders: #e5e7eb;
  --tw-prose-captions: #6b7280;
  --tw-prose-kbd: #111827;
  --tw-prose-kbd-shadows: 17 24 39;
  --tw-prose-code: #111827;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
  --tw-prose-invert-body: #d1d5db;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #9ca3af;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #9ca3af;
  --tw-prose-invert-bullets: #4b5563;
  --tw-prose-invert-hr: #374151;
  --tw-prose-invert-quotes: #f3f4f6;
  --tw-prose-invert-quote-borders: #374151;
  --tw-prose-invert-captions: #9ca3af;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d1d5db;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #4b5563;
  --tw-prose-invert-td-borders: #374151;
  font-size: 1rem;
  line-height: 1.75;
}
.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-start: 0.375em;
          padding-inline-start: 0.375em;
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-start: 0.375em;
          padding-inline-start: 0.375em;
}
.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}
.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}
.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}
.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}
.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  -webkit-padding-start: 1.625em;
          padding-inline-start: 1.625em;
}
.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
}
.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-end: 0;
          padding-inline-end: 0;
}
.prose :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  -webkit-padding-end: 0.5714286em;
          padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  -webkit-padding-start: 0.5714286em;
          padding-inline-start: 0.5714286em;
}
.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
}
.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-end: 0;
          padding-inline-end: 0;
}
.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}
.prose-lg {
  font-size: 1.125rem;
  line-height: 1.7777778;
}
.prose-lg :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
}
.prose-lg :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2222222em;
  line-height: 1.4545455;
  margin-top: 1.0909091em;
  margin-bottom: 1.0909091em;
}
.prose-lg :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.6666667em;
  margin-bottom: 1.6666667em;
  -webkit-padding-start: 1em;
          padding-inline-start: 1em;
}
.prose-lg :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 2.6666667em;
  margin-top: 0;
  margin-bottom: 0.8333333em;
  line-height: 1;
}
.prose-lg :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.6666667em;
  margin-top: 1.8666667em;
  margin-bottom: 1.0666667em;
  line-height: 1.3333333;
}
.prose-lg :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.3333333em;
  margin-top: 1.6666667em;
  margin-bottom: 0.6666667em;
  line-height: 1.5;
}
.prose-lg :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 0.4444444em;
  line-height: 1.5555556;
}
.prose-lg :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose-lg :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  border-radius: 0.3125rem;
  padding-top: 0.2222222em;
  -webkit-padding-end: 0.4444444em;
          padding-inline-end: 0.4444444em;
  padding-bottom: 0.2222222em;
  -webkit-padding-start: 0.4444444em;
          padding-inline-start: 0.4444444em;
}
.prose-lg :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
}
.prose-lg :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8666667em;
}
.prose-lg :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.875em;
}
.prose-lg :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.75;
  margin-top: 2em;
  margin-bottom: 2em;
  border-radius: 0.375rem;
  padding-top: 1em;
  -webkit-padding-end: 1.5em;
          padding-inline-end: 1.5em;
  padding-bottom: 1em;
  -webkit-padding-start: 1.5em;
          padding-inline-start: 1.5em;
}
.prose-lg :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  -webkit-padding-start: 1.5555556em;
          padding-inline-start: 1.5555556em;
}
.prose-lg :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  -webkit-padding-start: 1.5555556em;
          padding-inline-start: 1.5555556em;
}
.prose-lg :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6666667em;
  margin-bottom: 0.6666667em;
}
.prose-lg :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-start: 0.4444444em;
          padding-inline-start: 0.4444444em;
}
.prose-lg :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-start: 0.4444444em;
          padding-inline-start: 0.4444444em;
}
.prose-lg :where(.prose-lg > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}
.prose-lg :where(.prose-lg > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}
.prose-lg :where(.prose-lg > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.3333333em;
}
.prose-lg :where(.prose-lg > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}
.prose-lg :where(.prose-lg > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.3333333em;
}
.prose-lg :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}
.prose-lg :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
}
.prose-lg :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}
.prose-lg :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6666667em;
  -webkit-padding-start: 1.5555556em;
          padding-inline-start: 1.5555556em;
}
.prose-lg :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 3.1111111em;
  margin-bottom: 3.1111111em;
}
.prose-lg :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.5;
}
.prose-lg :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-end: 0.75em;
          padding-inline-end: 0.75em;
  padding-bottom: 0.75em;
  -webkit-padding-start: 0.75em;
          padding-inline-start: 0.75em;
}
.prose-lg :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
}
.prose-lg :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-end: 0;
          padding-inline-end: 0;
}
.prose-lg :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.75em;
  -webkit-padding-end: 0.75em;
          padding-inline-end: 0.75em;
  padding-bottom: 0.75em;
  -webkit-padding-start: 0.75em;
          padding-inline-start: 0.75em;
}
.prose-lg :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
}
.prose-lg :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  -webkit-padding-end: 0;
          padding-inline-end: 0;
}
.prose-lg :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose-lg :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.5;
  margin-top: 1em;
}
.prose-lg :where(.prose-lg > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(.prose-lg > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}
.aspect-h-9 {
  --tw-aspect-h: 9;
}
.aspect-w-16 {
  position: relative;
  padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
  --tw-aspect-w: 16;
}
.aspect-w-16 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
/* Butoane */
/* Input-uri */
.input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-ring-offset-color: #fff;
}
.input::-webkit-file-upload-button {
  border-width: 0px;
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
}
.input::file-selector-button {
  border-width: 0px;
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
}
.input::placeholder {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.input:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
}
.input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
/* Card-uri */
.card {
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
/* Badge-uri */
.badge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  border-width: 1px;
  padding-left: 0.625rem;
  padding-right: 0.625rem;
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 600;
  transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.badge:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
}
/* Alerte */
.alert {
  position: relative;
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 1rem;
}
/* Skeleton loading */
/* Separator */
.separator {
  flex-shrink: 0;
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
.collapse {
  visibility: collapse;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: -webkit-sticky;
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-bottom-0\\.5 {
  bottom: -0.125rem;
}
.-bottom-1 {
  bottom: -0.25rem;
}
.-right-0\\.5 {
  right: -0.125rem;
}
.-right-1 {
  right: -0.25rem;
}
.-top-1 {
  top: -0.25rem;
}
.-top-4 {
  top: -1rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-10 {
  bottom: 2.5rem;
}
.bottom-20 {
  bottom: 5rem;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-6 {
  bottom: 1.5rem;
}
.bottom-full {
  bottom: 100%;
}
.left-0 {
  left: 0px;
}
.left-1\\/2 {
  left: 50%;
}
.left-1\\/4 {
  left: 25%;
}
.left-10 {
  left: 2.5rem;
}
.left-20 {
  left: 5rem;
}
.left-3 {
  left: 0.75rem;
}
.left-4 {
  left: 1rem;
}
.left-full {
  left: 100%;
}
.right-0 {
  right: 0px;
}
.right-1\\/4 {
  right: 25%;
}
.right-10 {
  right: 2.5rem;
}
.right-20 {
  right: 5rem;
}
.right-4 {
  right: 1rem;
}
.right-6 {
  right: 1.5rem;
}
.right-full {
  right: 100%;
}
.top-0 {
  top: 0px;
}
.top-1\\/2 {
  top: 50%;
}
.top-10 {
  top: 2.5rem;
}
.top-16 {
  top: 4rem;
}
.top-20 {
  top: 5rem;
}
.top-4 {
  top: 1rem;
}
.top-40 {
  top: 10rem;
}
.top-full {
  top: 100%;
}
.z-10 {
  z-index: 10;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.-mx-1\\.5 {
  margin-left: -0.375rem;
  margin-right: -0.375rem;
}
.-my-1\\.5 {
  margin-top: -0.375rem;
  margin-bottom: -0.375rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-8 {
  margin-left: 2rem;
  margin-right: 2rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.-mb-1 {
  margin-bottom: -0.25rem;
}
.-mb-px {
  margin-bottom: -1px;
}
.-mt-1 {
  margin-top: -0.25rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-0 {
  margin-left: 0px;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-16 {
  margin-left: 4rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-64 {
  margin-left: 16rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mt-0\\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.h-0\\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-1\\.5 {
  height: 0.375rem;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\\.5 {
  height: 0.625rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-72 {
  height: 18rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-96 {
  height: 24rem;
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.max-h-32 {
  max-height: 8rem;
}
.max-h-40 {
  max-height: 10rem;
}
.max-h-60 {
  max-height: 15rem;
}
.max-h-64 {
  max-height: 16rem;
}
.max-h-96 {
  max-height: 24rem;
}
.min-h-0 {
  min-height: 0px;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0 {
  width: 0px;
}
.w-0\\.5 {
  width: 0.125rem;
}
.w-1 {
  width: 0.25rem;
}
.w-1\\.5 {
  width: 0.375rem;
}
.w-10 {
  width: 2.5rem;
}
.w-11 {
  width: 2.75rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\\.5 {
  width: 0.625rem;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-3 {
  width: 0.75rem;
}
.w-32 {
  width: 8rem;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-56 {
  width: 14rem;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-96 {
  width: 24rem;
}
.w-auto {
  width: auto;
}
.w-full {
  width: 100%;
}
.w-px {
  width: 1px;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-\\[1\\.25rem\\] {
  min-width: 1.25rem;
}
.min-w-\\[1\\.5rem\\] {
  min-width: 1.5rem;
}
.min-w-\\[1rem\\] {
  min-width: 1rem;
}
.min-w-\\[2\\.5rem\\] {
  min-width: 2.5rem;
}
.min-w-\\[200px\\] {
  min-width: 200px;
}
.min-w-\\[2rem\\] {
  min-width: 2rem;
}
.min-w-\\[3rem\\] {
  min-width: 3rem;
}
.min-w-full {
  min-width: 100%;
}
.min-w-max {
  min-width: -webkit-max-content;
  min-width: max-content;
}
.max-w-0 {
  max-width: 0px;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.origin-top {
  transform-origin: top;
}
.-translate-x-1\\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-6 {
  --tw-translate-x: 1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes scaleIn {

  0% {
    transform: scale(0.95);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-help {
  cursor: help;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  -webkit-user-select: none;
          user-select: none;
}
.resize {
  resize: both;
}
.list-inside {
  list-style-position: inside;
}
.list-disc {
  list-style-type: disc;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\\.5 {
  gap: 0.375rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.space-x-0\\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.125rem * var(--tw-space-x-reverse));
  margin-left: calc(0.125rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2.5rem * var(--tw-space-x-reverse));
  margin-left: calc(2.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(0px * var(--tw-divide-y-reverse));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.rounded-t-none {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-4 {
  border-width: 4px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-0 {
  border-bottom-width: 0px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-0 {
  border-right-width: 0px;
}
.border-r-2 {
  border-right-width: 2px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-0 {
  border-top-width: 0px;
}
.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}
.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.border-gray-800 {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}
.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}
.border-green-600 {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}
.border-green-700 {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}
.border-primary-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-primary-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.border-primary-700 {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}
.border-red-700 {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\\/20 {
  border-color: rgb(255 255 255 / 0.2);
}
.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}
.border-yellow-600 {
  --tw-border-opacity: 1;
  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}
.border-yellow-700 {
  --tw-border-opacity: 1;
  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));
}
.border-b-gray-200 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-b-white {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-r-white {
  --tw-border-opacity: 1;
  border-right-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-t-gray-900 {
  --tw-border-opacity: 1;
  border-top-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\\/10 {
  background-color: rgb(0 0 0 / 0.1);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(165 180 252 / var(--tw-bg-opacity, 1));
}
.bg-indigo-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}
.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.bg-pink-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 231 243 / var(--tw-bg-opacity, 1));
}
.bg-pink-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));
}
.bg-primary-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-primary-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-primary-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-primary-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(216 180 254 / var(--tw-bg-opacity, 1));
}
.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\\/10 {
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\\/5 {
  background-color: rgb(255 255 255 / 0.05);
}
.bg-white\\/80 {
  background-color: rgb(255 255 255 / 0.8);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}
.bg-opacity-10 {
  --tw-bg-opacity: 0.1;
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-blue-400 {
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50 {
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600 {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-400 {
  --tw-gradient-from: #9ca3af var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(156 163 175 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-50 {
  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-50\\/50 {
  --tw-gradient-from: rgb(249 250 251 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-50 {
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-600 {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-400 {
  --tw-gradient-from: #facc15 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-indigo-50 {
  --tw-gradient-to: rgb(238 242 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #eef2ff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-600 {
  --tw-gradient-to: rgb(147 51 234 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #9333ea var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-blue-600 {
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}
.to-gray-600 {
  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);
}
.to-indigo-600 {
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}
.to-orange-400 {
  --tw-gradient-to: #fb923c var(--tw-gradient-to-position);
}
.to-primary-100 {
  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);
}
.to-primary-700 {
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}
.to-purple-50 {
  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);
}
.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-secondary-50 {
  --tw-gradient-to: #f8fafc var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-white {
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}
.to-yellow-600 {
  --tw-gradient-to: #ca8a04 var(--tw-gradient-to-position);
}
.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}
.fill-current {
  fill: currentColor;
}
.stroke-blue-600 {
  stroke: #2563eb;
}
.stroke-green-600 {
  stroke: #16a34a;
}
.stroke-primary-600 {
  stroke: #2563eb;
}
.stroke-red-600 {
  stroke: #dc2626;
}
.stroke-yellow-600 {
  stroke: #ca8a04;
}
.object-cover {
  object-fit: cover;
}
.p-0 {
  padding: 0px;
}
.p-0\\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\\.5 {
  padding: 0.375rem;
}
.p-12 {
  padding: 3rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-24 {
  padding-top: 6rem;
  padding-bottom: 6rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-32 {
  padding-bottom: 8rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-20 {
  padding-top: 5rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-8 {
  padding-top: 2rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.font-mono {
  font-family: JetBrains Mono, Fira Code, monospace;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-9xl {
  font-size: 8rem;
  line-height: 1;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.leading-5 {
  line-height: 1.25rem;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-50 {
  --tw-text-opacity: 1;
  color: rgb(239 246 255 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-current {
  color: currentColor;
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-50 {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-50 {
  --tw-text-opacity: 1;
  color: rgb(240 253 244 / var(--tw-text-opacity, 1));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-green-900 {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}
.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}
.text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}
.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-pink-800 {
  --tw-text-opacity: 1;
  color: rgb(157 23 77 / var(--tw-text-opacity, 1));
}
.text-primary-50 {
  --tw-text-opacity: 1;
  color: rgb(239 246 255 / var(--tw-text-opacity, 1));
}
.text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-primary-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-red-200 {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}
.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-50 {
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-red-900 {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}
.text-secondary-600 {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.text-transparent {
  color: transparent;
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-50 {
  --tw-text-opacity: 1;
  color: rgb(254 252 232 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.placeholder-gray-500::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-20 {
  opacity: 0.2;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-90 {
  opacity: 0.9;
}
.mix-blend-multiply {
  mix-blend-mode: multiply;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline {
  outline-style: solid;
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-inset {
  --tw-ring-inset: inset;
}
.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}
.ring-blue-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.ring-blue-600\\/20 {
  --tw-ring-color: rgb(37 99 235 / 0.2);
}
.ring-gray-600\\/20 {
  --tw-ring-color: rgb(75 85 99 / 0.2);
}
.ring-green-600\\/20 {
  --tw-ring-color: rgb(22 163 74 / 0.2);
}
.ring-primary-600\\/20 {
  --tw-ring-color: rgb(37 99 235 / 0.2);
}
.ring-red-600\\/20 {
  --tw-ring-color: rgb(220 38 38 / 0.2);
}
.ring-yellow-600\\/20 {
  --tw-ring-color: rgb(202 138 4 / 0.2);
}
.ring-opacity-20 {
  --tw-ring-opacity: 0.2;
}
.ring-opacity-5 {
  --tw-ring-opacity: 0.05;
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-150 {
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
  transition-timing-function: linear;
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
/* Animații personalizate */
.animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
/* Gradient backgrounds */
/* Text gradients */
/* Shadows personalizate */
/* Responsive utilities */
/* Print utilities */

/* Variabile CSS pentru teme și culori */
:root {
  /* Culori principale */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
}

/* Reset și stiluri de bază */

/* Componente personalizate */

/* Utilități personalizate */

/* Keyframes pentru animații */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceSoft {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulseSoft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animații pentru landing page */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-800 {
  animation-delay: 0.8s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Efecte de hover îmbunătățite */
.hover\\:shadow-3xl:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Backdrop blur pentru browsere care nu suportă */
.backdrop-blur-sm {
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}

@supports not ((-webkit-backdrop-filter: blur(4px)) or (backdrop-filter: blur(4px))) {
  .backdrop-blur-sm {
    background-color: rgba(255, 255, 255, 0.9);
  }
}

/* Stiluri pentru accesibilitate */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Stiluri pentru high contrast */
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
  }

  .input {
    border-width: 2px;
  }

  .card {
    border-width: 2px;
  }
}
.placeholder\\:text-gray-400::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.after\\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}
.after\\:bottom-0::after {
  content: var(--tw-content);
  bottom: 0px;
}
.after\\:bottom-full::after {
  content: var(--tw-content);
  bottom: 100%;
}
.after\\:left-0::after {
  content: var(--tw-content);
  left: 0px;
}
.after\\:left-1\\/2::after {
  content: var(--tw-content);
  left: 50%;
}
.after\\:left-\\[2px\\]::after {
  content: var(--tw-content);
  left: 2px;
}
.after\\:left-full::after {
  content: var(--tw-content);
  left: 100%;
}
.after\\:right-0::after {
  content: var(--tw-content);
  right: 0px;
}
.after\\:right-full::after {
  content: var(--tw-content);
  right: 100%;
}
.after\\:top-0::after {
  content: var(--tw-content);
  top: 0px;
}
.after\\:top-1\\/2::after {
  content: var(--tw-content);
  top: 50%;
}
.after\\:top-\\[2px\\]::after {
  content: var(--tw-content);
  top: 2px;
}
.after\\:top-full::after {
  content: var(--tw-content);
  top: 100%;
}
.after\\:h-0\\.5::after {
  content: var(--tw-content);
  height: 0.125rem;
}
.after\\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}
.after\\:w-0\\.5::after {
  content: var(--tw-content);
  width: 0.125rem;
}
.after\\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}
.after\\:-translate-x-1\\/2::after {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\\:-translate-y-1\\/2::after {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\\:transform::after {
  content: var(--tw-content);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\\:border::after {
  content: var(--tw-content);
  border-width: 1px;
}
.after\\:border-4::after {
  content: var(--tw-content);
  border-width: 4px;
}
.after\\:border-gray-300::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.after\\:border-transparent::after {
  content: var(--tw-content);
  border-color: transparent;
}
.after\\:border-b-gray-900::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-bottom-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.after\\:border-b-green-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-bottom-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}
.after\\:border-b-primary-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-bottom-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.after\\:border-b-red-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-bottom-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}
.after\\:border-b-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.after\\:border-b-yellow-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-bottom-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}
.after\\:border-l-gray-900::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-left-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.after\\:border-l-green-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-left-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}
.after\\:border-l-primary-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-left-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.after\\:border-l-red-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-left-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}
.after\\:border-l-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-left-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.after\\:border-l-yellow-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-left-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}
.after\\:border-r-gray-900::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-right-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.after\\:border-r-green-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-right-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}
.after\\:border-r-primary-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-right-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.after\\:border-r-red-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-right-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}
.after\\:border-r-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-right-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.after\\:border-r-yellow-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-right-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}
.after\\:border-t-gray-900::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-top-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.after\\:border-t-green-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-top-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}
.after\\:border-t-primary-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-top-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.after\\:border-t-red-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-top-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}
.after\\:border-t-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.after\\:border-t-yellow-600::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-top-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}
.after\\:bg-primary-500::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.after\\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.after\\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.after\\:content-\\[\\'\\'\\]::after {
  --tw-content: '';
  content: var(--tw-content);
}
.last\\:mb-0:last-child {
  margin-bottom: 0px;
}
.last\\:border-b-0:last-child {
  border-bottom-width: 0px;
}
.hover\\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\\:-translate-y-2:hover {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\\:scale-\\[1\\.02\\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\\:border-blue-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.hover\\:border-blue-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.hover\\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.hover\\:border-gray-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.hover\\:border-gray-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.hover\\:border-green-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}
.hover\\:border-primary-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}
.hover\\:border-red-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}
.hover\\:border-yellow-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));
}
.hover\\:bg-black:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-blue-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-gray-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-gray-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-gray-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-green-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-primary-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-primary-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-primary-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-primary-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-primary-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-red-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-transparent:hover {
  background-color: transparent;
}
.hover\\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-yellow-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-yellow-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));
}
.hover\\:bg-opacity-10:hover {
  --tw-bg-opacity: 0.1;
}
.hover\\:bg-opacity-50:hover {
  --tw-bg-opacity: 0.5;
}
.hover\\:from-primary-700:hover {
  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\\:to-primary-800:hover {
  --tw-gradient-to: #1e40af var(--tw-gradient-to-position);
}
.hover\\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.hover\\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.hover\\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.hover\\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.hover\\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.hover\\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.hover\\:text-primary-500:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.hover\\:text-primary-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.hover\\:text-primary-700:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.hover\\:text-primary-900:hover {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.hover\\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.hover\\:text-red-900:hover {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}
.hover\\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.hover\\:underline:hover {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.hover\\:opacity-100:hover {
  opacity: 1;
}
.hover\\:opacity-75:hover {
  opacity: 0.75;
}
.hover\\:shadow-2xl:hover {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.focus\\:border-green-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.focus\\:border-primary-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.focus\\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.focus\\:border-transparent:focus {
  border-color: transparent;
}
.focus\\:bg-black:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.focus\\:bg-gray-100:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.focus\\:bg-gray-50:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.focus\\:bg-opacity-10:focus {
  --tw-bg-opacity: 0.1;
}
.focus\\:text-gray-900:focus {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.focus\\:placeholder-gray-400:focus::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.focus\\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.focus\\:ring-gray-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity, 1));
}
.focus\\:ring-green-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}
.focus\\:ring-primary-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.focus\\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}
.focus\\:ring-yellow-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));
}
.focus\\:ring-offset-0:focus {
  --tw-ring-offset-width: 0px;
}
.focus\\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}
.disabled\\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}
.disabled\\:border-gray-300:disabled {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.disabled\\:border-green-300:disabled {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}
.disabled\\:border-primary-300:disabled {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.disabled\\:border-red-300:disabled {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.disabled\\:border-yellow-300:disabled {
  --tw-border-opacity: 1;
  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));
}
.disabled\\:bg-gray-100:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.disabled\\:bg-gray-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.disabled\\:bg-green-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(134 239 172 / var(--tw-bg-opacity, 1));
}
.disabled\\:bg-primary-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}
.disabled\\:bg-red-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(252 165 165 / var(--tw-bg-opacity, 1));
}
.disabled\\:bg-yellow-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));
}
.disabled\\:text-gray-400:disabled {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.disabled\\:text-primary-300:disabled {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.disabled\\:opacity-50:disabled {
  opacity: 0.5;
}
.disabled\\:opacity-60:disabled {
  opacity: 0.6;
}
.group:hover .group-hover\\:-translate-x-1 {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\\:text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\\:text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\\:text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\\:text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\\:opacity-100 {
  opacity: 1;
}
.peer:checked ~ .peer-checked\\:bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.peer:checked ~ .peer-checked\\:after\\:translate-x-full::after {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.peer:checked ~ .peer-checked\\:after\\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.peer:focus ~ .peer-focus\\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.peer:focus ~ .peer-focus\\:ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.peer:focus ~ .peer-focus\\:ring-blue-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));
}
@media (min-width: 640px) {

  .sm\\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\\:mt-0 {
    margin-top: 0px;
  }

  .sm\\:flex {
    display: flex;
  }

  .sm\\:hidden {
    display: none;
  }

  .sm\\:w-full {
    width: 100%;
  }

  .sm\\:max-w-md {
    max-width: 28rem;
  }

  .sm\\:flex-1 {
    flex: 1 1 0%;
  }

  .sm\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\\:flex-row {
    flex-direction: row;
  }

  .sm\\:items-center {
    align-items: center;
  }

  .sm\\:justify-between {
    justify-content: space-between;
  }

  .sm\\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
@media (min-width: 768px) {

  .md\\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\\:block {
    display: block;
  }

  .md\\:flex {
    display: flex;
  }

  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\\:flex-row {
    flex-direction: row;
  }

  .md\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}
@media (min-width: 1024px) {

  .lg\\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\\:ml-16 {
    margin-left: 4rem;
  }

  .lg\\:ml-64 {
    margin-left: 16rem;
  }

  .lg\\:flex {
    display: flex;
  }

  .lg\\:hidden {
    display: none;
  }

  .lg\\:w-16 {
    width: 4rem;
  }

  .lg\\:w-64 {
    width: 16rem;
  }

  .lg\\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\\:p-6 {
    padding: 1.5rem;
  }

  .lg\\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
@media (min-width: 1280px) {

  .xl\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xl\\:p-8 {
    padding: 2rem;
  }
}
@media print {

  .print\\:block {
    display: block;
  }
}
.\\[\\&\\>button\\:first-child\\]\\:rounded-l-lg>button:first-child {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.\\[\\&\\>button\\:first-child\\]\\:rounded-l-none>button:first-child {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.\\[\\&\\>button\\:first-child\\]\\:rounded-t-lg>button:first-child {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.\\[\\&\\>button\\:last-child\\]\\:rounded-b-lg>button:last-child {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.\\[\\&\\>button\\:last-child\\]\\:rounded-r-lg>button:last-child {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.\\[\\&\\>button\\:last-child\\]\\:rounded-r-none>button:last-child {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.\\[\\&\\>button\\:not\\(\\:first-child\\)\\]\\:border-l>button:not(:first-child) {
  border-left-width: 1px;
}
.\\[\\&\\>button\\:not\\(\\:first-child\\)\\]\\:border-l-0>button:not(:first-child) {
  border-left-width: 0px;
}
.\\[\\&\\>button\\:not\\(\\:first-child\\)\\]\\:border-t-0>button:not(:first-child) {
  border-top-width: 0px;
}
.\\[\\&\\>button\\]\\:rounded-none>button {
  border-radius: 0px;
}
.\\[\\&\\>tr\\:nth-child\\(even\\)\\]\\:bg-gray-50>tr:nth-child(even) {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.\\[\\&\\>tr\\]\\:transition-colors>tr {
  transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.\\[\\&\\>tr\\]\\:hover\\:bg-gray-50:hover>tr {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}`, ""]);
// Exports
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);


/***/ }),

/***/ 9050:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   cn: () => (/* binding */ cn)
/* harmony export */ });
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4164);
/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(856);


function cn(...inputs) {
    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__/* .twMerge */ .QP)((0,clsx__WEBPACK_IMPORTED_MODULE_0__/* .clsx */ .$)(inputs));
}


/***/ }),

/***/ 9264:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony exports PageLoader, ButtonSpinner, CardLoader, OverlayLoader, InlineLoader */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3658);



// Variante de dimensiuni pentru spinner
const sizeVariants = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
    '2xl': 'w-16 h-16',
};
// Variante de culori pentru spinner
const colorVariants = {
    primary: 'text-primary-600',
    secondary: 'text-secondary-600',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    danger: 'text-red-600',
    white: 'text-white',
    gray: 'text-gray-600',
    currentColor: 'text-current',
};
// Tipuri de spinner
const SpinnerTypes = {
    CIRCULAR: 'circular',
    DOTS: 'dots',
    PULSE: 'pulse',
    BARS: 'bars',
};
// Componenta Spinner circular
const CircularSpinner = ({ size, color, className }) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("svg", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)('animate-spin', sizeVariants[size], colorVariants[color], className), xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("path", { className: "opacity-75", fill: "currentColor", d: "m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })] }));
// Componenta Spinner cu puncte
const DotsSpinner = ({ size, color, className }) => {
    const dotSize = {
        xs: 'w-1 h-1',
        sm: 'w-1.5 h-1.5',
        md: 'w-2 h-2',
        lg: 'w-2.5 h-2.5',
        xl: 'w-3 h-3',
        '2xl': 'w-4 h-4',
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)('flex space-x-1', className), children: [0, 1, 2].map(index => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)('rounded-full animate-pulse', dotSize[size], colorVariants[color].replace('text-', 'bg-')), style: {
                animationDelay: `${index * 0.2}s`,
                animationDuration: '1s',
            } }, index))) }));
};
// Componenta Spinner cu puls
const PulseSpinner = ({ size, color, className }) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)('rounded-full animate-pulse', sizeVariants[size], colorVariants[color].replace('text-', 'bg-'), className), style: {
        animationDuration: '1.5s',
    } }));
// Componenta Spinner cu bare
const BarsSpinner = ({ size, color, className }) => {
    const barHeight = {
        xs: 'h-2',
        sm: 'h-3',
        md: 'h-4',
        lg: 'h-6',
        xl: 'h-8',
        '2xl': 'h-10',
    };
    const barWidth = {
        xs: 'w-0.5',
        sm: 'w-0.5',
        md: 'w-1',
        lg: 'w-1',
        xl: 'w-1.5',
        '2xl': 'w-2',
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-end space-x-0.5', className), children: [0, 1, 2, 3, 4].map(index => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)('animate-pulse', barHeight[size], barWidth[size], colorVariants[color].replace('text-', 'bg-')), style: {
                animationDelay: `${index * 0.1}s`,
                animationDuration: '1s',
            } }, index))) }));
};
// Componenta principală LoadingSpinner
const LoadingSpinner = ({ size = 'md', color = 'primary', type = 'circular', className, label, inline = false, overlay = false, overlayClassName, }) => {
    // Selectează componenta de spinner în funcție de tip
    const SpinnerComponent = {
        circular: CircularSpinner,
        dots: DotsSpinner,
        pulse: PulseSpinner,
        bars: BarsSpinner,
    }[type];
    const spinner = (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SpinnerComponent, { size: size, color: color, className: className || '' });
    // Dacă este inline, returnează doar spinner-ul
    if (inline) {
        return label ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [spinner, label && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)('text-sm', colorVariants[color]), children: label })] })) : (spinner);
    }
    // Dacă este overlay, afișează peste conținut
    if (overlay) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)('absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm z-50', overlayClassName), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col items-center space-y-2", children: [spinner, label && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)('text-sm font-medium', colorVariants[color]), children: label }))] }) }));
    }
    // Afișare normală cu centrat
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col items-center justify-center space-y-2", children: [spinner, label && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)('text-sm font-medium', colorVariants[color]), children: label })] }));
};
// Componente specializate pentru cazuri comune
const PageLoader = ({ label = 'Se încarcă...' }) => (_jsx("div", { className: "min-h-screen flex items-center justify-center bg-gray-50", children: _jsx(LoadingSpinner, { size: "lg", label: label }) }));
const ButtonSpinner = ({ size = 'sm', color = 'white' }) => (_jsx(LoadingSpinner, { size: size, color: color, inline: true }));
const CardLoader = ({ label }) => (_jsx("div", { className: "flex items-center justify-center p-8", children: _jsx(LoadingSpinner, { size: "md", label: label || '' }) }));
const OverlayLoader = ({ label = 'Se încarcă...', className, }) => _jsx(LoadingSpinner, { overlay: true, label: label, overlayClassName: className || '' });
const InlineLoader = ({ size = 'sm', label, className }) => (_jsx(LoadingSpinner, { size: size, label: label || '', inline: true, className: className || '' }));
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingSpinner);


/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, [644,209,137,148,96], () => (__webpack_exec__(1134)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);