import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';
import { toast } from 'react-hot-toast';

import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import Input from '../components/ui/Input';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import Modal from '../components/ui/Modal';
import { formatCurrency } from '../utils/helpers';

// Tipuri pentru pagina Categories
interface Category {
  id: string;
  name: string;
  description: string;
  color: string;
  totalExpenses: number;
  expenseCount: number;
}

interface CategoryFormData {
  name: string;
  description: string;
  color: string;
}

// Mock data pentru demonstrație
const mockCategories: Category[] = [
  {
    id: 'cat_1',
    name: '<PERSON><PERSON><PERSON>',
    description: '<PERSON>el<PERSON><PERSON><PERSON> pentru mâncare și băuturi',
    color: '#3B82F6',
    totalExpenses: 450.25,
    expenseCount: 15,
  },
  {
    id: 'cat_2',
    name: 'Transport',
    description: 'Cheltuieli pentru transport și combustibil',
    color: '#10B981',
    totalExpenses: 200.0,
    expenseCount: 8,
  },
  {
    id: 'cat_3',
    name: 'Utilități',
    description: 'Facturi și utilități casnice',
    color: '#F59E0B',
    totalExpenses: 150.0,
    expenseCount: 5,
  },
  {
    id: 'cat_4',
    name: 'Divertisment',
    description: 'Activități de divertisment și hobby-uri',
    color: '#8B5CF6',
    totalExpenses: 50.0,
    expenseCount: 3,
  },
];

const Categories: React.FC = () => {
  const [showModal, setShowModal] = useState<boolean>(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    description: '',
    color: '#3B82F6',
  });

  // Simulare query pentru categorii
  const {
    data: categories,
    isLoading,
    error,
  } = useQuery<Category[]>({
    queryKey: ['categories'],
    queryFn: async (): Promise<Category[]> => {
      // Simulare API call
      await new Promise(resolve => setTimeout(resolve, 500));
      return mockCategories;
    },
  });

  const handleOpenModal = (category: Category | null = null): void => {
    if (category) {
      setEditingCategory(category);
      setFormData({
        name: category.name,
        description: category.description,
        color: category.color,
      });
    } else {
      setEditingCategory(null);
      setFormData({
        name: '',
        description: '',
        color: '#3B82F6',
      });
    }
    setShowModal(true);
  };

  const handleCloseModal = (): void => {
    setShowModal(false);
    setEditingCategory(null);
    setFormData({ name: '', description: '', color: '#3B82F6' });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    try {
      // Aici ar trebui să faci request către API
      if (editingCategory) {
        // Update category
        toast.success('Categoria a fost actualizată cu succes!');
      } else {
        // Create category
        toast.success('Categoria a fost creată cu succes!');
      }
      handleCloseModal();
    } catch (error) {
      toast.error('Eroare la salvarea categoriei');
    }
  };

  const handleDelete = async (categoryId: string): Promise<void> => {
    if (window.confirm('Ești sigur că vrei să ștergi această categorie?')) {
      try {
        // Aici ar trebui să faci request către API
        toast.success('Categoria a fost ștearsă cu succes!');
      } catch (error) {
        toast.error('Eroare la ștergerea categoriei');
      }
    }
  };

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Eroare la încărcarea categoriilor</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Categorii</h1>
          <p className="text-gray-600">Gestionează categoriile pentru cheltuielile tale</p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button variant="primary" onClick={() => handleOpenModal()} className="flex items-center">
            <PlusIcon className="h-5 w-5 mr-2" />
            Adaugă categorie
          </Button>
        </div>
      </div>

      {/* Lista categoriilor */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      ) : categories && categories.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map(category => (
            <Card key={category.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div
                    className="w-4 h-4 rounded-full mr-3"
                    style={{ backgroundColor: category.color }}
                  />
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{category.name}</h3>
                    <p className="text-sm text-gray-500 mt-1">{category.description}</p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleOpenModal(category)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(category.id)}
                    className="text-gray-400 hover:text-red-600"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-500">Total cheltuieli</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatCurrency(category.totalExpenses)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">Numărul de cheltuieli</p>
                    <p className="text-lg font-semibold text-gray-900">{category.expenseCount}</p>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="p-12 text-center">
          <p className="text-gray-500 mb-4">Nu ai încă categorii create</p>
          <Button
            variant="primary"
            onClick={() => handleOpenModal()}
            className="flex items-center mx-auto"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Creează prima categorie
          </Button>
        </Card>
      )}

      {/* Modal pentru adăugare/editare categorie */}
      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        title={editingCategory ? 'Editează categoria' : 'Adaugă categorie nouă'}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Input
              label="Nume categorie"
              type="text"
              value={formData.name}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setFormData({ ...formData, name: e.target.value })
              }
              placeholder="Ex: Mâncare, Transport, etc."
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Descriere</label>
            <textarea
              value={formData.description}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                setFormData({ ...formData, description: e.target.value })
              }
              placeholder="Descriere opțională pentru categorie"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Culoare</label>
            <div className="flex items-center space-x-2">
              <input
                type="color"
                value={formData.color}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFormData({ ...formData, color: e.target.value })
                }
                className="w-12 h-10 border border-gray-300 rounded-md"
              />
              <input
                type="text"
                value={formData.color}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFormData({ ...formData, color: e.target.value })
                }
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                placeholder="#3B82F6"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline" onClick={handleCloseModal}>
              Anulează
            </Button>
            <Button type="submit" variant="primary">
              {editingCategory ? 'Actualizează' : 'Creează'}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default Categories;
