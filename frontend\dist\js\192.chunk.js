"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[192],{

/***/ 8192:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2389);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4976);
/* harmony import */ var _components_layout_PublicLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(4370);






const Documentation = () => {
    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__/* .useTranslation */ .Bd)();
    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');
    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');
    const categories = [
        {
            id: 'all',
            name: t('docs.categories.all', 'Toate'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .BookOpenIcon */ .RuZ,
        },
        {
            id: 'getting-started',
            name: t('docs.categories.getting_started', 'Primii Pași'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .RocketLaunchIcon */ .Paj,
        },
        {
            id: 'features',
            name: t('docs.categories.features', 'Funcționalități'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CogIcon */ .DP5,
        },
        {
            id: 'api',
            name: t('docs.categories.api', 'API'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CodeBracketIcon */ .r$2,
        },
        {
            id: 'security',
            name: t('docs.categories.security', 'Securitate'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ShieldCheckIcon */ .Zus,
        },
        {
            id: 'faq',
            name: t('docs.categories.faq', 'FAQ'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .QuestionMarkCircleIcon */ .R2D,
        },
    ];
    const documentationSections = [
        {
            category: 'getting-started',
            title: t('docs.getting_started.title', 'Primii Pași'),
            description: t('docs.getting_started.description', 'Totul ce trebuie să știți pentru a începe cu FinanceFlow'),
            articles: [
                {
                    title: t('docs.getting_started.setup.title', 'Configurarea Contului'),
                    description: t('docs.getting_started.setup.description', 'Cum să vă creați și configurați contul pentru prima dată'),
                    readTime: '5 min',
                    type: 'guide',
                },
                {
                    title: t('docs.getting_started.first_budget.title', 'Primul Dvs. Buget'),
                    description: t('docs.getting_started.first_budget.description', 'Ghid pas cu pas pentru crearea primului buget'),
                    readTime: '10 min',
                    type: 'tutorial',
                },
                {
                    title: t('docs.getting_started.connect_bank.title', 'Conectarea Băncii'),
                    description: t('docs.getting_started.connect_bank.description', 'Cum să conectați conturile bancare în siguranță'),
                    readTime: '7 min',
                    type: 'guide',
                },
                {
                    title: t('docs.getting_started.mobile_app.title', 'Aplicația Mobilă'),
                    description: t('docs.getting_started.mobile_app.description', 'Descărcarea și configurarea aplicației mobile'),
                    readTime: '3 min',
                    type: 'guide',
                },
            ],
        },
        {
            category: 'features',
            title: t('docs.features.title', 'Funcționalități'),
            description: t('docs.features.description', 'Ghiduri detaliate pentru toate funcționalitățile FinanceFlow'),
            articles: [
                {
                    title: t('docs.features.budgeting.title', 'Bugetare Avansată'),
                    description: t('docs.features.budgeting.description', 'Cum să utilizați funcționalitățile avansate de bugetare'),
                    readTime: '15 min',
                    type: 'guide',
                },
                {
                    title: t('docs.features.analytics.title', 'Analiză și Rapoarte'),
                    description: t('docs.features.analytics.description', 'Înțelegerea graficelor și rapoartelor financiare'),
                    readTime: '12 min',
                    type: 'guide',
                },
                {
                    title: t('docs.features.goals.title', 'Obiective Financiare'),
                    description: t('docs.features.goals.description', 'Setarea și urmărirea obiectivelor financiare'),
                    readTime: '8 min',
                    type: 'tutorial',
                },
                {
                    title: t('docs.features.notifications.title', 'Notificări și Alerte'),
                    description: t('docs.features.notifications.description', 'Configurarea notificărilor personalizate'),
                    readTime: '6 min',
                    type: 'guide',
                },
                {
                    title: t('docs.features.export.title', 'Export și Backup'),
                    description: t('docs.features.export.description', 'Cum să exportați și să faceți backup datelor'),
                    readTime: '5 min',
                    type: 'guide',
                },
            ],
        },
        {
            category: 'api',
            title: t('docs.api.title', 'API Documentation'),
            description: t('docs.api.description', 'Documentație completă pentru API-ul FinanceFlow'),
            articles: [
                {
                    title: t('docs.api.authentication.title', 'Autentificare API'),
                    description: t('docs.api.authentication.description', 'Cum să vă autentificați și să utilizați API-ul'),
                    readTime: '10 min',
                    type: 'technical',
                },
                {
                    title: t('docs.api.endpoints.title', 'Endpoint-uri Disponibile'),
                    description: t('docs.api.endpoints.description', 'Lista completă a endpoint-urilor API'),
                    readTime: '20 min',
                    type: 'reference',
                },
                {
                    title: t('docs.api.webhooks.title', 'Webhooks'),
                    description: t('docs.api.webhooks.description', 'Configurarea și utilizarea webhook-urilor'),
                    readTime: '15 min',
                    type: 'technical',
                },
                {
                    title: t('docs.api.examples.title', 'Exemple de Cod'),
                    description: t('docs.api.examples.description', 'Exemple practice în multiple limbaje de programare'),
                    readTime: '25 min',
                    type: 'tutorial',
                },
            ],
        },
        {
            category: 'security',
            title: t('docs.security.title', 'Securitate'),
            description: t('docs.security.description', 'Informații despre securitatea și protecția datelor'),
            articles: [
                {
                    title: t('docs.security.overview.title', 'Prezentare Generală Securitate'),
                    description: t('docs.security.overview.description', 'Cum protejăm datele și informațiile dvs.'),
                    readTime: '8 min',
                    type: 'guide',
                },
                {
                    title: t('docs.security.2fa.title', 'Autentificare cu Doi Factori'),
                    description: t('docs.security.2fa.description', 'Activarea și utilizarea 2FA pentru securitate suplimentară'),
                    readTime: '5 min',
                    type: 'tutorial',
                },
                {
                    title: t('docs.security.privacy.title', 'Setări Confidențialitate'),
                    description: t('docs.security.privacy.description', 'Gestionarea setărilor de confidențialitate'),
                    readTime: '7 min',
                    type: 'guide',
                },
                {
                    title: t('docs.security.compliance.title', 'Conformitate și Reglementări'),
                    description: t('docs.security.compliance.description', 'Standardele de conformitate pe care le respectăm'),
                    readTime: '12 min',
                    type: 'reference',
                },
            ],
        },
        {
            category: 'faq',
            title: t('docs.faq.title', 'Întrebări Frecvente'),
            description: t('docs.faq.description', 'Răspunsuri la cele mai comune întrebări'),
            articles: [
                {
                    title: t('docs.faq.account.title', 'Întrebări despre Cont'),
                    description: t('docs.faq.account.description', 'Gestionarea contului, resetarea parolei, etc.'),
                    readTime: '3 min',
                    type: 'faq',
                },
                {
                    title: t('docs.faq.billing.title', 'Facturare și Plăți'),
                    description: t('docs.faq.billing.description', 'Întrebări despre planuri, facturare și plăți'),
                    readTime: '5 min',
                    type: 'faq',
                },
                {
                    title: t('docs.faq.technical.title', 'Probleme Tehnice'),
                    description: t('docs.faq.technical.description', 'Rezolvarea problemelor tehnice comune'),
                    readTime: '8 min',
                    type: 'faq',
                },
                {
                    title: t('docs.faq.features.title', 'Întrebări despre Funcționalități'),
                    description: t('docs.faq.features.description', 'Cum să utilizați diferitele funcționalități'),
                    readTime: '10 min',
                    type: 'faq',
                },
            ],
        },
    ];
    const getTypeIcon = (type) => {
        switch (type) {
            case 'tutorial':
                return _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .PlayIcon */ .udU;
            case 'guide':
                return _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .BookOpenIcon */ .RuZ;
            case 'technical':
                return _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CodeBracketIcon */ .r$2;
            case 'reference':
                return _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DocumentTextIcon */ .AQX;
            case 'faq':
                return _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .QuestionMarkCircleIcon */ .R2D;
            default:
                return _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DocumentTextIcon */ .AQX;
        }
    };
    const getTypeColor = (type) => {
        switch (type) {
            case 'tutorial':
                return 'bg-green-100 text-green-800';
            case 'guide':
                return 'bg-blue-100 text-blue-800';
            case 'technical':
                return 'bg-purple-100 text-purple-800';
            case 'reference':
                return 'bg-gray-100 text-gray-800';
            case 'faq':
                return 'bg-yellow-100 text-yellow-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    const filteredSections = documentationSections.filter(section => selectedCategory === 'all' || section.category === selectedCategory);
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_layout_PublicLayout__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "min-h-screen bg-gray-50", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-white shadow-sm", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/", className: "flex items-center text-gray-600 hover:text-gray-900 transition-colors", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowLeftIcon */ .A60, { className: "w-5 h-5 mr-2" }), t('common.back', 'Înapoi')] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "h-6 w-px bg-gray-300" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1", { className: "text-3xl font-bold text-gray-900", children: t('support.documentation.title', 'Documentație') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "mt-4 text-lg text-gray-600 max-w-3xl", children: t('support.documentation.subtitle', 'Ghiduri complete, tutoriale și documentație API pentru a vă ajuta să profitați la maximum de FinanceFlow.') })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg shadow-sm p-6 mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .MagnifyingGlassIcon */ .$p$, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "text", placeholder: t('docs.search.placeholder', 'Căutați în documentație...'), value: searchQuery, onChange: (e) => setSearchQuery(e.target.value), className: "w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex flex-wrap gap-2", children: categories.map((category) => {
                                        const IconComponent = category.icon;
                                        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { onClick: () => setSelectedCategory(category.id), className: `flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${selectedCategory === category.id
                                                ? 'bg-blue-100 text-blue-800 border border-blue-200'
                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconComponent, { className: "w-4 h-4 mr-2" }), category.name] }, category.id));
                                    }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 mb-8 text-white", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-2xl font-bold mb-2", children: t('docs.quick_start.title', 'Start Rapid') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-blue-100 mb-4", children: t('docs.quick_start.description', 'Nou pe FinanceFlow? Începeți cu ghidul nostru de start rapid.') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { className: "bg-white text-blue-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors", children: t('docs.quick_start.cta', 'Începeți Acum') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "hidden md:block", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .RocketLaunchIcon */ .Paj, { className: "w-24 h-24 text-blue-200" }) })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-8", children: filteredSections.map((section, sectionIndex) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg shadow-sm p-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-2xl font-bold text-gray-900 mb-2", children: section.title }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: section.description })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: section.articles.map((article, articleIndex) => {
                                            const TypeIcon = getTypeIcon(article.type);
                                            return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "border border-gray-200 rounded-lg p-6 hover:shadow-md transition-all duration-300 hover:border-gray-300 cursor-pointer group", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start justify-between mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-gray-100 p-2 rounded-lg mr-3", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TypeIcon, { className: "w-5 h-5 text-gray-600" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: `inline-block px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(article.type)}`, children: article.type.toUpperCase() }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronRightIcon */ .vKP, { className: "w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors", children: article.title }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600 text-sm mb-4 leading-relaxed", children: article.description }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center text-sm text-gray-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: article.readTime }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "mx-2", children: "\u2022" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: t('docs.read_time', 'timp de citire') })] })] }, articleIndex));
                                        }) })] }, sectionIndex))) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-gray-100 rounded-lg p-8 mt-12 text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .QuestionMarkCircleIcon */ .R2D, { className: "w-16 h-16 text-gray-400 mx-auto mb-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-2xl font-bold text-gray-900 mb-4", children: t('docs.help.title', 'Nu găsiți ce căutați?') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600 mb-6 max-w-2xl mx-auto", children: t('docs.help.description', 'Echipa noastră de suport este aici să vă ajute. Contactați-ne pentru asistență personalizată.') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col sm:flex-row gap-4 justify-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/support/contact", className: "bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors", children: t('docs.help.contact', 'Contactați Suportul') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/support/community", className: "border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors", children: t('docs.help.community', 'Comunitatea') })] })] })] })] }) }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Documentation);


/***/ })

}]);