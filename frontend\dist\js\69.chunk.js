"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[69],{

/***/ 9069:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(893);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9785);
/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(888);
/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(4862);
/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6103);
/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(125);
/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(6215);
/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(250);











// Schema pentru validarea profilului
const profileSchema = zod__WEBPACK_IMPORTED_MODULE_6__.z.object({
    firstName: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().min(2, 'Prenumele trebuie să aibă cel puțin 2 caractere'),
    lastName: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().min(2, 'Numele trebuie să aibă cel puțin 2 caractere'),
    email: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().email('Email invalid'),
    currency: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().optional(),
    timezone: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().optional(),
});
// Schema pentru schimbarea parolei
const passwordSchema = zod__WEBPACK_IMPORTED_MODULE_6__.z.object({
    currentPassword: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().min(1, 'Parola curentă este obligatorie'),
    newPassword: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().min(8, 'Parola nouă trebuie să aibă cel puțin 8 caractere'),
    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().min(1, 'Confirmarea parolei este obligatorie'),
})
    .refine(data => data.newPassword === data.confirmPassword, {
    message: 'Parolele nu se potrivesc',
    path: ['confirmPassword'],
});
const Profile = () => {
    const { user, isAuthenticated } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_10__/* .useAuthStore */ .nc)();
    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('profile');
    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);
    // Form pentru profil
    const { register: registerProfile, handleSubmit: handleSubmitProfile, formState: { errors: profileErrors, isSubmitting: isSubmittingProfile }, reset: resetProfileForm, } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_4__/* .useForm */ .mN)({
        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__/* .zodResolver */ .u)(profileSchema),
        defaultValues: {
            firstName: user?.firstName || '',
            lastName: user?.lastName || '',
            email: user?.email || '',
            currency: user?.currency || 'USD',
            timezone: user?.timezone || 'UTC',
        },
    });
    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(() => {
        if (user) {
            resetProfileForm({
                firstName: user.firstName || '',
                lastName: user.lastName || '',
                email: user.email || '',
                currency: user.currency || 'USD',
                timezone: user.timezone || 'UTC',
            });
        }
    }, [user, resetProfileForm]);
    // Form pentru parolă
    const { register: registerPassword, handleSubmit: handleSubmitPassword, formState: { errors: passwordErrors, isSubmitting: isSubmittingPassword }, reset: resetPasswordForm, } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_4__/* .useForm */ .mN)({
        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__/* .zodResolver */ .u)(passwordSchema),
    });
    const handleProfileSubmit = async (data) => {
        try {
            // Aici ar trebui să faci request către API
            await new Promise(resolve => setTimeout(resolve, 1000));
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.success('Profilul a fost actualizat cu succes!');
        }
        catch (error) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.error('Eroare la actualizarea profilului');
        }
    };
    const handlePasswordSubmit = async (data) => {
        try {
            // Aici ar trebui să faci request către API
            await new Promise(resolve => setTimeout(resolve, 1000));
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.success('Parola a fost schimbată cu succes!');
            resetPasswordForm();
        }
        catch (error) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.error('Eroare la schimbarea parolei');
        }
    };
    const handleAvatarUpload = async (event) => {
        const file = event.target.files?.[0];
        if (!file)
            return;
        if (file.size > 5 * 1024 * 1024) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.error('Fișierul este prea mare. Dimensiunea maximă este 5MB.');
            return;
        }
        if (!file.type.startsWith('image/')) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.error('Te rog să selectezi un fișier imagine valid.');
            return;
        }
        setIsUploading(true);
        try {
            // Aici ar trebui să faci upload către API
            await new Promise(resolve => setTimeout(resolve, 2000));
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.success('Avatarul a fost actualizat cu succes!');
        }
        catch (error) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.error('Eroare la încărcarea avatarului');
        }
        finally {
            setIsUploading(false);
        }
    };
    const tabs = [
        { id: 'profile', label: 'Informații personale', icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UserIcon */ .nys },
        { id: 'security', label: 'Securitate', icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .KeyIcon */ .RYV },
        { id: 'notifications', label: 'Notificări', icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .BellIcon */ .XFE },
    ];
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1", { className: "text-2xl font-bold text-gray-900", children: "Profil" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: "Gestioneaz\u0103 informa\u021Biile contului t\u0103u" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "border-b border-gray-200", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("nav", { className: "-mb-px flex space-x-8", children: tabs.map(tab => {
                        const Icon = tab.icon;
                        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { onClick: () => setActiveTab(tab.id), className: `flex items-center py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id
                                ? 'border-primary-500 text-primary-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Icon, { className: "h-5 w-5 mr-2" }), tab.label] }, tab.id));
                    }) }) }), activeTab === 'profile' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 lg:grid-cols-3 gap-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative inline-block", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center mx-auto", children: user?.avatar ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("img", { src: user.avatar, alt: "Avatar", className: "w-32 h-32 rounded-full object-cover" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UserIcon */ .nys, { className: "h-16 w-16 text-gray-400" })) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", { className: "absolute bottom-0 right-0 bg-primary-600 text-white p-2 rounded-full cursor-pointer hover:bg-primary-700", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CameraIcon */ .xpe, { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "file", accept: "image/*", onChange: handleAvatarUpload, className: "hidden", disabled: isUploading })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("h3", { className: "mt-4 text-lg font-medium text-gray-900", children: [user?.firstName ?? '', " ", user?.lastName ?? ''] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-500", children: user?.email ?? '' }), isUploading && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-primary-600 mt-2", children: "Se \u00EEncarc\u0103..." })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "lg:col-span-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-gray-900 mb-6", children: "Informa\u021Bii personale" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("form", { onSubmit: handleSubmitProfile(handleProfileSubmit), className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { label: "Prenume", ...registerProfile('firstName'), error: profileErrors.firstName?.message, leftIcon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UserIcon */ .nys, { className: "h-5 w-5 text-gray-400" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { label: "Nume", ...registerProfile('lastName'), error: profileErrors.lastName?.message, leftIcon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UserIcon */ .nys, { className: "h-5 w-5 text-gray-400" }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { label: "Email", type: "email", ...registerProfile('email'), error: profileErrors.email?.message, leftIcon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .EnvelopeIcon */ .u6c, { className: "h-5 w-5 text-gray-400" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Moned\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select", { ...registerProfile('currency'), className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "USD", children: "USD - Dolar American" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "EUR", children: "EUR - Euro" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "RON", children: "RON - Leu Rom\u00E2nesc" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "GBP", children: "GBP - Lir\u0103 Sterlin\u0103" })] }), profileErrors.currency && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "mt-1 text-sm text-red-600", children: profileErrors.currency.message }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Fus orar" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select", { ...registerProfile('timezone'), className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "UTC", children: "UTC - Coordinated Universal Time" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "Europe/Bucharest", children: "Europe/Bucharest - Rom\u00E2nia" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "Europe/London", children: "Europe/London - Marea Britanie" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "America/New_York", children: "America/New_York - New York" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "America/Los_Angeles", children: "America/Los_Angeles - Los Angeles" })] }), profileErrors.timezone && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "mt-1 text-sm text-red-600", children: profileErrors.timezone.message }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex justify-end", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, { type: "submit", variant: "primary", disabled: isSubmittingProfile, children: isSubmittingProfile ? 'Se salvează...' : 'Salvează modificările' }) })] })] }) })] })), activeTab === 'security' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay, { className: "p-6 max-w-2xl", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-gray-900 mb-6", children: "Schimb\u0103 parola" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("form", { onSubmit: handleSubmitPassword(handlePasswordSubmit), className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { label: "Parola curent\u0103", type: "password", ...registerPassword('currentPassword'), error: passwordErrors.currentPassword?.message, leftIcon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .KeyIcon */ .RYV, { className: "h-5 w-5 text-gray-400" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { label: "Parola nou\u0103", type: "password", ...registerPassword('newPassword'), error: passwordErrors.newPassword?.message, leftIcon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .KeyIcon */ .RYV, { className: "h-5 w-5 text-gray-400" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { label: "Confirm\u0103 parola nou\u0103", type: "password", ...registerPassword('confirmPassword'), error: passwordErrors.confirmPassword?.message, leftIcon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .KeyIcon */ .RYV, { className: "h-5 w-5 text-gray-400" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex justify-end", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, { type: "submit", variant: "primary", disabled: isSubmittingPassword, children: isSubmittingPassword ? 'Se schimbă...' : 'Schimbă parola' }) })] })] })), activeTab === 'notifications' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay, { className: "p-6 max-w-2xl", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-gray-900 mb-6", children: "Preferin\u021Be notific\u0103ri" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900", children: "Notific\u0103ri email" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: "Prime\u0219te notific\u0103ri despre cheltuieli \u0219i rapoarte" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", defaultChecked: true, className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900", children: "Notific\u0103ri push" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: "Prime\u0219te notific\u0103ri \u00EEn browser" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", defaultChecked: true, className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900", children: "Rapoarte s\u0103pt\u0103m\u00E2nale" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: "Prime\u0219te un rezumat s\u0103pt\u0103m\u00E2nal al cheltuielilor" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", defaultChecked: true, className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900", children: "Alerte buget" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: "Prime\u0219te alerte c\u00E2nd dep\u0103\u0219e\u0219ti bugetul" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", defaultChecked: true, className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "pt-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, { variant: "primary", children: "Salveaz\u0103 preferin\u021Bele" }) })] })] }))] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Profile);


/***/ })

}]);