# Expense Tracker - Aplicație de Tracking Cheltuieli

O aplicație modernă pentru gestionarea cheltuielilor personale, construită cu React, Node.js și PostgreSQL.

## 🚀 Funcționalități

- **Autentificare și Autorizare**: Sistem complet de înregistrare și autentificare
- **Gestionare Cheltuieli**: Adăugare, editare și ștergere cheltuieli
- **Categorii Personalizate**: Organizarea cheltuielilor pe categorii
- **Rapoarte și Statistici**: Vizualizarea datelor prin grafice și tabele
- **Export Date**: Export în format Excel și PDF
- **Responsive Design**: Optimizat pentru desktop și mobile
- **Sistem de Planuri**: Abonamente cu funcționalități premium
- **Integrare Stripe**: Procesarea plăților pentru abonamente
- **CUID-uri**: Identificatori unici și siguri pentru toate entitățile
- **Gestionare Nume**: Suport pentru prenume și nume de familie separate

## 🏗️ Arhitectura Aplicației

### Backend
- **Node.js** cu Express.js
- **PostgreSQL** pentru baza de date
- **Prisma** ca ORM
- **CUID2** pentru generarea ID-urilor
- **JWT** pentru autentificare
- **Stripe** pentru procesarea plăților

### Frontend
- **React** cu TypeScript
- **Tailwind CSS** pentru styling
- **React Router** pentru navigare
- **Axios** pentru API calls
- **React Hook Form** pentru formulare

## 📦 Instalare și Configurare

### Cerințe de Sistem
- Node.js >= 18.0.0
- PostgreSQL >= 13
- npm >= 8.0.0

### 1. Clonarea Proiectului
```bash
git clone <repository-url>
cd expense-tracker
```

### 2. Configurarea Backend-ului
```bash
cd backend
npm install
```

#### Configurarea Variabilelor de Mediu
Creează fișierul `.env` în directorul `backend`:
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/expense_tracker"

# JWT
JWT_SECRET="your-super-secret-jwt-key"
JWT_EXPIRES_IN="7d"

# Stripe
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# App
NODE_ENV="development"
PORT=5000

# CUID Prefixes (optional)
CUID_PREFIX_USER="usr"
CUID_PREFIX_CATEGORY="cat"
CUID_PREFIX_EXPENSE="exp"
```

#### Configurarea Bazei de Date
```bash
# Generarea client-ului Prisma
npm run db:generate

# Aplicarea migrărilor
npm run db:migrate

# Popularea cu date inițiale
npm run db:seed
```

### 3. Configurarea Frontend-ului
```bash
cd ../frontend
npm install
```

Creează fișierul `.env` în directorul `frontend`:
```env
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

## 🚀 Rularea Aplicației

### Dezvoltare
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm start
```

### Producție
```bash
# Backend
cd backend
npm run build
npm start

# Frontend
cd frontend
npm run build
# Servește fișierele din build/ cu un server web
```

## 🔄 Migrarea la CUID și firstName/lastName

### Noile Funcționalități

#### CUID-uri
- **Securitate îmbunătățită**: ID-urile nu mai sunt predictibile
- **Scalabilitate**: Generare distribuită fără conflicte
- **Performanță**: Optimizate pentru indexare

#### Gestionarea Numelor
- **Flexibilitate**: Prenume și nume de familie separate
- **Internaționalizare**: Suport pentru diverse culturi
- **UX îmbunătățit**: Personalizare mai bună a saluturilor

### Aplicarea Migrării

#### Pentru Aplicații Noi
```bash
cd backend
npm run db:migrate
npm run db:generate
```

#### Pentru Aplicații Existente
```bash
# 1. Backup baza de date
pg_dump -h localhost -U username -d database_name > backup.sql

# 2. Generarea CUID-urilor pentru datele existente
npm run migrate:cuid:generate

# 3. Aplicarea migrării
npm run migrate:cuid:apply

# 4. Verificarea rezultatelor
npm run db:studio
```

## 📚 Documentație API

### Autentificare
```bash
# Înregistrare
POST /api/auth/register
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "password123"
}

# Autentificare
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Utilizatori
```bash
# Profil utilizator
GET /api/users/profile
Authorization: Bearer <token>

# Actualizare profil
PUT /api/users/profile
{
  "firstName": "John",
  "lastName": "Smith",
  "email": "<EMAIL>"
}
```

### Cheltuieli
```bash
# Lista cheltuielilor
GET /api/expenses?page=1&limit=10&categoryId=<cuid>

# Creare cheltuială
POST /api/expenses
{
  "amount": 25.50,
  "description": "Cafea",
  "date": "2024-01-15",
  "categoryId": "<category-cuid>",
  "paymentMethod": "card"
}
```

## 🧪 Testare

### Backend
```bash
cd backend

# Teste unitare
npm test

# Teste de integrare
npm run test:integration

# Coverage
npm run test:coverage
```

### Frontend
```bash
cd frontend

# Teste unitare
npm test

# Teste E2E
npm run test:e2e
```

## 📁 Structura Proiectului

```
expense-tracker/
├── backend/
│   ├── src/
│   │   ├── controllers/
│   │   ├── middleware/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── services/
│   │   └── utils/
│   │       ├── cuid.js          # Utilitare CUID
│   │       └── nameHelpers.js   # Utilitare pentru nume
│   ├── prisma/
│   │   └── schema.prisma        # Schema bazei de date
│   ├── migrations/
│   │   └── 001_migrate_to_cuid_and_names.sql
│   ├── scripts/
│   │   └── generateCuids.js     # Script pentru migrare
│   └── package.json
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   ├── types/
│   │   │   └── index.ts         # Tipuri TypeScript
│   │   └── utils/
│   │       └── nameHelpers.ts   # Utilitare pentru nume
│   └── package.json
├── DATABASE_IMPROVEMENTS.md     # Documentația îmbunătățirilor
├── IMPLEMENTATION_GUIDE.md      # Ghidul de implementare
└── README.md
```

## 🔧 Utilitare și Comenzi

### Backend
```bash
# Dezvoltare
npm run dev              # Pornește serverul în modul dezvoltare
npm run dev:build        # Compilare TypeScript în watch mode

# Baza de date
npm run db:generate      # Generează client-ul Prisma
npm run db:migrate       # Aplică migrările
npm run db:seed          # Populează cu date inițiale
npm run db:studio        # Deschide Prisma Studio

# Migrare CUID
npm run migrate:cuid:generate  # Generează CUID-uri pentru migrare
npm run migrate:cuid:apply     # Aplică migrarea
npm run migrate:cuid:full      # Generează și aplică migrarea

# Stripe
npm run stripe:plans     # Inițializează planurile în Stripe
npm run stripe:webhooks  # Configurează webhook-urile

# Calitatea codului
npm run lint             # Verifică codul cu ESLint
npm run format           # Formatează codul cu Prettier
npm run type-check       # Verifică tipurile TypeScript
```

### Frontend
```bash
# Dezvoltare
npm start                # Pornește aplicația în modul dezvoltare
npm run build            # Construiește pentru producție
npm run test             # Rulează testele
npm run lint             # Verifică codul
```

## 🚨 Troubleshooting

### Probleme Comune

#### Erori de Migrare
```bash
# Resetează baza de date
npm run db:reset

# Regenerează client-ul Prisma
npm run db:generate
```

#### Probleme cu CUID-urile
```bash
# Verifică mapările ID-urilor
cat backend/migrations/id_mappings.json

# Regenerează CUID-urile
npm run migrate:cuid:generate
```

#### Probleme cu Stripe
```bash
# Verifică webhook-urile
npm run stripe:webhooks:list

# Reconfigurează webhook-urile
npm run stripe:webhooks:setup
```

## 📈 Performanță și Optimizări

### Baza de Date
- Indexuri optimizate pentru query-urile frecvente
- CUID-uri pentru performanță îmbunătățită
- Relații eficiente între tabele

### Frontend
- Lazy loading pentru componente
- Memoizare pentru calcule costisitoare
- Optimizarea bundle-ului

### Backend
- Caching cu Redis (opțional)
- Rate limiting pentru API
- Compresie pentru răspunsuri

## 🤝 Contribuții

1. Fork proiectul
2. Creează o ramură pentru feature (`git checkout -b feature/AmazingFeature`)
3. Commit modificările (`git commit -m 'Add some AmazingFeature'`)
4. Push pe ramură (`git push origin feature/AmazingFeature`)
5. Deschide un Pull Request

## 📄 Licență

Acest proiect este licențiat sub MIT License - vezi fișierul [LICENSE](LICENSE) pentru detalii.

## 📞 Suport

- **Email**: <EMAIL>
- **Documentație**: [Wiki](https://github.com/your-repo/expense-tracker/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-repo/expense-tracker/issues)

---

**Dezvoltat cu ❤️ pentru gestionarea eficientă a cheltuielilor personale.**