"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[552],{

/***/ 4552:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ admin_AdminDashboard)
});

// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(4848);
// EXTERNAL MODULE: ./node_modules/@heroicons/react/24/outline/esm/index.js + 324 modules
var esm = __webpack_require__(3740);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(6540);
// EXTERNAL MODULE: ./src/utils/helpers.ts
var helpers = __webpack_require__(3658);
// EXTERNAL MODULE: ./src/components/ui/Button.tsx
var Button = __webpack_require__(6103);
;// ./src/components/ui/AlertBanner.tsx




/**
 * Componentă pentru afișarea alertelor și notificărilor
 */
const AlertBanner = ({ type = 'info', title, message, actions = [], dismissible = false, onDismiss, className, ...props }) => {
    const getAlertStyles = (type) => {
        const styles = {
            info: {
                container: 'bg-blue-50 border-blue-200',
                icon: 'text-blue-400',
                title: 'text-blue-800',
                message: 'text-blue-700',
            },
            warning: {
                container: 'bg-yellow-50 border-yellow-200',
                icon: 'text-yellow-400',
                title: 'text-yellow-800',
                message: 'text-yellow-700',
            },
            error: {
                container: 'bg-red-50 border-red-200',
                icon: 'text-red-400',
                title: 'text-red-800',
                message: 'text-red-700',
            },
            success: {
                container: 'bg-green-50 border-green-200',
                icon: 'text-green-400',
                title: 'text-green-800',
                message: 'text-green-700',
            },
        };
        return styles[type] || styles.info;
    };
    const getIcon = (type) => {
        switch (type) {
            case 'warning':
                return ((0,jsx_runtime.jsx)("svg", { className: "w-5 h-5", fill: "currentColor", viewBox: "0 0 20 20", children: (0,jsx_runtime.jsx)("path", { fillRule: "evenodd", d: "M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z", clipRule: "evenodd" }) }));
            case 'error':
                return ((0,jsx_runtime.jsx)("svg", { className: "w-5 h-5", fill: "currentColor", viewBox: "0 0 20 20", children: (0,jsx_runtime.jsx)("path", { fillRule: "evenodd", d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z", clipRule: "evenodd" }) }));
            case 'success':
                return ((0,jsx_runtime.jsx)("svg", { className: "w-5 h-5", fill: "currentColor", viewBox: "0 0 20 20", children: (0,jsx_runtime.jsx)("path", { fillRule: "evenodd", d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z", clipRule: "evenodd" }) }));
            case 'info':
            default:
                return ((0,jsx_runtime.jsx)("svg", { className: "w-5 h-5", fill: "currentColor", viewBox: "0 0 20 20", children: (0,jsx_runtime.jsx)("path", { fillRule: "evenodd", d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z", clipRule: "evenodd" }) }));
        }
    };
    const styles = getAlertStyles(type);
    return ((0,jsx_runtime.jsx)("div", { className: (0,helpers.cn)('border rounded-lg p-4', styles.container, className), ...props, children: (0,jsx_runtime.jsxs)("div", { className: "flex", children: [(0,jsx_runtime.jsx)("div", { className: "flex-shrink-0", children: (0,jsx_runtime.jsx)("div", { className: styles.icon, children: getIcon(type) }) }), (0,jsx_runtime.jsxs)("div", { className: "ml-3 flex-1", children: [title && (0,jsx_runtime.jsx)("h3", { className: (0,helpers.cn)('text-sm font-medium', styles.title), children: title }), message && ((0,jsx_runtime.jsx)("div", { className: (0,helpers.cn)('text-sm', title ? 'mt-1' : '', styles.message), children: message })), actions.length > 0 && ((0,jsx_runtime.jsx)("div", { className: "mt-3 flex space-x-2", children: actions.map((action, index) => ((0,jsx_runtime.jsx)(Button/* default */.Ay, { variant: action.variant || 'outline', size: "sm", onClick: action.onClick, className: action.className || '', children: action.label }, index))) }))] }), dismissible && ((0,jsx_runtime.jsx)("div", { className: "ml-auto pl-3", children: (0,jsx_runtime.jsx)("div", { className: "-mx-1.5 -my-1.5", children: (0,jsx_runtime.jsxs)(Button/* default */.Ay, { variant: "ghost", size: "sm", onClick: onDismiss || (() => { }), className: (0,helpers.cn)('p-1.5', styles.icon), children: [(0,jsx_runtime.jsx)("span", { className: "sr-only", children: "\u00CEnchide" }), (0,jsx_runtime.jsx)("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: (0,jsx_runtime.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" }) })] }) }) }))] }) }));
};
/* harmony default export */ const ui_AlertBanner = ((/* unused pure expression or super */ null && (AlertBanner)));

// EXTERNAL MODULE: ./src/components/ui/Badge.tsx
var Badge = __webpack_require__(9582);
// EXTERNAL MODULE: ./src/components/ui/Card.tsx
var Card = __webpack_require__(125);
// EXTERNAL MODULE: ./src/components/ui/LoadingSpinner.tsx
var LoadingSpinner = __webpack_require__(9264);
// EXTERNAL MODULE: ./src/hooks/useAdminData.ts + 1 modules
var useAdminData = __webpack_require__(71);
// EXTERNAL MODULE: ./src/pages/admin/AdminStats.tsx
var AdminStats = __webpack_require__(8598);
// EXTERNAL MODULE: ./src/pages/admin/RevenueCharts.tsx + 1 modules
var RevenueCharts = __webpack_require__(6392);
// EXTERNAL MODULE: ./src/pages/admin/SubscriptionManager.tsx
var SubscriptionManager = __webpack_require__(714);
// EXTERNAL MODULE: ./src/pages/admin/UsersList.tsx
var UsersList = __webpack_require__(7530);
;// ./src/pages/admin/AdminDashboard.tsx













const AdminDashboard = () => {
    // Hook-uri pentru datele dashboard-ului
    const { data: dashboardStats, isLoading: statsLoading } = (0,useAdminData/* useAdminDashboardStats */.MD)();
    const { data: alerts, isLoading: alertsLoading } = (0,useAdminData/* useSystemAlerts */.U2)();
    const markAlertAsRead = (0,useAdminData/* useMarkAlertAsRead */.hU)();
    const handleDismissAlert = (alertId) => {
        markAlertAsRead.mutate(alertId);
    };
    const isLoading = statsLoading || alertsLoading;
    if (isLoading) {
        return ((0,jsx_runtime.jsx)("div", { className: "flex items-center justify-center h-64", children: (0,jsx_runtime.jsx)(LoadingSpinner/* default */.Ay, { size: "lg" }) }));
    }
    return ((0,jsx_runtime.jsxs)("div", { className: "space-y-6", children: [(0,jsx_runtime.jsxs)("div", { className: "border-b border-gray-200 pb-4", children: [(0,jsx_runtime.jsx)("h1", { className: "text-3xl font-bold text-gray-900", children: "Dashboard Administrator" }), (0,jsx_runtime.jsx)("p", { className: "text-gray-600 mt-1", children: "Monitorizarea \u0219i gestionarea platformei FinanceFlow" })] }), alerts && alerts.length > 0 && ((0,jsx_runtime.jsx)("div", { className: "mb-6 space-y-3", children: alerts.map(alert => ((0,jsx_runtime.jsx)(AlertBanner, { type: alert.type, title: alert.title, message: `${alert.message} • ${(0,helpers/* formatRelativeDate */.wI)(alert.createdAt)}`, dismissible: true, onDismiss: () => handleDismissAlert(alert.id), actions: [] }, alert.id))) })), (0,jsx_runtime.jsx)(AdminStats["default"], { dashboardStats: dashboardStats }), (0,jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 xl:grid-cols-2 gap-6", children: [(0,jsx_runtime.jsx)(RevenueCharts["default"], { dashboardStats: dashboardStats }), (0,jsx_runtime.jsxs)(Card/* default */.Ay, { className: "p-6", children: [(0,jsx_runtime.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: "Activitate recent\u0103" }), (0,jsx_runtime.jsx)("div", { className: "space-y-4", children: dashboardStats?.recentActivity?.map((activity, index) => ((0,jsx_runtime.jsxs)("div", { className: "flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0", children: [(0,jsx_runtime.jsxs)("div", { className: "flex-1", children: [(0,jsx_runtime.jsx)("p", { className: "text-sm font-medium text-gray-900", children: activity.description }), (0,jsx_runtime.jsxs)("p", { className: "text-xs text-gray-500", children: [activity.user, " \u2022 ", new Date(activity.timestamp).toLocaleString('ro-RO')] })] }), (0,jsx_runtime.jsx)(Badge/* default */.Ay, { variant: activity.type === 'error' ? 'danger' : 'secondary', children: activity.type })] }, index))) || ((0,jsx_runtime.jsx)("p", { className: "text-sm text-gray-500 text-center py-4", children: "Nu exist\u0103 activitate recent\u0103" })) })] })] }), (0,jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 xl:grid-cols-2 gap-6", children: [(0,jsx_runtime.jsx)(UsersList["default"], {}), (0,jsx_runtime.jsx)(SubscriptionManager["default"], {})] }), (0,jsx_runtime.jsxs)(Card/* default */.Ay, { className: "p-6", children: [(0,jsx_runtime.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: "Ac\u021Biuni rapide" }), (0,jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [(0,jsx_runtime.jsxs)("button", { className: "flex items-center justify-center px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors", children: [(0,jsx_runtime.jsx)(esm/* UsersIcon */.c2u, { className: "h-5 w-5 mr-2" }), "Gestioneaz\u0103 utilizatori"] }), (0,jsx_runtime.jsxs)("button", { className: "flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors", children: [(0,jsx_runtime.jsx)(esm/* CurrencyDollarIcon */.xmO, { className: "h-5 w-5 mr-2" }), "Export venituri"] }), (0,jsx_runtime.jsxs)("button", { className: "flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors", children: [(0,jsx_runtime.jsx)(esm/* ChartBarIcon */.r95, { className: "h-5 w-5 mr-2" }), "Raport complet"] })] })] })] }));
};
/* harmony default export */ const admin_AdminDashboard = (AdminDashboard);


/***/ })

}]);