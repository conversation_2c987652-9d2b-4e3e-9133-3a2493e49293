/**
 * Teste pentru sincronizarea tipurilor TypeScript cu schema Prisma
 */

import { PrismaClient } from '@prisma/client';
import { User, Category, Expense, Subscription, SubscriptionPlan, UsageLog, WebhookEvent } from '../../../src/types';

const prisma = new PrismaClient();

describe('Type Synchronization with Prisma Schema', () => {
  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('User Type', () => {
    it('should have all required fields matching Prisma schema', () => {
      const userFields: (keyof User)[] = [
        'id',
        'email',
        'firstName',
        'lastName',
        'password',
        'avatar',
        'role',
        'currency',
        'timezone',
        'isActive',
        'emailVerified',
        'emailVerificationToken',
        'passwordResetToken',
        'passwordResetExpires',
        'lastLogin',
        'loginCount',
        'preferences',
        'lastUsageReset',
        'monthlyExpenseCount',
        'monthlyExpenseLimit',
        'planType',
        'stripeCustomerId',
        'subscriptionCurrentPeriodEnd',
        'subscriptionCurrentPeriodStart',
        'subscriptionId',
        'subscriptionStatus',
        'trialEndsAt',
        'refreshToken',
        'createdAt',
        'updatedAt',
        'subscription',
        'expenses',
        'categories',
        'usageLogs'
      ];

      // Verifică că toate câmpurile sunt definite în tipul User
      userFields.forEach(field => {
        expect(field).toBeDefined();
      });
    });

    it('should have correct enum values for role', () => {
      const validRoles: User['role'][] = ['user', 'admin'];
      expect(validRoles).toContain('user');
      expect(validRoles).toContain('admin');
    });

    it('should have correct enum values for planType', () => {
      const validPlanTypes: User['planType'][] = ['free', 'basic', 'premium'];
      expect(validPlanTypes).toContain('free');
      expect(validPlanTypes).toContain('basic');
      expect(validPlanTypes).toContain('premium');
    });
  });

  describe('Category Type', () => {
    it('should have all required fields matching Prisma schema', () => {
      const categoryFields: (keyof Category)[] = [
        'id',
        'name',
        'description',
        'color',
        'icon',
        'budgetLimit',
        'budgetPeriod',
        'isActive',
        'isDefault',
        'sortOrder',
        'userId',
        'createdAt',
        'updatedAt',
        'user',
        'expenses'
      ];

      categoryFields.forEach(field => {
        expect(field).toBeDefined();
      });
    });

    it('should have correct enum values for budgetPeriod', () => {
      const validPeriods: Category['budgetPeriod'][] = ['daily', 'weekly', 'monthly', 'yearly'];
      expect(validPeriods).toContain('daily');
      expect(validPeriods).toContain('weekly');
      expect(validPeriods).toContain('monthly');
      expect(validPeriods).toContain('yearly');
    });
  });

  describe('Expense Type', () => {
    it('should have all required fields matching Prisma schema', () => {
      const expenseFields: (keyof Expense)[] = [
        'id',
        'amount',
        'description',
        'date',
        'notes',
        'paymentMethod',
        'location',
        'receiptUrl',
        'tags',
        'isRecurring',
        'recurringFrequency',
        'recurringEndDate',
        'originalExpenseId',
        'userId',
        'categoryId',
        'createdAt',
        'updatedAt',
        'user',
        'category',
        'originalExpense',
        'recurringExpenses'
      ];

      expenseFields.forEach(field => {
        expect(field).toBeDefined();
      });
    });

    it('should have correct enum values for paymentMethod', () => {
      const validMethods: Expense['paymentMethod'][] = [
        'cash', 'card', 'bank_transfer', 'digital_wallet', 'other'
      ];
      expect(validMethods).toContain('cash');
      expect(validMethods).toContain('card');
      expect(validMethods).toContain('bank_transfer');
      expect(validMethods).toContain('digital_wallet');
      expect(validMethods).toContain('other');
    });
  });

  describe('DTO Types', () => {
    it('should have CreateExpenseDto with all required fields', () => {
      const createExpenseDto = {
        amount: 100,
        description: 'Test expense',
        categoryId: 'test-category-id',
        date: '2023-01-01',
        notes: 'Test notes',
        paymentMethod: 'card' as const,
        location: 'Test location',
        receiptUrl: 'https://example.com/receipt.jpg',
        tags: ['test', 'expense'],
        isRecurring: false,
        recurringFrequency: 'monthly' as const,
        recurringEndDate: '2023-12-31'
      };

      expect(createExpenseDto.amount).toBe(100);
      expect(createExpenseDto.description).toBe('Test expense');
      expect(createExpenseDto.categoryId).toBe('test-category-id');
      expect(createExpenseDto.paymentMethod).toBe('card');
    });

    it('should have RegisterDto with firstName and lastName', () => {
      const registerDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        currency: 'USD',
        timezone: 'UTC'
      };

      expect(registerDto.firstName).toBe('John');
      expect(registerDto.lastName).toBe('Doe');
      expect(registerDto.email).toBe('<EMAIL>');
    });
  });

  describe('API Response Types', () => {
    it('should have correct ApiResponse structure', () => {
      const successResponse = {
        success: true,
        data: { id: '1', name: 'Test' },
        message: 'Success'
      };

      const errorResponse = {
        success: false,
        error: 'Something went wrong'
      };

      expect(successResponse.success).toBe(true);
      expect(successResponse.data).toBeDefined();
      expect(errorResponse.success).toBe(false);
      expect(errorResponse.error).toBeDefined();
    });

    it('should have correct PaginatedResponse structure', () => {
      const paginatedResponse = {
        data: [{ id: '1', name: 'Test' }],
        pagination: {
          page: 1,
          limit: 10,
          total: 100,
          totalPages: 10
        }
      };

      expect(paginatedResponse.data).toHaveLength(1);
      expect(paginatedResponse.pagination.page).toBe(1);
      expect(paginatedResponse.pagination.totalPages).toBe(10);
    });
  });

  describe('Utility Types', () => {
    it('should have correct ValidationError structure', () => {
      const validationError = {
        field: 'email',
        message: 'Email is required',
        value: ''
      };

      expect(validationError.field).toBe('email');
      expect(validationError.message).toBe('Email is required');
    });

    it('should have correct AuthenticatedRequest structure', () => {
      // Verifică că tipul AuthenticatedRequest extinde Request corect
      const mockRequest = {
        user: {
          id: 'user-id',
          email: '<EMAIL>',
          subscription: {
            planId: 'plan-id',
            status: 'active'
          }
        }
      };

      expect(mockRequest.user?.id).toBe('user-id');
      expect(mockRequest.user?.subscription?.status).toBe('active');
    });
  });
});
