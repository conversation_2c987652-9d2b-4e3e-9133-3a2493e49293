"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[560],{

/***/ 8179:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8035);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(6103);
/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(125);
/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(9264);
/* harmony import */ var _hooks_useExpenses__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(2948);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(3658);









// Mock data pentru demonstrație
const mockReportData = {
    totalExpenses: 1250.75,
    totalIncome: 2500.0,
    savings: 1249.25,
    monthlyData: [
        { month: 'Ian', expenses: 800, income: 2500 },
        { month: 'Feb', expenses: 950, income: 2500 },
        { month: 'Mar', expenses: 1100, income: 2500 },
        { month: 'Apr', expenses: 750, income: 2500 },
        { month: 'Mai', expenses: 1250, income: 2500 },
        { month: 'Iun', expenses: 900, income: 2500 },
    ],
    categoryBreakdown: [
        { name: 'Mâncare', amount: 450.25, percentage: 36 },
        { name: 'Transport', amount: 200.0, percentage: 16 },
        { name: 'Utilități', amount: 150.0, percentage: 12 },
        { name: 'Divertisment', amount: 100.0, percentage: 8 },
        { name: 'Altele', amount: 350.5, percentage: 28 },
    ],
    topExpenses: [
        {
            id: 'exp_1',
            description: 'Cumpărături supermarket',
            amount: 85.5,
            date: '2024-01-15',
            category: 'Mâncare',
        },
        {
            id: 'exp_2',
            description: 'Combustibil',
            amount: 75.0,
            date: '2024-01-14',
            category: 'Transport',
        },
        {
            id: 'exp_3',
            description: 'Factură electricitate',
            amount: 65.0,
            date: '2024-01-13',
            category: 'Utilități',
        },
        {
            id: 'exp_4',
            description: 'Cinema',
            amount: 45.0,
            date: '2024-01-12',
            category: 'Divertisment',
        },
        {
            id: 'exp_5',
            description: 'Restaurant',
            amount: 120.0,
            date: '2024-01-11',
            category: 'Mâncare',
        },
    ],
};
const Reports = () => {
    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('thisMonth');
    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('all');
    // Hook pentru export
    const exportExpensesMutation = (0,_hooks_useExpenses__WEBPACK_IMPORTED_MODULE_7__/* .useExportExpenses */ .Rj)();
    // Simulare query pentru rapoarte
    const { data: reportData, isLoading, error, } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__/* .useQuery */ .pw)({
        queryKey: ['reports', selectedPeriod, selectedCategory],
        queryFn: async () => {
            // Simulare API call
            await new Promise(resolve => setTimeout(resolve, 800));
            return mockReportData;
        },
    });
    const handleExportReport = async (format) => {
        try {
            // Pregătește parametrii pentru export bazați pe filtrele curente
            // Aici ar trebui să existe o logică pentru a converti `selectedPeriod` în `dateFrom` și `dateTo`
            const exportParams = {
                // dateFrom: ...,
                // dateTo: ...,
                categoryId: selectedCategory !== 'all' ? selectedCategory : undefined,
            };
            // Apelează funcția de export
            await exportExpensesMutation.mutateAsync({ format, params: exportParams });
        }
        catch (error) {
            // Eroarea este gestionată în hook
            console.error('Export failed:', error);
        }
    };
    const periodOptions = [
        { value: 'thisWeek', label: 'Această săptămână' },
        { value: 'thisMonth', label: 'Această lună' },
        { value: 'lastMonth', label: 'Luna trecută' },
        { value: 'thisYear', label: 'Acest an' },
        { value: 'custom', label: 'Perioadă personalizată' },
    ];
    const categoryOptions = [
        { value: 'all', label: 'Toate categoriile' },
        { value: 'food', label: 'Mâncare' },
        { value: 'transport', label: 'Transport' },
        { value: 'utilities', label: 'Utilități' },
        { value: 'entertainment', label: 'Divertisment' },
    ];
    if (error) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center py-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-red-600", children: "Eroare la \u00EEnc\u0103rcarea raportului" }) }));
    }
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col sm:flex-row sm:items-center sm:justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1", { className: "text-2xl font-bold text-gray-900", children: "Rapoarte" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: "Analizeaz\u0103 cheltuielile \u0219i tendin\u021Bele financiare" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mt-4 sm:mt-0 flex space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", onClick: () => handleExportReport('csv'), disabled: exportExpensesMutation.isPending, className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DocumentArrowDownIcon */ .A50, { className: "h-5 w-5 mr-2" }), exportExpensesMutation.isPending ? 'Se exportă...' : 'Export CSV'] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", onClick: () => handleExportReport('pdf'), disabled: exportExpensesMutation.isPending, className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DocumentArrowDownIcon */ .A50, { className: "h-5 w-5 mr-2" }), exportExpensesMutation.isPending ? 'Se exportă...' : 'Export PDF'] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", onClick: () => handleExportReport('excel'), disabled: exportExpensesMutation.isPending, className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DocumentArrowDownIcon */ .A50, { className: "h-5 w-5 mr-2" }), exportExpensesMutation.isPending ? 'Se exportă...' : 'Export Excel'] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-4 sm:space-y-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .FunnelIcon */ .mWh, { className: "h-5 w-5 text-gray-400" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium text-gray-700", children: "Filtreaz\u0103:" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col sm:flex-row sm:space-x-4 space-y-4 sm:space-y-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Perioada" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("select", { value: selectedPeriod, onChange: e => setSelectedPeriod(e.target.value), className: "px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500", children: periodOptions.map(option => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: option.value, children: option.label }, option.value))) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Categoria" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("select", { value: selectedCategory, onChange: e => setSelectedCategory(e.target.value), className: "px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500", children: categoryOptions.map(option => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: option.value, children: option.label }, option.value))) })] })] })] }) }), isLoading ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center justify-center py-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { size: "lg" }) })) : reportData ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "p-2 bg-red-100 rounded-lg", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChartBarIcon */ .r95, { className: "h-6 w-6 text-red-600" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "ml-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-600", children: "Total cheltuieli" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-2xl font-bold text-gray-900", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_8__/* .formatCurrency */ .vv)(reportData.totalExpenses) })] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "p-2 bg-green-100 rounded-lg", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChartBarIcon */ .r95, { className: "h-6 w-6 text-green-600" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "ml-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-600", children: "Total venituri" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-2xl font-bold text-gray-900", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_8__/* .formatCurrency */ .vv)(reportData.totalIncome) })] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "p-2 bg-blue-100 rounded-lg", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChartBarIcon */ .r95, { className: "h-6 w-6 text-blue-600" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "ml-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-600", children: "Economii" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-2xl font-bold text-gray-900", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_8__/* .formatCurrency */ .vv)(reportData.savings) })] })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-gray-900 mb-4", children: "Tendin\u021Ba lunar\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4", children: reportData.monthlyData.map((month, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-12 text-sm font-medium text-gray-600", children: month.month }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-1 bg-gray-200 rounded-full h-4 relative", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-red-500 h-4 rounded-full", style: { width: `${(month.expenses / month.income) * 100}%` } }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-gray-600 w-20", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_8__/* .formatCurrency */ .vv)(month.expenses) })] }) })] }, index))) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-gray-900 mb-4", children: "Cheltuieli pe categorii" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4", children: reportData.categoryBreakdown.map((category, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-3 h-3 bg-blue-500 rounded-full" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium text-gray-900", children: category.name })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "text-sm text-gray-600", children: [category.percentage, "%"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium text-gray-900", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_8__/* .formatCurrency */ .vv)(category.amount) })] })] }, index))) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-gray-900 mb-4", children: "Top cheltuieli" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4", children: reportData.topExpenses.map(expense => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-900", children: expense.description }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-xs text-gray-500", children: [expense.category, " \u2022 ", expense.date] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium text-gray-900", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_8__/* .formatCurrency */ .vv)(expense.amount) })] }, expense.id))) })] })] })] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-12 text-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-500", children: "Nu sunt date disponibile pentru perioada selectat\u0103" }) }))] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Reports);


/***/ })

}]);