"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[725],{

/***/ 1939:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

/* harmony import */ var workbox_precaching__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(9135);
/* harmony import */ var workbox_routing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2491);
/* harmony import */ var workbox_strategies__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1767);
/* harmony import */ var workbox_expiration__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1015);
/* harmony import */ var workbox_background_sync__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1325);
// Service Worker pentru caching și offline support
/// <reference lib="webworker" />






// Detectează dacă suntem în modul de dezvoltare
const isDevelopment = !self.__WB_MANIFEST || !Array.isArray(self.__WB_MANIFEST) || self.__WB_MANIFEST.length === 0;
// Precache all static assets
// În modul de dezvoltare, __WB_MANIFEST poate să nu fie disponibil
if (self.__WB_MANIFEST && Array.isArray(self.__WB_MANIFEST)) {
    (0,workbox_precaching__WEBPACK_IMPORTED_MODULE_0__/* .precacheAndRoute */ .ok)(self.__WB_MANIFEST);
}
else {
    // În modul de dezvoltare, folosim o listă goală
    (0,workbox_precaching__WEBPACK_IMPORTED_MODULE_0__/* .precacheAndRoute */ .ok)([]);
}
// Clean up old caches
(0,workbox_precaching__WEBPACK_IMPORTED_MODULE_0__/* .cleanupOutdatedCaches */ .Up)();
// Cache API responses
(0,workbox_routing__WEBPACK_IMPORTED_MODULE_1__/* .registerRoute */ .R6)(({ url }) => url.pathname.startsWith('/api/'), new workbox_strategies__WEBPACK_IMPORTED_MODULE_2__/* .NetworkFirst */ .dq({
    cacheName: 'api-cache',
    plugins: [
        new workbox_expiration__WEBPACK_IMPORTED_MODULE_3__/* .ExpirationPlugin */ .V({
            maxEntries: 100,
            maxAgeSeconds: 5 * 60, // 5 minute
        }),
    ],
}));
// Cache images
(0,workbox_routing__WEBPACK_IMPORTED_MODULE_1__/* .registerRoute */ .R6)(({ request }) => request.destination === 'image', new workbox_strategies__WEBPACK_IMPORTED_MODULE_2__/* .CacheFirst */ .h6({
    cacheName: 'images-cache',
    plugins: [
        new workbox_expiration__WEBPACK_IMPORTED_MODULE_3__/* .ExpirationPlugin */ .V({
            maxEntries: 50,
            maxAgeSeconds: 30 * 24 * 60 * 60, // 30 zile
        }),
    ],
}));
// Cache fonts
(0,workbox_routing__WEBPACK_IMPORTED_MODULE_1__/* .registerRoute */ .R6)(({ request }) => request.destination === 'font', new workbox_strategies__WEBPACK_IMPORTED_MODULE_2__/* .CacheFirst */ .h6({
    cacheName: 'fonts-cache',
    plugins: [
        new workbox_expiration__WEBPACK_IMPORTED_MODULE_3__/* .ExpirationPlugin */ .V({
            maxEntries: 10,
            maxAgeSeconds: 365 * 24 * 60 * 60, // 1 year
        }),
    ],
}));
// Cache CSS and JS files
(0,workbox_routing__WEBPACK_IMPORTED_MODULE_1__/* .registerRoute */ .R6)(({ request }) => request.destination === 'style' ||
    request.destination === 'script', new workbox_strategies__WEBPACK_IMPORTED_MODULE_2__/* .StaleWhileRevalidate */ .kV({
    cacheName: 'static-resources',
    plugins: [
        new workbox_expiration__WEBPACK_IMPORTED_MODULE_3__/* .ExpirationPlugin */ .V({
            maxEntries: 30,
            maxAgeSeconds: 7 * 24 * 60 * 60, // 7 days
        }),
    ],
}));
// Background sync for failed API requests
let bgSyncPlugin = null;
let offlineQueue = null;
// Initialize background sync and queue only in production mode
if (!isDevelopment) {
    try {
        bgSyncPlugin = new workbox_background_sync__WEBPACK_IMPORTED_MODULE_4__/* .BackgroundSyncPlugin */ .tU('api-queue', {
            maxRetentionTime: 24 * 60, // 24 hours
        });
    }
    catch (error) {
        console.warn('Failed to initialize background sync plugin:', error);
    }
    try {
        offlineQueue = new workbox_background_sync__WEBPACK_IMPORTED_MODULE_4__/* .Queue */ .op('offline-actions', {
            onSync: async ({ queue }) => {
                let entry;
                while ((entry = await queue.shiftRequest())) {
                    try {
                        await fetch(entry.request);
                        console.log('Offline action synced:', entry.request.url);
                    }
                    catch (error) {
                        console.error('Failed to sync offline action:', error);
                        await queue.unshiftRequest(entry);
                        throw error;
                    }
                }
            },
        });
    }
    catch (error) {
        console.warn('Failed to initialize offline queue:', error);
    }
}
else {
    console.log('Development mode: Skipping background sync and queue initialization');
}
// Handle offline expense creation (only in production)
if (!isDevelopment) {
    (0,workbox_routing__WEBPACK_IMPORTED_MODULE_1__/* .registerRoute */ .R6)(({ url, request }) => url.pathname.startsWith('/api/expenses') &&
        request.method === 'POST', async ({ request }) => {
        try {
            return await fetch(request);
        }
        catch (error) {
            // Add to offline queue if available
            if (offlineQueue) {
                await offlineQueue.pushRequest({ request });
            }
            // Return a response indicating offline storage
            return new Response(JSON.stringify({
                success: true,
                offline: true,
                message: 'Expense saved offline. Will sync when online.',
            }), {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
            });
        }
    });
}
// Handle app updates
self.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});
// Notify clients about updates
self.addEventListener('activate', (event) => {
    event.waitUntil((async () => {
        // Claim all clients
        await self.clients.claim();
        // Notify clients about the update
        const clients = await self.clients.matchAll();
        clients.forEach((client) => {
            client.postMessage({
                type: 'SW_ACTIVATED',
                message: 'Service Worker activated',
            });
        });
    })());
});
// Handle push notifications (for future implementation)
self.addEventListener('push', (event) => {
    if (!event.data)
        return;
    const data = event.data.json();
    const options = {
        body: data.body,
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png',
        tag: data.tag || 'default',
        data: data.data,
        actions: data.actions || [],
        requireInteraction: data.requireInteraction || false,
    };
    event.waitUntil(self.registration.showNotification(data.title, options));
});
// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    if (event.action) {
        // Handle action buttons
        switch (event.action) {
            case 'view':
                event.waitUntil(self.clients.openWindow(event.notification.data?.url || '/'));
                break;
            case 'dismiss':
                // Just close the notification
                break;
        }
    }
    else {
        // Handle notification click
        event.waitUntil(self.clients.openWindow(event.notification.data?.url || '/'));
    }
});
// Periodic background sync (for future implementation)
self.addEventListener('periodicsync', (event) => {
    if (event.tag === 'sync-expenses') {
        event.waitUntil(syncExpenses());
    }
});
async function syncExpenses() {
    try {
        // Sync any pending offline actions if queue is available (only in production)
        if (!isDevelopment && offlineQueue) {
            await offlineQueue.replayRequests();
        }
        // Fetch latest data
        const response = await fetch('/api/expenses?limit=10');
        if (response.ok) {
            const data = await response.json();
            // Update cache with latest data
            const cache = await caches.open('api-cache');
            await cache.put('/api/expenses?limit=10', response.clone());
        }
    }
    catch (error) {
        console.error('Background sync failed:', error);
    }
}
// Handle install event
self.addEventListener('install', (event) => {
    console.log('Service Worker installing...');
    // Skip waiting to activate immediately
    event.waitUntil(self.skipWaiting());
});
// Log cache usage for debugging
self.addEventListener('fetch', (event) => {
    if (event.request.url.includes('/api/')) {
        console.log('SW: Handling API request:', event.request.url);
    }
});
// Error handling
self.addEventListener('error', (event) => {
    console.error('Service Worker error:', event.error);
});
self.addEventListener('unhandledrejection', (event) => {
    console.error('Service Worker unhandled rejection:', event.reason);
});


/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, [96], () => (__webpack_exec__(1939)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);