"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[834],{

/***/ 5834:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ product_Pricing)
});

// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(4848);
// EXTERNAL MODULE: ./node_modules/@heroicons/react/24/outline/esm/index.js + 324 modules
var esm = __webpack_require__(3740);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(6540);
// EXTERNAL MODULE: ./node_modules/react-i18next/dist/es/index.js + 15 modules
var es = __webpack_require__(2389);
// EXTERNAL MODULE: ./node_modules/react-router-dom/dist/index.js
var dist = __webpack_require__(4976);
// EXTERNAL MODULE: ./node_modules/react-hot-toast/dist/index.mjs + 1 modules
var react_hot_toast_dist = __webpack_require__(888);
// EXTERNAL MODULE: ./src/components/layout/PublicLayout.tsx
var PublicLayout = __webpack_require__(4370);
// EXTERNAL MODULE: ./node_modules/@tanstack/react-query/build/modern/index.js + 21 modules
var modern = __webpack_require__(8035);
// EXTERNAL MODULE: ./src/services/api.ts + 1 modules
var api = __webpack_require__(4768);
;// ./src/services/subscriptionService.ts

/**
 * Serviciu pentru gestionarea abonamentelor utilizatorilor finali
 */
class SubscriptionService {
    /**
     * Obține planurile disponibile
     */
    async getPlans() {
        try {
            const response = await api/* default */.A.get('/subscriptions/plans');
            return response.data;
        }
        catch (error) {
            console.error('Eroare la obținerea planurilor:', error);
            throw error;
        }
    }
    /**
     * Obține abonamentul curent al utilizatorului
     */
    async getCurrentSubscription() {
        try {
            const response = await api/* default */.A.get('/subscriptions/current');
            return response.data;
        }
        catch (error) {
            console.error('Eroare la obținerea abonamentului curent:', error);
            throw error;
        }
    }
    /**
     * Creează o sesiune de checkout pentru un plan
     */
    async createCheckoutSession(planId, billingCycle = 'monthly', successUrl, cancelUrl) {
        try {
            const response = await api/* default */.A.post('/subscriptions/checkout', {
                planId,
                billingCycle,
                successUrl,
                cancelUrl
            });
            return response.data;
        }
        catch (error) {
            console.error('Eroare la crearea sesiunii de checkout:', error);
            throw error;
        }
    }
    /**
     * Creează un portal pentru gestionarea abonamentului
     */
    async createCustomerPortal(returnUrl) {
        try {
            const response = await api/* default */.A.post('/subscriptions/portal', {
                returnUrl
            });
            return response.data;
        }
        catch (error) {
            console.error('Eroare la crearea portalului de clienți:', error);
            throw error;
        }
    }
    /**
     * Anulează abonamentul curent
     */
    async cancelSubscription(reason = '') {
        try {
            const response = await api/* default */.A.post('/subscriptions/cancel', {
                reason
            });
            return response.data;
        }
        catch (error) {
            console.error('Eroare la anularea abonamentului:', error);
            throw error;
        }
    }
    /**
     * Reactivează un abonament anulat
     */
    async reactivateSubscription() {
        try {
            const response = await api/* default */.A.post('/subscriptions/reactivate');
            return response.data;
        }
        catch (error) {
            console.error('Eroare la reactivarea abonamentului:', error);
            throw error;
        }
    }
    /**
     * Obține statisticile de utilizare ale utilizatorului
     */
    async getUsageStats() {
        try {
            const response = await api/* default */.A.get('/subscriptions/usage');
            return response.data;
        }
        catch (error) {
            console.error('Eroare la obținerea statisticilor de utilizare:', error);
            throw error;
        }
    }
    /**
     * Verifică statusul unei sesiuni de checkout
     */
    async checkCheckoutSession(sessionId) {
        try {
            const response = await api/* default */.A.get(`/subscriptions/checkout/${sessionId}`);
            return response.data;
        }
        catch (error) {
            console.error('Eroare la verificarea sesiunii de checkout:', error);
            throw error;
        }
    }
    /**
     * Verifică permisiunile utilizatorului pentru o anumită acțiune
     */
    async checkPermission(action) {
        try {
            const response = await api/* default */.A.get(`/subscriptions/permissions/${action}`);
            return response.data;
        }
        catch (error) {
            console.error('Eroare la verificarea permisiunilor:', error);
            throw error;
        }
    }
}
const subscriptionService_subscriptionService = new SubscriptionService();
/* harmony default export */ const services_subscriptionService = (subscriptionService_subscriptionService);

;// ./src/hooks/useSubscription.ts



/**
 * Hook pentru obținerea planurilor disponibile
 */
function usePlans() {
    return (0,modern/* useQuery */.pw)({
        queryKey: ['plans'],
        queryFn: services_subscriptionService.getPlans,
        staleTime: 10 * 60 * 1000, // 10 minute
        gcTime: 30 * 60 * 1000, // 30 minute (renamed from cacheTime)
    });
}
/**
 * Hook pentru obținerea abonamentului curent
 */
function useCurrentSubscription() {
    return (0,modern/* useQuery */.pw)({
        queryKey: ['subscription', 'current'],
        queryFn: services_subscriptionService.getCurrentSubscription,
        staleTime: 5 * 60 * 1000, // 5 minute
        gcTime: 15 * 60 * 1000, // 15 minute
        retry: (failureCount, error) => {
            // Nu reîncerca dacă utilizatorul nu are abonament
            if (error?.response?.status === 404) {
                return false;
            }
            return failureCount < 3;
        },
    });
}
/**
 * Hook pentru obținerea statisticilor de utilizare
 */
function useUsageStats() {
    return useQuery({
        queryKey: ['subscription', 'usage'],
        queryFn: subscriptionService.getUsageStats,
        staleTime: 2 * 60 * 1000, // 2 minute
        gcTime: 10 * 60 * 1000, // 10 minute
    });
}
/**
 * Hook pentru crearea unei sesiuni de checkout
 */
function useCreateCheckoutSession() {
    return (0,modern/* useMutation */.n_)({
        mutationFn: ({ planId, billingCycle, successUrl, cancelUrl }) => services_subscriptionService.createCheckoutSession(planId, billingCycle, successUrl, cancelUrl),
        onSuccess: data => {
            // Redirecționează către Stripe Checkout
            if (data.data?.url) {
                window.location.href = data.data.url;
            }
        },
        onError: (error) => {
            react_hot_toast_dist/* toast */.oR.error(error.response?.data?.message ||
                'Eroare la crearea sesiunii de checkout. Vă rugăm să încercați din nou.');
        },
    });
}
/**
 * Hook pentru crearea portalului de clienți
 */
function useCreateCustomerPortal() {
    return useMutation({
        mutationFn: (returnUrl) => subscriptionService.createCustomerPortal(returnUrl),
        onSuccess: data => {
            // Redirecționează către Stripe Customer Portal
            if (data.data?.url) {
                window.location.href = data.data.url;
            }
        },
        onError: (error) => {
            toast.error(error.response?.data?.message ||
                'Eroare la accesarea portalului de gestionare. Vă rugăm să încercați din nou.');
        },
    });
}
/**
 * Hook pentru anularea abonamentului
 */
function useCancelSubscription() {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (reason = '') => subscriptionService.cancelSubscription(reason),
        onSuccess: () => {
            // Invalidează cache-ul pentru abonamentul curent
            queryClient.invalidateQueries({ queryKey: ['subscription', 'current'] });
            queryClient.invalidateQueries({ queryKey: ['subscription', 'usage'] });
            toast.success('Abonamentul a fost anulat cu succes.');
        },
        onError: (error) => {
            toast.error(error.response?.data?.message ||
                'Eroare la anularea abonamentului. Vă rugăm să încercați din nou.');
        },
    });
}
/**
 * Hook pentru reactivarea abonamentului
 */
function useReactivateSubscription() {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: () => subscriptionService.reactivateSubscription(),
        onSuccess: () => {
            // Invalidează cache-ul pentru abonamentul curent
            queryClient.invalidateQueries({ queryKey: ['subscription', 'current'] });
            queryClient.invalidateQueries({ queryKey: ['subscription', 'usage'] });
            toast.success('Abonamentul a fost reactivat cu succes.');
        },
        onError: (error) => {
            toast.error(error.response?.data?.message ||
                'Eroare la reactivarea abonamentului. Vă rugăm să încercați din nou.');
        },
    });
}
/**
 * Hook pentru verificarea statusului unei sesiuni de checkout
 */
function useCheckCheckoutSession(sessionId) {
    return useQuery({
        queryKey: ['checkout', 'session', sessionId],
        queryFn: () => subscriptionService.checkCheckoutSession(sessionId),
        enabled: !!sessionId,
        staleTime: 0, // Verifică mereu statusul cel mai recent
        gcTime: 5 * 60 * 1000, // 5 minute
        refetchInterval: query => {
            // Oprește polling-ul dacă sesiunea este completă sau expirată
            const sessionData = query?.state?.data;
            if (sessionData?.data?.status === 'complete' || sessionData?.data?.status === 'expired') {
                return false;
            }
            return 5000; // Verifică la fiecare 5 secunde
        },
    });
}
/**
 * Hook pentru verificarea permisiunilor
 */
function useCheckPermission(action) {
    return useQuery({
        queryKey: ['subscription', 'permissions', action],
        queryFn: () => subscriptionService.checkPermission(action),
        enabled: !!action,
        staleTime: 5 * 60 * 1000, // 5 minute
        gcTime: 15 * 60 * 1000, // 15 minute
    });
}
/**
 * Hook compus pentru gestionarea completă a abonamentului
 */
function useSubscriptionManager() {
    const plans = usePlans();
    const currentSubscription = useCurrentSubscription();
    const usageStats = useUsageStats();
    const createCheckoutSession = useCreateCheckoutSession();
    const createCustomerPortal = useCreateCustomerPortal();
    const cancelSubscription = useCancelSubscription();
    const reactivateSubscription = useReactivateSubscription();
    return {
        // Queries
        plans,
        currentSubscription,
        usageStats,
        // Mutations
        createCheckoutSession,
        createCustomerPortal,
        cancelSubscription,
        reactivateSubscription,
        // Computed values
        isLoading: plans.isLoading || currentSubscription.isLoading,
        hasActiveSubscription: currentSubscription.data?.data?.status === 'active',
        canUpgrade: currentSubscription.data?.data?.plan?.name !== 'Business',
        isFreePlan: !currentSubscription.data?.data || currentSubscription.data?.data?.plan?.name === 'Free',
    };
}

// EXTERNAL MODULE: ./src/store/authStore.ts + 2 modules
var authStore = __webpack_require__(250);
;// ./src/pages/product/Pricing.tsx









const Pricing = () => {
    const { t } = (0,es/* useTranslation */.Bd)();
    const navigate = (0,dist/* useNavigate */.Zp)();
    const [billingCycle, setBillingCycle] = (0,react.useState)('monthly'); // 'monthly' or 'yearly'
    // Hooks pentru autentificare și abonamente
    const { user, isAuthenticated } = (0,authStore/* useAuthStore */.nc)((state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
    }));
    const { data: plansFromApi, isLoading: plansLoading } = usePlans();
    const { data: currentSubscription } = useCurrentSubscription();
    const createCheckoutSession = useCreateCheckoutSession();
    // Folosește planurile din API sau fallback la planurile statice
    const staticPlans = [
        {
            id: 'free',
            name: t('pricing.plans.free.name', 'Gratuit'),
            description: t('pricing.plans.free.description', 'Perfect pentru începători'),
            icon: esm/* UserIcon */.nys,
            price: {
                monthly: 0,
                yearly: 0,
            },
            features: [
                t('pricing.plans.free.feature1', 'Până la 3 conturi bancare'),
                t('pricing.plans.free.feature2', '100 tranzacții/lună'),
                t('pricing.plans.free.feature3', 'Rapoarte de bază'),
                t('pricing.plans.free.feature4', 'Suport email'),
                t('pricing.plans.free.feature5', 'Aplicație mobilă'),
            ],
            limitations: [
                t('pricing.plans.free.limitation1', 'Fără export date'),
                t('pricing.plans.free.limitation2', 'Fără analiză avansată'),
                t('pricing.plans.free.limitation3', 'Fără integrări terțe părți'),
            ],
            cta: t('pricing.plans.free.cta', 'Începeți Gratuit'),
            popular: false,
        },
        {
            id: 'personal',
            name: t('pricing.plans.personal.name', 'Personal'),
            description: t('pricing.plans.personal.description', 'Pentru utilizatori activi'),
            icon: esm/* UserIcon */.nys,
            price: {
                monthly: 9.99,
                yearly: 99.99,
            },
            features: [
                t('pricing.plans.personal.feature1', 'Conturi bancare nelimitate'),
                t('pricing.plans.personal.feature2', 'Tranzacții nelimitate'),
                t('pricing.plans.personal.feature3', 'Analiză avansată'),
                t('pricing.plans.personal.feature4', 'Export în toate formatele'),
                t('pricing.plans.personal.feature5', 'Bugetare inteligentă'),
                t('pricing.plans.personal.feature6', 'Notificări personalizate'),
                t('pricing.plans.personal.feature7', 'Suport prioritar'),
                t('pricing.plans.personal.feature8', 'Backup automat'),
            ],
            limitations: [],
            cta: t('pricing.plans.personal.cta', 'Alegeți Personal'),
            popular: true,
        },
        {
            id: 'family',
            name: t('pricing.plans.family.name', 'Familie'),
            description: t('pricing.plans.family.description', 'Pentru familii și cupluri'),
            icon: esm/* UserGroupIcon */.K6s,
            price: {
                monthly: 19.99,
                yearly: 199.99,
            },
            features: [
                t('pricing.plans.family.feature1', 'Toate funcționalitățile Personal'),
                t('pricing.plans.family.feature2', 'Până la 6 utilizatori'),
                t('pricing.plans.family.feature3', 'Bugete partajate'),
                t('pricing.plans.family.feature4', 'Permisiuni granulare'),
                t('pricing.plans.family.feature5', 'Rapoarte familiale'),
                t('pricing.plans.family.feature6', 'Obiective comune'),
                t('pricing.plans.family.feature7', 'Chat integrat'),
                t('pricing.plans.family.feature8', 'Suport telefonic'),
            ],
            limitations: [],
            cta: t('pricing.plans.family.cta', 'Alegeți Familie'),
            popular: false,
        },
        {
            id: 'business',
            name: t('pricing.plans.business.name', 'Business'),
            description: t('pricing.plans.business.description', 'Pentru afaceri mici și mijlocii'),
            icon: esm/* BuildingOfficeIcon */.yQU,
            price: {
                monthly: 49.99,
                yearly: 499.99,
            },
            features: [
                t('pricing.plans.business.feature1', 'Toate funcționalitățile Familie'),
                t('pricing.plans.business.feature2', 'Utilizatori nelimitați'),
                t('pricing.plans.business.feature3', 'API pentru integrări'),
                t('pricing.plans.business.feature4', 'Rapoarte avansate'),
                t('pricing.plans.business.feature5', 'Facturare automată'),
                t('pricing.plans.business.feature6', 'Contabilitate integrată'),
                t('pricing.plans.business.feature7', 'Manager dedicat'),
                t('pricing.plans.business.feature8', 'SLA garantat'),
            ],
            limitations: [],
            cta: t('pricing.plans.business.cta', 'Contactați-ne'),
            popular: false,
        },
    ];
    const displayPlans = (0,react.useMemo)(() => plansFromApi?.data || staticPlans, [plansFromApi, staticPlans]);
    // Funcție pentru gestionarea checkout-ului
    const handlePlanSelection = async (plan) => {
        // Dacă planul este gratuit
        if (plan.id === 'free') {
            if (!isAuthenticated) {
                navigate('/register');
                return;
            }
            // Utilizatorul este deja pe planul gratuit
            (0,react_hot_toast_dist/* toast */.oR)(t('pricing.messages.alreadyOnFreePlan', 'Sunteți deja pe planul gratuit!'));
            return;
        }
        // Pentru planul business, redirecționează către contact
        if (plan.id === 'business') {
            navigate('/contact');
            return;
        }
        // Verifică dacă utilizatorul este autentificat
        if (!isAuthenticated) {
            // Salvează planul selectat în localStorage pentru după autentificare
            localStorage.setItem('selectedPlan', JSON.stringify({
                planId: plan.id,
                billingCycle
            }));
            navigate('/login');
            return;
        }
        // Verifică dacă utilizatorul are deja acest abonament
        if (currentSubscription?.data && currentSubscription.data.plan?.id === plan.id) {
            (0,react_hot_toast_dist/* toast */.oR)(t('pricing.messages.alreadySubscribed', 'Aveți deja acest abonament!'));
            return;
        }
        try {
            // Creează sesiunea de checkout
            await createCheckoutSession.mutateAsync({
                planId: plan.id,
                billingCycle: billingCycle,
                successUrl: `${window.location.origin}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
                cancelUrl: window.location.href,
            });
        }
        catch (error) {
            console.error('Eroare la crearea checkout-ului:', error);
            react_hot_toast_dist/* toast */.oR.error(t('pricing.messages.checkoutError', 'A apărut o eroare. Vă rugăm să încercați din nou.'));
        }
    };
    // Funcție pentru obținerea textului butonului
    const getButtonText = (plan) => {
        if (plan.id === 'free') {
            return isAuthenticated ?
                t('pricing.buttons.currentPlan', 'Planul Curent') :
                t('pricing.buttons.startFree', 'Începeți Gratuit');
        }
        if (plan.id === 'business') {
            return t('pricing.buttons.contact', 'Contactați-ne');
        }
        if (currentSubscription?.data && currentSubscription.data.plan?.id === plan.id) {
            return t('pricing.buttons.currentPlan', 'Planul Curent');
        }
        return createCheckoutSession.isPending ?
            t('pricing.buttons.processing', 'Se procesează...') :
            plan.cta;
    };
    // Funcție pentru verificarea dacă butonul este dezactivat
    const isButtonDisabled = (plan) => {
        if (plan.id === 'free' && isAuthenticated)
            return true;
        if (currentSubscription?.data && currentSubscription.data.plan?.id === plan.id)
            return true;
        if (createCheckoutSession.isPending)
            return true;
        return false;
    };
    const faqs = [
        {
            question: t('pricing.faq.q1', 'Pot schimba planul oricând?'),
            answer: t('pricing.faq.a1', 'Da, puteți face upgrade sau downgrade oricând. Modificările se aplică imediat și facturarea se ajustează proporțional.'),
        },
        {
            question: t('pricing.faq.q2', 'Există o perioadă de probă gratuită?'),
            answer: t('pricing.faq.a2', 'Da, toate planurile plătite vin cu o perioadă de probă gratuită de 14 zile. Nu este necesară cardul de credit.'),
        },
        {
            question: t('pricing.faq.q3', 'Ce metode de plată acceptați?'),
            answer: t('pricing.faq.a3', 'Acceptăm toate cardurile majore (Visa, Mastercard, American Express), PayPal și transfer bancar pentru planurile Business.'),
        },
        {
            question: t('pricing.faq.q4', 'Pot anula oricând?'),
            answer: t('pricing.faq.a4', 'Absolut! Nu există contracte pe termen lung. Puteți anula oricând din setările contului dvs.'),
        },
        {
            question: t('pricing.faq.q5', 'Oferiti reduceri pentru studenți sau ONG-uri?'),
            answer: t('pricing.faq.a5', 'Da, oferim reduceri de 50% pentru studenți cu ID valid și reduceri speciale pentru organizații non-profit. Contactați-ne pentru detalii.'),
        },
    ];
    const getPrice = (plan) => {
        const price = plan.price[billingCycle];
        if (price === 0)
            return t('pricing.free', 'Gratuit');
        if (billingCycle === 'yearly') {
            const monthlyEquivalent = (price / 12).toFixed(2);
            return `$${monthlyEquivalent}/${t('pricing.month', 'lună')}`;
        }
        return `$${price}/${t('pricing.month', 'lună')}`;
    };
    const getSavings = (plan) => {
        if (billingCycle === 'yearly' && plan.price.yearly > 0) {
            const yearlyPrice = plan.price.yearly;
            const monthlyPrice = plan.price.monthly * 12;
            const savings = monthlyPrice - yearlyPrice;
            const percentage = Math.round((savings / monthlyPrice) * 100);
            return `${t('pricing.save', 'Economisiți')} ${percentage}%`;
        }
        return null;
    };
    return ((0,jsx_runtime.jsx)(PublicLayout/* default */.A, { children: (0,jsx_runtime.jsxs)("div", { className: "min-h-screen bg-gray-50", children: [(0,jsx_runtime.jsx)("div", { className: "bg-white shadow-sm", children: (0,jsx_runtime.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6", children: [(0,jsx_runtime.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,jsx_runtime.jsxs)(dist/* Link */.N_, { to: "/", className: "flex items-center text-gray-600 hover:text-gray-900 transition-colors", children: [(0,jsx_runtime.jsx)(esm/* ArrowLeftIcon */.A60, { className: "w-5 h-5 mr-2" }), t('common.back', 'Înapoi')] }), (0,jsx_runtime.jsx)("div", { className: "h-6 w-px bg-gray-300" }), (0,jsx_runtime.jsx)("h1", { className: "text-3xl font-bold text-gray-900", children: t('product.pricing.title', 'Prețuri') })] }), (0,jsx_runtime.jsx)("p", { className: "mt-4 text-lg text-gray-600 max-w-3xl", children: t('product.pricing.subtitle', 'Alegeți planul perfect pentru nevoile dvs. financiare. Toate planurile includ suport 24/7 și actualizări gratuite.') })] }) }), (0,jsx_runtime.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12", children: [(0,jsx_runtime.jsxs)("div", { className: "text-center mb-12", children: [(0,jsx_runtime.jsx)("h2", { className: "text-3xl font-bold text-gray-900 mb-4", children: t('pricing.title', 'Planuri Simple și Transparente') }), (0,jsx_runtime.jsx)("p", { className: "text-lg text-gray-600 mb-8", children: t('pricing.subtitle', 'Fără costuri ascunse. Fără surprize. Doar valoare reală.') }), (0,jsx_runtime.jsxs)("div", { className: "flex items-center justify-center space-x-4", children: [(0,jsx_runtime.jsx)("span", { className: `text-sm font-medium ${billingCycle === 'monthly' ? 'text-gray-900' : 'text-gray-500'}`, children: t('pricing.monthly', 'Lunar') }), (0,jsx_runtime.jsx)("button", { onClick: () => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly'), className: "relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2", children: (0,jsx_runtime.jsx)("span", { className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'}` }) }), (0,jsx_runtime.jsx)("span", { className: `text-sm font-medium ${billingCycle === 'yearly' ? 'text-gray-900' : 'text-gray-500'}`, children: t('pricing.yearly', 'Anual') }), billingCycle === 'yearly' && ((0,jsx_runtime.jsx)("span", { className: "bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full", children: t('pricing.save_up_to', 'Economisiți până la 17%') }))] })] }), plansLoading && ((0,jsx_runtime.jsxs)("div", { className: "flex justify-center items-center py-12", children: [(0,jsx_runtime.jsx)("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600" }), (0,jsx_runtime.jsx)("span", { className: "ml-3 text-gray-600", children: t('pricing.loading', 'Se încarcă planurile...') })] })), !plansLoading && ((0,jsx_runtime.jsx)("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16", children: displayPlans.map((plan) => {
                                const IconComponent = plan.icon;
                                const savings = getSavings(plan);
                                return ((0,jsx_runtime.jsxs)("div", { className: `relative bg-white rounded-2xl shadow-sm border-2 p-8 ${plan.popular
                                        ? 'border-blue-500 ring-2 ring-blue-500 ring-opacity-20'
                                        : 'border-gray-200 hover:border-gray-300'} transition-all duration-300 hover:shadow-lg`, children: [plan.popular && ((0,jsx_runtime.jsx)("div", { className: "absolute -top-4 left-1/2 transform -translate-x-1/2", children: (0,jsx_runtime.jsxs)("div", { className: "bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center", children: [(0,jsx_runtime.jsx)(esm/* StarIcon */.Gg5, { className: "w-4 h-4 mr-1" }), t('pricing.popular', 'Cel mai popular')] }) })), (0,jsx_runtime.jsxs)("div", { className: "text-center mb-8", children: [(0,jsx_runtime.jsx)("div", { className: "bg-gray-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4", children: (0,jsx_runtime.jsx)(IconComponent, { className: "w-6 h-6 text-gray-600" }) }), (0,jsx_runtime.jsx)("h3", { className: "text-xl font-semibold text-gray-900 mb-2", children: plan.name }), (0,jsx_runtime.jsx)("p", { className: "text-gray-600 text-sm mb-4", children: plan.description }), (0,jsx_runtime.jsxs)("div", { className: "mb-2", children: [(0,jsx_runtime.jsx)("span", { className: "text-4xl font-bold text-gray-900", children: getPrice(plan) }), plan.price[billingCycle] > 0 && billingCycle === 'yearly' && ((0,jsx_runtime.jsxs)("div", { className: "text-sm text-gray-500 mt-1", children: ["$", plan.price.yearly, "/", t('pricing.year', 'an')] }))] }), savings && ((0,jsx_runtime.jsx)("div", { className: "text-sm text-green-600 font-medium", children: savings }))] }), (0,jsx_runtime.jsxs)("ul", { className: "space-y-3 mb-8", children: [plan.features.map((feature, index) => ((0,jsx_runtime.jsxs)("li", { className: "flex items-start", children: [(0,jsx_runtime.jsx)(esm/* CheckIcon */.Srz, { className: "w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" }), (0,jsx_runtime.jsx)("span", { className: "text-gray-700 text-sm", children: feature })] }, index))), plan.limitations.map((limitation, index) => ((0,jsx_runtime.jsxs)("li", { className: "flex items-start", children: [(0,jsx_runtime.jsx)(esm/* XMarkIcon */.fKY, { className: "w-5 h-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" }), (0,jsx_runtime.jsx)("span", { className: "text-gray-500 text-sm", children: limitation })] }, `limitation-${index}`)))] }), (0,jsx_runtime.jsx)("button", { onClick: () => handlePlanSelection(plan), disabled: isButtonDisabled(plan), className: `w-full py-3 px-4 rounded-lg font-semibold transition-colors ${isButtonDisabled(plan)
                                                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                                : plan.popular
                                                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                                                    : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`, children: getButtonText(plan) })] }, plan.id));
                            }) })), (0,jsx_runtime.jsx)("div", { className: "bg-white py-16", children: (0,jsx_runtime.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: [(0,jsx_runtime.jsxs)("div", { className: "text-center mb-12", children: [(0,jsx_runtime.jsx)("h2", { className: "text-3xl font-bold text-gray-900 mb-4", children: t('pricing.comparison.title', 'Comparați Planurile') }), (0,jsx_runtime.jsx)("p", { className: "text-lg text-gray-600", children: t('pricing.comparison.subtitle', 'Vedeți exact ce obțineți cu fiecare plan') })] }), (0,jsx_runtime.jsx)("div", { className: "overflow-x-auto", children: (0,jsx_runtime.jsxs)("table", { className: "w-full", children: [(0,jsx_runtime.jsx)("thead", { children: (0,jsx_runtime.jsxs)("tr", { className: "border-b border-gray-200", children: [(0,jsx_runtime.jsx)("th", { className: "text-left py-4 px-6 font-semibold text-gray-900", children: t('pricing.comparison.features', 'Funcționalități') }), displayPlans.map((plan) => ((0,jsx_runtime.jsx)("th", { className: "text-center py-4 px-6 font-semibold text-gray-900", children: plan.name }, plan.id)))] }) }), (0,jsx_runtime.jsxs)("tbody", { className: "divide-y divide-gray-200", children: [(0,jsx_runtime.jsxs)("tr", { children: [(0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-gray-700", children: t('pricing.comparison.accounts', 'Conturi bancare') }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-gray-600", children: "3" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-green-600", children: "\u221E" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-green-600", children: "\u221E" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-green-600", children: "\u221E" })] }), (0,jsx_runtime.jsxs)("tr", { children: [(0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-gray-700", children: t('pricing.comparison.transactions', 'Tranzacții/lună') }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-gray-600", children: "100" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-green-600", children: "\u221E" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-green-600", children: "\u221E" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-green-600", children: "\u221E" })] }), (0,jsx_runtime.jsxs)("tr", { children: [(0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-gray-700", children: t('pricing.comparison.users', 'Utilizatori') }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-gray-600", children: "1" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-gray-600", children: "1" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-green-600", children: "6" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-green-600", children: "\u221E" })] }), (0,jsx_runtime.jsxs)("tr", { children: [(0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-gray-700", children: t('pricing.comparison.support', 'Suport') }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-gray-600", children: "Email" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-green-600", children: "Prioritar" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-green-600", children: "Telefonic" }), (0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-center text-green-600", children: "Dedicat" })] }), (0,jsx_runtime.jsxs)("tr", { children: [(0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-gray-700", children: t('pricing.comparison.price', 'Preț') }), displayPlans.map((plan) => ((0,jsx_runtime.jsxs)("td", { className: "text-center py-4 px-6", children: [(0,jsx_runtime.jsx)("span", { className: "text-2xl font-bold text-gray-900", children: getPrice(plan) }), (0,jsx_runtime.jsxs)("span", { className: "text-gray-500 ml-1", children: ["/", billingCycle === 'monthly' ? t('pricing.month', 'lună') : t('pricing.year', 'an')] })] }, plan.id)))] }), (0,jsx_runtime.jsxs)("tr", { children: [(0,jsx_runtime.jsx)("td", { className: "py-4 px-6 text-gray-700" }), displayPlans.map((plan) => ((0,jsx_runtime.jsx)("td", { className: "text-center py-4 px-6", children: (0,jsx_runtime.jsx)("button", { onClick: () => handlePlanSelection(plan), disabled: isButtonDisabled(plan), className: `w-full py-3 px-4 rounded-lg font-semibold transition-colors ${isButtonDisabled(plan)
                                                                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                                                            : plan.popular
                                                                                ? 'bg-blue-600 text-white hover:bg-blue-700'
                                                                                : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`, children: getButtonText(plan) }) }, plan.id)))] })] })] }) })] }) }), (0,jsx_runtime.jsx)("div", { className: "bg-gray-50 py-16", children: (0,jsx_runtime.jsxs)("div", { className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8", children: [(0,jsx_runtime.jsxs)("div", { className: "text-center mb-12", children: [(0,jsx_runtime.jsx)("h2", { className: "text-3xl font-bold text-gray-900 mb-4", children: t('pricing.faq.title', 'Întrebări Frecvente') }), (0,jsx_runtime.jsx)("p", { className: "text-lg text-gray-600", children: t('pricing.faq.subtitle', 'Răspunsuri la cele mai comune întrebări despre prețuri') })] }), (0,jsx_runtime.jsx)("div", { className: "space-y-6", children: faqs.map((faq, index) => ((0,jsx_runtime.jsxs)("div", { className: "bg-white rounded-lg p-6 shadow-sm", children: [(0,jsx_runtime.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-3", children: faq.question }), (0,jsx_runtime.jsx)("p", { className: "text-gray-700 leading-relaxed", children: faq.answer })] }, index))) })] }) }), (0,jsx_runtime.jsx)("div", { className: "bg-blue-600 text-white py-16", children: (0,jsx_runtime.jsxs)("div", { className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center", children: [(0,jsx_runtime.jsx)("h2", { className: "text-3xl font-bold mb-4", children: t('pricing.cta.title', 'Gata să vă transformați finanțele?') }), (0,jsx_runtime.jsx)("p", { className: "text-xl text-blue-100 mb-8", children: t('pricing.cta.subtitle', 'Alăturați-vă miilor de utilizatori care și-au îmbunătățit situația financiară cu FinanceFlow.') }), (0,jsx_runtime.jsxs)("div", { className: "flex flex-col sm:flex-row gap-4 justify-center", children: [(0,jsx_runtime.jsx)("button", { onClick: () => {
                                                    const popularPlan = displayPlans.find(p => p.popular) ?? displayPlans[1];
                                                    if (popularPlan) {
                                                        handlePlanSelection(popularPlan);
                                                    }
                                                }, disabled: createCheckoutSession.isPending, className: `px-8 py-3 rounded-lg font-semibold transition-colors text-center ${createCheckoutSession.isPending
                                                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                                    : 'bg-white text-blue-600 hover:bg-gray-100'}`, children: createCheckoutSession.isPending
                                                    ? t('pricing.buttons.processing', 'Se procesează...')
                                                    : t('pricing.cta.start', 'Începeți Gratuit') }), (0,jsx_runtime.jsx)(dist/* Link */.N_, { to: "/contact", className: "border border-blue-400 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors", children: t('pricing.cta.contact', 'Contactați Vânzările') })] })] }) })] })] }) }));
};
/* harmony default export */ const product_Pricing = (Pricing);


/***/ })

}]);