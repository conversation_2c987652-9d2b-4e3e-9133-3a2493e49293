import {
  ChartBarIcon,
  CalendarIcon,
  DocumentArrowDownIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';
import { useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';
import { toast } from 'react-hot-toast';

import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { useExportExpenses } from '../hooks/useExpenses';
import { formatCurrency } from '../utils/helpers';

// Mock data pentru demonstrație
const mockReportData = {
  totalExpenses: 1250.75,
  totalIncome: 2500.0,
  savings: 1249.25,
  monthlyData: [
    { month: 'Ian', expenses: 800, income: 2500 },
    { month: 'Feb', expenses: 950, income: 2500 },
    { month: 'Mar', expenses: 1100, income: 2500 },
    { month: 'Apr', expenses: 750, income: 2500 },
    { month: 'Mai', expenses: 1250, income: 2500 },
    { month: 'Iun', expenses: 900, income: 2500 },
  ],
  categoryBreakdown: [
    { name: 'Mâncare', amount: 450.25, percentage: 36 },
    { name: 'Transport', amount: 200.0, percentage: 16 },
    { name: 'Utilități', amount: 150.0, percentage: 12 },
    { name: 'Divertisment', amount: 100.0, percentage: 8 },
    { name: 'Altele', amount: 350.5, percentage: 28 },
  ],
  topExpenses: [
    {
      id: 'exp_1',
      description: 'Cumpărături supermarket',
      amount: 85.5,
      date: '2024-01-15',
      category: 'Mâncare',
    },
    {
      id: 'exp_2',
      description: 'Combustibil',
      amount: 75.0,
      date: '2024-01-14',
      category: 'Transport',
    },
    {
      id: 'exp_3',
      description: 'Factură electricitate',
      amount: 65.0,
      date: '2024-01-13',
      category: 'Utilități',
    },
    {
      id: 'exp_4',
      description: 'Cinema',
      amount: 45.0,
      date: '2024-01-12',
      category: 'Divertisment',
    },
    {
      id: 'exp_5',
      description: 'Restaurant',
      amount: 120.0,
      date: '2024-01-11',
      category: 'Mâncare',
    },
  ],
};

const Reports: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('thisMonth');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Hook pentru export
  const exportExpensesMutation = useExportExpenses();

  // Simulare query pentru rapoarte
  const {
    data: reportData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['reports', selectedPeriod, selectedCategory],
    queryFn: async () => {
      // Simulare API call
      await new Promise(resolve => setTimeout(resolve, 800));
      return mockReportData;
    },
  });

  const handleExportReport = async (format: 'csv' | 'pdf' | 'excel') => {
    try {
      // Pregătește parametrii pentru export bazați pe filtrele curente
      // Aici ar trebui să existe o logică pentru a converti `selectedPeriod` în `dateFrom` și `dateTo`
      const exportParams = {
        // dateFrom: ...,
        // dateTo: ...,
        categoryId: selectedCategory !== 'all' ? selectedCategory : undefined,
      };

      // Apelează funcția de export
      await exportExpensesMutation.mutateAsync({ format, params: exportParams });
    } catch (error: any) {
      // Eroarea este gestionată în hook
      console.error('Export failed:', error);
    }
  };

  const periodOptions = [
    { value: 'thisWeek', label: 'Această săptămână' },
    { value: 'thisMonth', label: 'Această lună' },
    { value: 'lastMonth', label: 'Luna trecută' },
    { value: 'thisYear', label: 'Acest an' },
    { value: 'custom', label: 'Perioadă personalizată' },
  ];

  const categoryOptions = [
    { value: 'all', label: 'Toate categoriile' },
    { value: 'food', label: 'Mâncare' },
    { value: 'transport', label: 'Transport' },
    { value: 'utilities', label: 'Utilități' },
    { value: 'entertainment', label: 'Divertisment' },
  ];

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Eroare la încărcarea raportului</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Rapoarte</h1>
          <p className="text-gray-600">Analizează cheltuielile și tendințele financiare</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <Button
            variant="outline"
            onClick={() => handleExportReport('csv')}
            disabled={exportExpensesMutation.isPending}
            className="flex items-center"
          >
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            {exportExpensesMutation.isPending ? 'Se exportă...' : 'Export CSV'}
          </Button>
          <Button
            variant="outline"
            onClick={() => handleExportReport('pdf')}
            disabled={exportExpensesMutation.isPending}
            className="flex items-center"
          >
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            {exportExpensesMutation.isPending ? 'Se exportă...' : 'Export PDF'}
          </Button>
          <Button
            variant="outline"
            onClick={() => handleExportReport('excel')}
            disabled={exportExpensesMutation.isPending}
            className="flex items-center"
          >
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            {exportExpensesMutation.isPending ? 'Se exportă...' : 'Export Excel'}
          </Button>
        </div>
      </div>

      {/* Filtre */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <span className="text-sm font-medium text-gray-700">Filtrează:</span>
          </div>

          <div className="flex flex-col sm:flex-row sm:space-x-4 space-y-4 sm:space-y-0">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Perioada</label>
              <select
                value={selectedPeriod}
                onChange={e => setSelectedPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
              >
                {periodOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Categoria</label>
              <select
                value={selectedCategory}
                onChange={e => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
              >
                {categoryOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </Card>

      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      ) : reportData ? (
        <>
          {/* Statistici generale */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <ChartBarIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total cheltuieli</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(reportData.totalExpenses)}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <ChartBarIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total venituri</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(reportData.totalIncome)}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <ChartBarIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Economii</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(reportData.savings)}
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Grafic lunar (simulat cu bare simple) */}
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Tendința lunară</h3>
            <div className="space-y-4">
              {reportData.monthlyData.map((month, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-12 text-sm font-medium text-gray-600">{month.month}</div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                        <div
                          className="bg-red-500 h-4 rounded-full"
                          style={{ width: `${(month.expenses / month.income) * 100}%` }}
                        />
                      </div>
                      <div className="text-sm text-gray-600 w-20">
                        {formatCurrency(month.expenses)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Breakdown pe categorii */}
            <Card className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Cheltuieli pe categorii</h3>
              <div className="space-y-4">
                {reportData.categoryBreakdown.map((category, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-blue-500 rounded-full" />
                      <span className="text-sm font-medium text-gray-900">{category.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">{category.percentage}%</span>
                      <span className="text-sm font-medium text-gray-900">
                        {formatCurrency(category.amount)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Top cheltuieli */}
            <Card className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Top cheltuieli</h3>
              <div className="space-y-4">
                {reportData.topExpenses.map(expense => (
                  <div
                    key={expense.id}
                    className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
                  >
                    <div>
                      <p className="text-sm font-medium text-gray-900">{expense.description}</p>
                      <p className="text-xs text-gray-500">
                        {expense.category} • {expense.date}
                      </p>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {formatCurrency(expense.amount)}
                    </span>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        </>
      ) : (
        <Card className="p-12 text-center">
          <p className="text-gray-500">Nu sunt date disponibile pentru perioada selectată</p>
        </Card>
      )}
    </div>
  );
};

export default Reports;
