
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  email: 'email',
  password: 'password',
  avatar: 'avatar',
  role: 'role',
  currency: 'currency',
  timezone: 'timezone',
  isActive: 'isActive',
  emailVerified: 'emailVerified',
  emailVerificationToken: 'emailVerificationToken',
  passwordResetToken: 'passwordResetToken',
  passwordResetExpires: 'passwordResetExpires',
  lastLogin: 'lastLogin',
  loginCount: 'loginCount',
  preferences: 'preferences',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastUsageReset: 'lastUsageReset',
  monthlyExpenseCount: 'monthlyExpenseCount',
  monthlyExpenseLimit: 'monthlyExpenseLimit',
  planType: 'planType',
  stripeCustomerId: 'stripeCustomerId',
  subscriptionCurrentPeriodEnd: 'subscriptionCurrentPeriodEnd',
  subscriptionCurrentPeriodStart: 'subscriptionCurrentPeriodStart',
  subscriptionId: 'subscriptionId',
  subscriptionStatus: 'subscriptionStatus',
  trialEndsAt: 'trialEndsAt',
  refreshToken: 'refreshToken',
  firstName: 'firstName',
  lastName: 'lastName',
  id: 'id',
  new_id: 'new_id'
};

exports.Prisma.CategoryScalarFieldEnum = {
  name: 'name',
  description: 'description',
  color: 'color',
  icon: 'icon',
  budgetLimit: 'budgetLimit',
  budgetPeriod: 'budgetPeriod',
  isActive: 'isActive',
  isDefault: 'isDefault',
  sortOrder: 'sortOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  id: 'id',
  userId: 'userId',
  new_id: 'new_id',
  new_user_id: 'new_user_id'
};

exports.Prisma.ExpenseScalarFieldEnum = {
  amount: 'amount',
  description: 'description',
  date: 'date',
  notes: 'notes',
  paymentMethod: 'paymentMethod',
  location: 'location',
  receiptUrl: 'receiptUrl',
  tags: 'tags',
  isRecurring: 'isRecurring',
  recurringFrequency: 'recurringFrequency',
  recurringEndDate: 'recurringEndDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  id: 'id',
  userId: 'userId',
  categoryId: 'categoryId',
  originalExpenseId: 'originalExpenseId',
  new_id: 'new_id',
  new_user_id: 'new_user_id',
  new_category_id: 'new_category_id',
  new_original_expense_id: 'new_original_expense_id'
};

exports.Prisma.PlanScalarFieldEnum = {
  stripeId: 'stripeId',
  name: 'name',
  description: 'description',
  price: 'price',
  currency: 'currency',
  interval: 'interval',
  features: 'features',
  limits: 'limits',
  isActive: 'isActive',
  sortOrder: 'sortOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  id: 'id',
  new_id: 'new_id'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  stripeId: 'stripeId',
  status: 'status',
  currentPeriodStart: 'currentPeriodStart',
  currentPeriodEnd: 'currentPeriodEnd',
  trialStart: 'trialStart',
  trialEnd: 'trialEnd',
  canceledAt: 'canceledAt',
  endedAt: 'endedAt',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  id: 'id',
  userId: 'userId',
  planId: 'planId',
  new_id: 'new_id',
  new_user_id: 'new_user_id',
  new_plan_id: 'new_plan_id'
};

exports.Prisma.UsageLogScalarFieldEnum = {
  action: 'action',
  resource: 'resource',
  metadata: 'metadata',
  createdAt: 'createdAt',
  id: 'id',
  userId: 'userId',
  resourceId: 'resourceId',
  new_id: 'new_id',
  new_user_id: 'new_user_id',
  new_resource_id: 'new_resource_id'
};

exports.Prisma.WebhookEventScalarFieldEnum = {
  stripeId: 'stripeId',
  type: 'type',
  data: 'data',
  processed: 'processed',
  processedAt: 'processedAt',
  error: 'error',
  retryCount: 'retryCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  id: 'id',
  new_id: 'new_id'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.UserRole = exports.$Enums.UserRole = {
  user: 'user',
  admin: 'admin'
};

exports.PlanType = exports.$Enums.PlanType = {
  free: 'free',
  basic: 'basic',
  premium: 'premium'
};

exports.SubscriptionStatus = exports.$Enums.SubscriptionStatus = {
  active: 'active',
  canceled: 'canceled',
  incomplete: 'incomplete',
  incomplete_expired: 'incomplete_expired',
  past_due: 'past_due',
  trialing: 'trialing',
  unpaid: 'unpaid'
};

exports.BudgetPeriod = exports.$Enums.BudgetPeriod = {
  daily: 'daily',
  weekly: 'weekly',
  monthly: 'monthly',
  yearly: 'yearly'
};

exports.PaymentMethod = exports.$Enums.PaymentMethod = {
  cash: 'cash',
  card: 'card',
  bank_transfer: 'bank_transfer',
  digital_wallet: 'digital_wallet',
  check: 'check',
  other: 'other'
};

exports.RecurringFrequency = exports.$Enums.RecurringFrequency = {
  daily: 'daily',
  weekly: 'weekly',
  monthly: 'monthly',
  yearly: 'yearly'
};

exports.Prisma.ModelName = {
  User: 'User',
  Category: 'Category',
  Expense: 'Expense',
  Plan: 'Plan',
  Subscription: 'Subscription',
  UsageLog: 'UsageLog',
  WebhookEvent: 'WebhookEvent'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
