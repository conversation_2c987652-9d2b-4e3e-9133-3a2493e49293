// Tipuri centrale pentru aplicația Expense Tracker
// Sincronizate cu backend-ul (camelCase + CUID)

// Tipuri de bază
export interface BaseEntity {
  id: string; // CUID format
  createdAt: string;
  updatedAt: string;
}

// Tipuri pentru utilizatori (sincronizate cu backend)
export interface User extends BaseEntity {
  email: string;
  firstName: string;
  lastName: string;
  password?: string; // Doar pentru tipare interne, nu vine de la API
  avatar?: string;
  role: 'user' | 'admin';
  currency: string;
  timezone: string;
  isActive: boolean;
  emailVerified: boolean;
  emailVerificationToken?: string;
  passwordResetToken?: string;
  passwordResetExpires?: string;
  lastLogin?: string;
  loginCount: number;
  preferences: Record<string, any>;
  lastUsageReset: string;
  monthlyExpenseCount: number;
  monthlyExpenseLimit: number;
  planType: 'free' | 'basic' | 'premium';
  stripeCustomerId?: string;
  subscriptionCurrentPeriodEnd?: string;
  subscriptionCurrentPeriodStart?: string;
  subscriptionId?: string;
  subscriptionStatus?:
    | 'active'
    | 'canceled'
    | 'incomplete'
    | 'incomplete_expired'
    | 'past_due'
    | 'trialing'
    | 'unpaid';
  trialEndsAt?: string;
  refreshToken?: string;
  subscription?: Subscription;
  expenses?: Expense[];
  categories?: Category[];
  usageLogs?: UsageLog[];
}

export interface UserPreferences {
  language: 'ro' | 'en';
  currency: 'RON' | 'EUR' | 'USD' | 'GBP';
  dateFormat: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    email: boolean;
    push: boolean;
    weeklyReports: boolean;
    monthlyReports: boolean;
  };
}

// Tipuri pentru categorii (sincronizate cu backend)
export interface Category extends BaseEntity {
  name: string;
  description?: string;
  color: string;
  icon: string;
  budgetLimit?: number;
  budgetPeriod: 'daily' | 'weekly' | 'monthly' | 'yearly';
  isActive: boolean;
  isDefault: boolean;
  sortOrder: number;
  userId: string;
  user?: User;
  expenses?: Expense[];
  // Câmpuri calculate (doar frontend)
  expenseCount?: number;
  totalAmount?: number;
}

// Tipuri pentru cheltuieli (sincronizate cu backend)
export interface Expense extends BaseEntity {
  amount: number;
  description: string;
  date: string;
  notes?: string;
  paymentMethod: 'cash' | 'card' | 'bank_transfer' | 'digital_wallet' | 'other';
  location?: string;
  receiptUrl?: string;
  tags: string[];
  isRecurring: boolean;
  recurringFrequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  recurringEndDate?: string;
  originalExpenseId?: string;
  userId: string;
  categoryId: string;
  user?: User;
  category?: Category;
  originalExpense?: Expense;
  recurringExpenses?: Expense[];
}

// Tipuri pentru abonamente (sincronizate cu backend)
export interface Subscription extends BaseEntity {
  userId: string;
  planId: string;
  stripeId: string;
  status:
    | 'active'
    | 'canceled'
    | 'incomplete'
    | 'incomplete_expired'
    | 'past_due'
    | 'trialing'
    | 'unpaid';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  trialStart?: string;
  trialEnd?: string;
  canceledAt?: string;
  endedAt?: string;
  metadata?: Record<string, any>;
  user?: User;
  plan?: SubscriptionPlan;
}

export interface SubscriptionPlan extends BaseEntity {
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  stripePriceId: string;
  isActive: boolean;
  subscriptions?: Subscription[];
}

// Tipuri pentru Usage Logs
export interface UsageLog extends BaseEntity {
  userId: string;
  feature: string;
  count: number;
  date: string;
  metadata?: Record<string, any>;
  user?: User;
}

// Tipuri pentru Webhook Events
export interface WebhookEvent extends BaseEntity {
  stripeId: string;
  type: string;
  data: Record<string, any>;
  processed: boolean;
  processedAt?: string;
  error?: string;
  retryCount: number;
}

// Tipuri pentru formulare (camelCase)
export interface CreateExpenseForm {
  amount: number;
  description: string;
  categoryId: string;
  date: string;
  notes?: string;
  paymentMethod?: 'cash' | 'card' | 'bank_transfer' | 'digital_wallet' | 'other';
  location?: string;
  receiptUrl?: string;
  tags?: string[];
  isRecurring?: boolean;
  recurringFrequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  recurringEndDate?: string;
}

export interface UpdateExpenseForm extends Partial<CreateExpenseForm> {
  id: string;
}

export interface CreateCategoryForm {
  name: string;
  description?: string;
  color: string;
  icon: string;
  sortOrder?: number;
  budgetLimit?: number;
  budgetPeriod?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  isDefault?: boolean;
}

export interface UpdateCategoryForm extends Partial<CreateCategoryForm> {
  id: string;
  isActive?: boolean;
}

// Tipuri pentru autentificare (camelCase)
export interface LoginForm {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterForm {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  passwordConfirmation: string;
  termsAccepted: boolean;
  currency?: string;
  timezone?: string;
}

export interface AuthResponse {
  success: boolean;
  data: {
    user: User;
    tokens: {
      accessToken: string;
      refreshToken: string;
    };
  };
  message?: string;
}

// Tipuri pentru planuri de abonament (pentru afișare în frontend)
export interface PlanDisplay {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  limits: {
    expensesPerMonth: number;
    categories: number;
    exportsPerMonth: number;
  };
  stripePriceId: string;
  isPopular?: boolean;
}

// API Response types (camelCase)
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  errors?: Array<{
    field: string;
    message: string;
    value?: any;
  }>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types (actualizate pentru camelCase)
export interface ExpenseFormData {
  amount: number;
  description: string;
  categoryId: string;
  date: string;
  notes?: string;
  paymentMethod?: string;
  location?: string;
  tags?: string[];
  isRecurring?: boolean;
  recurringFrequency?: string;
  recurringEndDate?: string;
}

export interface CategoryFormData {
  name: string;
  description?: string;
  color: string;
  icon: string;
  budgetLimit?: number;
  budgetPeriod?: string;
  sortOrder?: number;
}

// Chart types
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string[];
    borderColor?: string[];
    borderWidth?: number;
  }[];
}

// Store types (actualizate pentru camelCase)
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (userData: RegisterData) => Promise<void>;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  currency?: string;
  timezone?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface UpdateProfileData {
  firstName?: string;
  lastName?: string;
  email?: string;
  avatar?: string;
  currency?: string;
  timezone?: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Component prop types
export interface ExpenseCardProps {
  expense: Expense;
  onEdit?: (expense: Expense) => void;
  onDelete?: (id: string) => void;
}

export interface CategoryBadgeProps {
  category: Category;
  size?: 'sm' | 'md' | 'lg';
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

// Filter and sort types (camelCase)
export interface ExpenseFilters {
  categoryId?: string;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
  paymentMethod?: string;
  isRecurring?: boolean;
  tags?: string[];
  search?: string;
}

export interface SortOptions {
  sortBy: 'date' | 'amount' | 'description' | 'createdAt';
  sortOrder: 'asc' | 'desc';
}

// Re-export tipuri din constants
export type { PaymentMethod, RecurringFrequency, Currency, Language } from '../utils/constants';

// Import tipuri pentru utilizare internă
import type { Currency, Language } from '../utils/constants';

// Statistics types (camelCase)
export interface ExpenseStats {
  totalExpenses: number;
  monthlyTotal: number;
  categoryBreakdown: {
    categoryId: string;
    categoryName: string;
    total: number;
    percentage: number;
    color?: string;
  }[];
  monthlyTrend: {
    month: string;
    total: number;
    expenseCount: number;
  }[];
  paymentMethodBreakdown: {
    method: string;
    total: number;
    percentage: number;
  }[];
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Language și Currency types sunt importate din constants

// Settings types (camelCase)
export interface UserSettings {
  theme: Theme;
  language: Language;
  currency: Currency;
  dateFormat: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
  notifications: {
    email: boolean;
    push: boolean;
    weekly: boolean;
    monthly: boolean;
  };
  dashboard: {
    defaultPeriod: 'week' | 'month' | 'year' | 'all';
    showCategories: boolean;
    showTrends: boolean;
  };
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface FormErrors {
  [key: string]: string | undefined;
}

// Hook types pentru query parameters
export interface UseExpensesParams {
  page?: number;
  limit?: number;
  categoryId?: string;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
  paymentMethod?: string;
  isRecurring?: boolean;
  tags?: string[];
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface UseCategoriesParams {
  includeInactive?: boolean;
  sortBy?: 'name' | 'sortOrder' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

// Tipuri pentru dashboard și statistici
export interface DashboardData {
  stats: ExpenseStats;
  recentExpenses: Expense[];
  topCategories: Category[];
  monthlyComparison: {
    current: number;
    previous: number;
    change: number;
    changePercentage: number;
  };
}

// Tipuri pentru export
export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf';
  dateRange: {
    startDate: string;
    endDate: string;
  };
  categories?: string[];
  includeNotes?: boolean;
  includeReceipts?: boolean;
}

// Tipuri pentru notificări
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

// Tipuri pentru loading states
export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

export interface AsyncState<T> extends LoadingState {
  data?: T | null;
}

// Tipuri pentru paginare
export interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Tipuri pentru modal-uri
export interface ModalState {
  isOpen: boolean;
  data?: any;
}

// Tipuri pentru theme și appearance
export interface ThemeConfig {
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  shadowColor: string;
}

// Tipuri pentru responsive design
export type BreakPoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

// Tipuri pentru accessibility
export interface A11yProps {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  role?: string;
  tabIndex?: number;
}
