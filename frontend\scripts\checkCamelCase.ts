/**
 * Script pentru verificarea utilizării camelCase în fișierele TypeScript
 * 
 * Acest script scan<PERSON><PERSON><PERSON> toate fișierele TypeScript din frontend și verifică
 * dacă folosesc camelCase pentru variabile, proprietăți și funcții.
 * 
 * Rulare: npx ts-node scripts/checkCamelCase.ts
 */

import * as fs from 'fs';
import * as path from 'path';
import * as ts from 'typescript';

// Configurare
const SRC_DIR = path.join(__dirname, '..', 'src');
const IGNORE_DIRS = ['node_modules', 'dist', 'build', 'coverage', '.git'];
const IGNORE_FILES = ['.d.ts', '.test.ts', '.test.tsx', '.spec.ts', '.spec.tsx'];
const IGNORE_PATTERNS = [
  /import\s+.*\s+from/,  // Ignoră importurile
  /export\s+.*\s+from/,  // Ignoră exporturile
  /\/\/.*$/,             // Ignoră comentariile pe o linie
  /\/\*[\s\S]*?\*\//,    // Ignoră comentariile multi-linie
  /`[\s\S]*?`/,          // Ignoră template strings
  /'[^']*'/,             // Ignoră string-urile cu ghilimele simple
  /"[^"]*"/,             // Ignoră string-urile cu ghilimele duble
];

// Tipuri pentru rezultate
interface FileResult {
  filePath: string;
  snakeCaseInstances: SnakeCaseInstance[];
  totalSnakeCaseCount: number;
}

interface SnakeCaseInstance {
  line: number;
  column: number;
  text: string;
  context: string;
}

interface SummaryResult {
  totalFiles: number;
  filesWithSnakeCase: number;
  totalSnakeCaseInstances: number;
  fileResults: FileResult[];
}

/**
 * Verifică dacă un string este în snake_case
 */
function isSnakeCase(str: string): boolean {
  return /^[a-z][a-z0-9]*(_[a-z0-9]+)+$/.test(str);
}

/**
 * Obține contextul pentru o poziție în text
 */
function getContext(text: string, position: number, length: number, contextSize: number = 40): string {
  const start = Math.max(0, position - contextSize);
  const end = Math.min(text.length, position + length + contextSize);
  
  let context = text.substring(start, end);
  
  // Adaugă elipse dacă contextul este trunchiat
  if (start > 0) context = '...' + context;
  if (end < text.length) context = context + '...';
  
  return context;
}

/**
 * Găsește toate instanțele de snake_case într-un fișier
 */
function findSnakeCaseInFile(filePath: string): FileResult {
  const fileContent = fs.readFileSync(filePath, 'utf-8');
  const sourceFile = ts.createSourceFile(
    filePath,
    fileContent,
    ts.ScriptTarget.Latest,
    true
  );
  
  const snakeCaseInstances: SnakeCaseInstance[] = [];
  
  // Funcție pentru a procesa nodurile AST
  function visit(node: ts.Node) {
    // Verifică identificatorii (variabile, proprietăți, funcții)
    if (ts.isIdentifier(node)) {
      const text = node.getText(sourceFile);
      
      // Verifică dacă este snake_case și nu este într-un pattern ignorat
      if (isSnakeCase(text)) {
        const position = node.getStart(sourceFile);
        const lineAndChar = sourceFile.getLineAndCharacterOfPosition(position);
        
        // Obține linia completă pentru context
        const line = fileContent.split('\n')[lineAndChar.line];
        
        // Verifică dacă nu este într-un pattern ignorat
        let shouldIgnore = false;
        for (const pattern of IGNORE_PATTERNS) {
          if (pattern.test(line)) {
            shouldIgnore = true;
            break;
          }
        }
        
        if (!shouldIgnore) {
          snakeCaseInstances.push({
            line: lineAndChar.line + 1,
            column: lineAndChar.character + 1,
            text,
            context: getContext(fileContent, position, text.length),
          });
        }
      }
    }
    
    // Continuă recursiv cu nodurile copil
    ts.forEachChild(node, visit);
  }
  
  // Începe procesarea AST
  visit(sourceFile);
  
  return {
    filePath: path.relative(SRC_DIR, filePath),
    snakeCaseInstances,
    totalSnakeCaseCount: snakeCaseInstances.length,
  };
}

/**
 * Scanează recursiv un director pentru fișiere TypeScript
 */
function scanDirectory(dir: string): string[] {
  const files: string[] = [];
  
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    // Ignoră directoarele specificate
    if (entry.isDirectory() && !IGNORE_DIRS.includes(entry.name)) {
      files.push(...scanDirectory(fullPath));
    } 
    // Procesează doar fișierele TypeScript care nu sunt ignorate
    else if (entry.isFile() && 
             (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx')) &&
             !IGNORE_FILES.some(ext => entry.name.endsWith(ext))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Funcția principală
 */
function main(): SummaryResult {
  console.log('🔍 Verificarea utilizării camelCase în fișierele TypeScript...\n');
  
  // Scanează toate fișierele TypeScript
  const files = scanDirectory(SRC_DIR);
  console.log(`📂 ${files.length} fișiere TypeScript găsite pentru analiză.\n`);
  
  // Procesează fiecare fișier
  const fileResults: FileResult[] = [];
  let totalSnakeCaseInstances = 0;
  let filesWithSnakeCase = 0;
  
  for (const file of files) {
    const result = findSnakeCaseInFile(file);
    
    if (result.totalSnakeCaseCount > 0) {
      fileResults.push(result);
      totalSnakeCaseInstances += result.totalSnakeCaseCount;
      filesWithSnakeCase++;
    }
  }
  
  // Sortează rezultatele după numărul de instanțe
  fileResults.sort((a, b) => b.totalSnakeCaseCount - a.totalSnakeCaseCount);
  
  // Afișează rezultatele
  console.log('📊 REZULTATE:');
  console.log(`   - Fișiere analizate: ${files.length}`);
  console.log(`   - Fișiere cu snake_case: ${filesWithSnakeCase}`);
  console.log(`   - Total instanțe snake_case: ${totalSnakeCaseInstances}`);
  console.log('');
  
  if (filesWithSnakeCase > 0) {
    console.log('⚠️  FIȘIERE CU SNAKE_CASE:');
    
    for (const result of fileResults) {
      console.log(`\n📄 ${result.filePath} (${result.totalSnakeCaseCount} instanțe):`);
      
      for (const instance of result.snakeCaseInstances) {
        console.log(`   - Linia ${instance.line}, Col ${instance.column}: "${instance.text}"`);
        console.log(`     Context: ${instance.context}`);
      }
    }
    
    console.log('\n⚠️  Aceste fișiere necesită actualizare pentru a folosi camelCase.');
  } else {
    console.log('✅ Toate fișierele folosesc camelCase! Migrarea este completă.');
  }
  
  return {
    totalFiles: files.length,
    filesWithSnakeCase,
    totalSnakeCaseInstances,
    fileResults,
  };
}

// Rulează scriptul dacă este apelat direct
if (require.main === module) {
  main();
}

export { main as checkCamelCase };
