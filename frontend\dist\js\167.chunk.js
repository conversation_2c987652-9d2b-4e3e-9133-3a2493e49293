"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[167],{

/***/ 7167:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9582);
/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(125);
/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(9264);
/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(2552);
/* harmony import */ var _hooks_useAdminData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(71);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(3658);









const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ro-RO', {
        style: 'currency',
        currency: 'RON',
    }).format(amount);
};
// Tipurile sunt importate din types/index.ts
const ActivityFeed = () => {
    const [selectedActivity, setSelectedActivity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);
    const [showActivityModal, setShowActivityModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');
    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('24h');
    // Query pentru activitatea recentă
    const { data: activities, isLoading, error, } = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_7__/* .useActivityFeed */ .Bq)({
        type: filter === 'all' ? undefined : filter,
        timeRange,
        limit: 50,
    });
    const getActivityIcon = (type) => {
        const iconMap = {
            user_registered: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UserIcon */ .nys,
            user_login: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UserIcon */ .nys,
            subscription_created: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CreditCardIcon */ .BFk,
            subscription_canceled: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .XMarkIcon */ .fKY,
            payment_succeeded: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CheckCircleIcon */ .C1y,
            payment_failed: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ExclamationTriangleIcon */ .Pip,
            plan_upgraded: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowRightIcon */ .flY,
            plan_downgraded: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowRightIcon */ .flY,
            usage_limit_reached: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ExclamationTriangleIcon */ .Pip,
            system_error: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ExclamationTriangleIcon */ .Pip,
        };
        return iconMap[type] || _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ClockIcon */ .O4;
    };
    const getActivityColor = (type) => {
        const colorMap = {
            user_registered: 'text-green-600 bg-green-100',
            user_login: 'text-blue-600 bg-blue-100',
            subscription_created: 'text-green-600 bg-green-100',
            subscription_canceled: 'text-red-600 bg-red-100',
            payment_succeeded: 'text-green-600 bg-green-100',
            payment_failed: 'text-red-600 bg-red-100',
            plan_upgraded: 'text-blue-600 bg-blue-100',
            plan_downgraded: 'text-yellow-600 bg-yellow-100',
            usage_limit_reached: 'text-yellow-600 bg-yellow-100',
            system_error: 'text-red-600 bg-red-100',
        };
        return colorMap[type] || 'text-gray-600 bg-gray-100';
    };
    const getActivityTitle = (activity) => {
        const titleMap = {
            user_registered: 'Utilizator nou înregistrat',
            user_login: 'Utilizator autentificat',
            subscription_created: 'Abonament creat',
            subscription_canceled: 'Abonament anulat',
            payment_succeeded: 'Plată reușită',
            payment_failed: 'Plată eșuată',
            plan_upgraded: 'Plan îmbunătățit',
            plan_downgraded: 'Plan retrogradat',
            usage_limit_reached: 'Limită de utilizare atinsă',
            system_error: 'Eroare de sistem',
        };
        return titleMap[activity.type] || 'Activitate necunoscută';
    };
    const getActivityDescription = (activity) => {
        const { type, data } = activity;
        if (!data) {
            return activity.description || 'Fără descriere';
        }
        switch (type) {
            case 'user_registered':
                return `${data.user?.firstName || ''} ${data.user?.lastName || ''} (${data.user?.email || ''})`;
            case 'user_login':
                return `${data.user?.firstName || ''} ${data.user?.lastName || ''} s-a autentificat`;
            case 'subscription_created':
                return `${data.user?.firstName || ''} ${data.user?.lastName || ''} a creat abonament ${data.plan?.name || ''}`;
            case 'subscription_canceled':
                return `${data.user?.firstName || ''} ${data.user?.lastName || ''} a anulat abonamentul ${data.plan?.name || ''}`;
            case 'payment_succeeded':
                return `Plată de ${formatCurrency(data.amount || 0)} pentru ${data.user?.firstName || ''} ${data.user?.lastName || ''}`;
            case 'payment_failed':
                return `Plată eșuată de ${formatCurrency(data.amount || 0)} pentru ${data.user?.firstName || ''} ${data.user?.lastName || ''}`;
            case 'plan_upgraded':
                return `${data.user?.firstName || ''} ${data.user?.lastName || ''}: ${data.fromPlan || ''} → ${data.toPlan || ''}`;
            case 'plan_downgraded':
                return `${data.user?.firstName || ''} ${data.user?.lastName || ''}: ${data.fromPlan || ''} → ${data.toPlan || ''}`;
            case 'usage_limit_reached':
                return `${data.user?.firstName || ''} ${data.user?.lastName || ''} a atins limita pentru ${data.feature || ''}`;
            case 'system_error':
                return data.message || 'Eroare de sistem';
            default:
                return activity.description || 'Fără descriere';
        }
    };
    const getPriorityBadge = (priority) => {
        const priorityConfig = {
            high: { variant: 'error', label: 'Prioritate mare' },
            medium: { variant: 'warning', label: 'Prioritate medie' },
            low: { variant: 'secondary', label: 'Prioritate mică' },
        };
        const config = priorityConfig[priority] || priorityConfig.low;
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, { variant: config.variant, size: "sm", children: config.label }));
    };
    const getRelativeTime = (date) => {
        const now = new Date();
        const activityDate = new Date(date);
        const diffInMinutes = Math.floor((now.getTime() - activityDate.getTime()) / (1000 * 60));
        if (diffInMinutes < 1)
            return 'Acum';
        if (diffInMinutes < 60)
            return `${diffInMinutes}m`;
        if (diffInMinutes < 1440)
            return `${Math.floor(diffInMinutes / 60)}h`;
        return `${Math.floor(diffInMinutes / 1440)}z`;
    };
    if (isLoading) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center justify-center h-32", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { size: "lg" }) }) }));
    }
    if (error) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center text-red-600", children: "Eroare la \u00EEnc\u0103rcarea activit\u0103\u021Bii" }) }));
    }
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900", children: "Activitate recent\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex gap-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select", { value: timeRange, onChange: (e) => setTimeRange(e.target.value), className: "px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "1h", children: "Ultima or\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "24h", children: "Ultimele 24h" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "7d", children: "Ultima s\u0103pt\u0103m\u00E2n\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "30d", children: "Ultima lun\u0103" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select", { value: filter, onChange: (e) => setFilter(e.target.value), className: "px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "all", children: "Toate" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "users", children: "Utilizatori" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "payments", children: "Pl\u0103\u021Bi" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "subscriptions", children: "Abonamente" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "errors", children: "Erori" })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4 max-h-96 overflow-y-auto", children: activities?.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center text-gray-500 py-8", children: "Nu exist\u0103 activitate \u00EEn perioada selectat\u0103" })) : (activities?.map((activity) => {
                            const activityType = activity.type;
                            const IconComponent = getActivityIcon(activityType);
                            const colorClasses = getActivityColor(activityType);
                            return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer", onClick: () => {
                                    const activityData = {
                                        ...activity,
                                        type: activity.type,
                                        createdAt: activity.timestamp,
                                        priority: 'medium',
                                    };
                                    setSelectedActivity(activityData);
                                    setShowActivityModal(true);
                                }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: `flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${colorClasses}`, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconComponent, { className: "h-4 w-4" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1 min-w-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-900 truncate", children: getActivityTitle({
                                                            ...activity,
                                                            type: activity.type,
                                                            createdAt: activity.timestamp,
                                                            priority: 'medium',
                                                        }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center space-x-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-xs text-gray-500", children: getRelativeTime(activity.timestamp) }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-600 truncate", children: getActivityDescription({
                                                    ...activity,
                                                    type: activity.type,
                                                    createdAt: activity.timestamp,
                                                    priority: 'medium',
                                                }) }), activity.metadata?.ip && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-xs text-gray-400", children: ["IP: ", activity.metadata.ip] }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-shrink-0", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .EyeIcon */ .bMW, { className: "h-4 w-4 text-gray-400" }) })] }, activity.id));
                        })) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-6 pt-4 border-t border-gray-200", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-4 gap-4 text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-lg font-semibold text-green-600", children: activities?.filter((a) => a.type.includes('succeeded') || a.type.includes('created')).length || 0 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-xs text-gray-500", children: "Ac\u021Biuni pozitive" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-lg font-semibold text-red-600", children: activities?.filter((a) => a.type.includes('failed') || a.type.includes('error')).length || 0 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-xs text-gray-500", children: "Erori" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-lg font-semibold text-blue-600", children: activities?.filter((a) => a.type.includes('user')).length || 0 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-xs text-gray-500", children: "Activitate utilizatori" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-lg font-semibold text-purple-600", children: activities?.filter((a) => a.type.includes('payment')).length ||
                                                0 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-xs text-gray-500", children: "Tranzac\u021Bii" })] })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { isOpen: showActivityModal, onClose: () => setShowActivityModal(false), title: "Detalii activitate", size: "lg", children: selectedActivity && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: `flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${getActivityColor(selectedActivity.type)}`, children: react__WEBPACK_IMPORTED_MODULE_2___default().createElement(getActivityIcon(selectedActivity.type), {
                                        className: 'h-5 w-5',
                                    }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-lg font-medium text-gray-900", children: getActivityTitle(selectedActivity) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-600", children: getActivityDescription(selectedActivity) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500 mt-1", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_8__/* .formatDate */ .Yq)(selectedActivity.createdAt) })] }), selectedActivity.priority && selectedActivity.priority !== 'low' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-shrink-0", children: getPriorityBadge(selectedActivity.priority) }))] }), selectedActivity.data?.user && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "border-t pt-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h5", { className: "text-sm font-medium text-gray-900 mb-2", children: "Utilizator" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 gap-4 text-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-gray-500", children: "Nume:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "ml-2 text-gray-900", children: [selectedActivity.data.user.firstName || '', ' ', selectedActivity.data.user.lastName || ''] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-gray-500", children: "Email:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "ml-2 text-gray-900", children: selectedActivity.data.user.email })] }), selectedActivity.data.user.id && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-gray-500", children: "ID:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "ml-2 text-gray-900 font-mono text-xs", children: selectedActivity.data.user.id })] }))] })] })), selectedActivity.data?.amount && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "border-t pt-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h5", { className: "text-sm font-medium text-gray-900 mb-2", children: "Detalii financiare" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 gap-4 text-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-gray-500", children: "Sum\u0103:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "ml-2 text-gray-900 font-semibold", children: formatCurrency(selectedActivity.data.amount) })] }), selectedActivity.data.currency && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-gray-500", children: "Moned\u0103:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "ml-2 text-gray-900", children: selectedActivity.data.currency })] })), selectedActivity.data.paymentMethod && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-gray-500", children: "Metod\u0103 plat\u0103:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "ml-2 text-gray-900", children: selectedActivity.data.paymentMethod })] }))] })] })), selectedActivity.metadata && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "border-t pt-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h5", { className: "text-sm font-medium text-gray-900 mb-2", children: "Informa\u021Bii tehnice" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-gray-50 p-3 rounded-md", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("pre", { className: "text-xs text-gray-600 whitespace-pre-wrap", children: JSON.stringify(selectedActivity.metadata, null, 2) }) })] })), selectedActivity.data && Object.keys(selectedActivity.data).length > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "border-t pt-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h5", { className: "text-sm font-medium text-gray-900 mb-2", children: "Date complete" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-gray-50 p-3 rounded-md max-h-40 overflow-y-auto", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("pre", { className: "text-xs text-gray-600 whitespace-pre-wrap", children: JSON.stringify(selectedActivity.data, null, 2) }) })] }))] })) })] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ActivityFeed);


/***/ })

}]);