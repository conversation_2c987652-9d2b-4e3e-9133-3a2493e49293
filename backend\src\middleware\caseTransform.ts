/**
 * Middleware pentru transformarea automată între snake_case și camelCase
 * Permite compatibilitatea temporară în timpul tranziției
 */

import { Request, Response, NextFunction } from 'express';
import { CaseConverter } from '../utils/caseConverter';

// Tipuri pentru configurarea middleware-ului
interface CaseTransformOptions {
  transformRequest?: boolean;
  transformResponse?: boolean;
  transformQuery?: boolean;
  transformParams?: boolean;
  skipPaths?: string[];
  skipMethods?: string[];
  logTransformations?: boolean;
}

// Configurația implicită
const DEFAULT_OPTIONS: CaseTransformOptions = {
  transformRequest: true,
  transformResponse: true,
  transformQuery: true,
  transformParams: false, // Parametrii de rută rămân neschimbați
  skipPaths: ['/health', '/docs'],
  skipMethods: [],
  logTransformations: process.env.NODE_ENV === 'development'
};

/**
 * Middleware pentru transformarea request-urilor de la camelCase la snake_case
 * Folosit pentru a permite frontend-ului să trimită camelCase
 */
export function transformRequestToSnake(options: CaseTransformOptions = {}) {
  const config = { ...DEFAULT_OPTIONS, ...options };
  
  return (req: Request, res: Response, next: NextFunction): void => {
    // Verifică dacă trebuie să omitem această rută
    if (shouldSkipTransformation(req, config)) {
      return next();
    }
    
    try {
      // Transformă body-ul request-ului
      if (config.transformRequest && req.body && typeof req.body === 'object') {
        const originalBody = { ...req.body };
        req.body = CaseConverter.toSnake(req.body);
        
        if (config.logTransformations) {
          console.log('🔄 Request body transformed (camelCase → snake_case):', {
            path: req.path,
            method: req.method,
            original: originalBody,
            transformed: req.body
          });
        }
      }
      
      // Transformă query parameters
      if (config.transformQuery && req.query && typeof req.query === 'object') {
        const originalQuery = { ...req.query };
        req.query = CaseConverter.toSnake(req.query);
        
        if (config.logTransformations) {
          console.log('🔄 Query params transformed (camelCase → snake_case):', {
            path: req.path,
            original: originalQuery,
            transformed: req.query
          });
        }
      }
      
      next();
    } catch (error) {
      console.error('❌ Error in request transformation middleware:', error);
      next(error);
    }
  };
}

/**
 * Middleware pentru transformarea response-urilor de la snake_case la camelCase
 * Folosit pentru a trimite camelCase către frontend
 */
export function transformResponseToCamel(options: CaseTransformOptions = {}) {
  const config = { ...DEFAULT_OPTIONS, ...options };
  
  return (req: Request, res: Response, next: NextFunction): void => {
    // Verifică dacă trebuie să omitem această rută
    if (shouldSkipTransformation(req, config)) {
      return next();
    }
    
    // Interceptează metoda json() pentru a transforma response-ul
    const originalJson = res.json;
    
    res.json = function(data: any) {
      try {
        if (config.transformResponse && data && typeof data === 'object') {
          const originalData = JSON.parse(JSON.stringify(data));
          const transformedData = CaseConverter.toCamel(data);
          
          if (config.logTransformations) {
            console.log('🔄 Response transformed (snake_case → camelCase):', {
              path: req.path,
              method: req.method,
              original: originalData,
              transformed: transformedData
            });
          }
          
          return originalJson.call(this, transformedData);
        }
        
        return originalJson.call(this, data);
      } catch (error) {
        console.error('❌ Error in response transformation:', error);
        return originalJson.call(this, data);
      }
    };
    
    next();
  };
}

/**
 * Middleware combinat pentru transformare bidirectională
 * Transformă request-urile la snake_case și response-urile la camelCase
 */
export function transformCaseBidirectional(options: CaseTransformOptions = {}) {
  const config = { ...DEFAULT_OPTIONS, ...options };
  
  return [
    transformRequestToSnake(config),
    transformResponseToCamel(config)
  ];
}

/**
 * Verifică dacă transformarea trebuie omisă pentru această cerere
 */
function shouldSkipTransformation(req: Request, config: CaseTransformOptions): boolean {
  // Omite căile specificate
  if (config.skipPaths?.some(path => req.path.startsWith(path))) {
    return true;
  }
  
  // Omite metodele specificate
  if (config.skipMethods?.includes(req.method)) {
    return true;
  }
  
  // Omite fișierele statice
  if (req.path.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
    return true;
  }
  
  return false;
}

/**
 * Middleware pentru validarea duală (temporar)
 * Acceptă atât snake_case cât și camelCase în timpul tranziției
 */
export function enableDualCaseSupport(options: CaseTransformOptions = {}) {
  const config = { ...DEFAULT_OPTIONS, ...options };

  return (req: Request, res: Response, next: NextFunction): void => {
    if (shouldSkipTransformation(req, config)) {
      return next();
    }

    try {
      // Analizează formatul request-ului
      if (req.body && typeof req.body === 'object') {
        const analysis = analyzeRequestFormat(req.body);

        if (config.logTransformations) {
          console.log('📊 Request format analysis:', {
            path: req.path,
            method: req.method,
            format: analysis.dominantFormat,
            confidence: analysis.confidence,
            stats: analysis.stats
          });
        }

        // Transformă la snake_case dacă este predominant camelCase
        if (analysis.dominantFormat === 'camel' && analysis.confidence > 0.7) {
          req.body = CaseConverter.toSnake(req.body);

          if (config.logTransformations) {
            console.log('🔄 Auto-converted camelCase request to snake_case');
          }
        }
      }

      next();
    } catch (error) {
      console.error('❌ Error in dual case support middleware:', error);
      // Asigură-te că eroarea este transmisă mai departe
      if (error instanceof Error) {
        next(error);
      } else {
        next(new Error('Unknown error in dual case support middleware'));
      }
    }
  };
}

/**
 * Analizează formatul unui obiect pentru a determina dacă este snake_case sau camelCase
 */
function analyzeRequestFormat(obj: any): {
  dominantFormat: 'snake' | 'camel' | 'mixed' | 'unknown';
  confidence: number;
  stats: {
    totalKeys: number;
    snakeKeys: number;
    camelKeys: number;
    mixedKeys: number;
  };
} {
  const stats = {
    totalKeys: 0,
    snakeKeys: 0,
    camelKeys: 0,
    mixedKeys: 0
  };
  
  const analyzeRecursive = (item: any) => {
    if (typeof item === 'object' && item !== null && !Array.isArray(item)) {
      for (const key of Object.keys(item)) {
        stats.totalKeys++;
        
        if (CaseConverter.isSnakeCase(key)) {
          stats.snakeKeys++;
        } else if (CaseConverter.isCamelCase(key)) {
          stats.camelKeys++;
        } else {
          stats.mixedKeys++;
        }
        
        analyzeRecursive(item[key]);
      }
    } else if (Array.isArray(item)) {
      item.forEach(analyzeRecursive);
    }
  };
  
  analyzeRecursive(obj);
  
  if (stats.totalKeys === 0) {
    return { dominantFormat: 'unknown', confidence: 0, stats };
  }
  
  const snakeRatio = stats.snakeKeys / stats.totalKeys;
  const camelRatio = stats.camelKeys / stats.totalKeys;
  
  let dominantFormat: 'snake' | 'camel' | 'mixed' | 'unknown';
  let confidence: number;
  
  if (snakeRatio > camelRatio && snakeRatio > 0.5) {
    dominantFormat = 'snake';
    confidence = snakeRatio;
  } else if (camelRatio > snakeRatio && camelRatio > 0.5) {
    dominantFormat = 'camel';
    confidence = camelRatio;
  } else if (snakeRatio + camelRatio > 0.5) {
    dominantFormat = 'mixed';
    confidence = Math.max(snakeRatio, camelRatio);
  } else {
    dominantFormat = 'unknown';
    confidence = 0;
  }
  
  return { dominantFormat, confidence, stats };
}

/**
 * Middleware pentru logging detaliat al transformărilor
 * Util pentru debugging în timpul dezvoltării
 */
export function logCaseTransformations() {
  return (req: Request, res: Response, next: NextFunction): void => {
    const startTime = Date.now();
    
    // Log request
    if (req.body && typeof req.body === 'object') {
      const analysis = analyzeRequestFormat(req.body);
      console.log('📥 Incoming request:', {
        path: req.path,
        method: req.method,
        format: analysis.dominantFormat,
        confidence: analysis.confidence,
        bodyKeys: Object.keys(req.body)
      });
    }
    
    // Interceptează response pentru logging
    const originalJson = res.json;
    res.json = function(data: any) {
      const duration = Date.now() - startTime;
      
      if (data && typeof data === 'object') {
        const analysis = analyzeRequestFormat(data);
        console.log('📤 Outgoing response:', {
          path: req.path,
          method: req.method,
          status: res.statusCode,
          duration: `${duration}ms`,
          format: analysis.dominantFormat,
          confidence: analysis.confidence,
          dataKeys: typeof data === 'object' && data.data 
            ? Object.keys(data.data) 
            : Object.keys(data)
        });
      }
      
      return originalJson.call(this, data);
    };
    
    next();
  };
}

/**
 * Configurații predefinite pentru diferite scenarii
 */
export const CaseTransformPresets = {
  /**
   * Configurație pentru dezvoltare - logging activat
   */
  development: {
    transformRequest: true,
    transformResponse: true,
    transformQuery: true,
    logTransformations: true,
    skipPaths: ['/health', '/docs', '/api-docs']
  },
  
  /**
   * Configurație pentru producție - fără logging
   */
  production: {
    transformRequest: true,
    transformResponse: true,
    transformQuery: true,
    logTransformations: false,
    skipPaths: ['/health', '/docs']
  },
  
  /**
   * Configurație pentru testare - transformări minime
   */
  testing: {
    transformRequest: false,
    transformResponse: false,
    transformQuery: false,
    logTransformations: false
  },
  
  /**
   * Configurație pentru tranziție - suport dual
   */
  transition: {
    transformRequest: true,
    transformResponse: true,
    transformQuery: true,
    logTransformations: true,
    skipPaths: ['/health', '/docs']
  }
};

export default {
  transformRequestToSnake,
  transformResponseToCamel,
  transformCaseBidirectional,
  enableDualCaseSupport,
  logCaseTransformations,
  CaseTransformPresets
};
