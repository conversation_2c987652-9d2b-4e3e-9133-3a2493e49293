"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[76],{

/***/ 71:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Bq: () => (/* binding */ useActivityFeed),
  MD: () => (/* binding */ useAdminDashboardStats),
  o4: () => (/* binding */ useBlockUser),
  Xi: () => (/* binding */ useCancelSubscription),
  hU: () => (/* binding */ useMarkAlertAsRead),
  gm: () => (/* binding */ usePlanStats),
  l4: () => (/* binding */ useReactivateSubscription),
  wr: () => (/* binding */ useRevenueData),
  fZ: () => (/* binding */ useSubscriptionDetails),
  cV: () => (/* binding */ useSubscriptionStats),
  Q2: () => (/* binding */ useSubscriptions),
  d: () => (/* binding */ useSuspendSubscription),
  gI: () => (/* binding */ useSyncWithStripe),
  U2: () => (/* binding */ useSystemAlerts),
  b4: () => (/* binding */ useUnblockUser),
  aZ: () => (/* binding */ useUsageStats),
  RB: () => (/* binding */ useUserDetails),
  kp: () => (/* binding */ useUsers)
});

// UNUSED EXPORTS: useActivityStats, useExportData, useSubscriptionsSimple, useUsersSimple

// EXTERNAL MODULE: ./node_modules/@tanstack/react-query/build/modern/index.js + 21 modules
var modern = __webpack_require__(8035);
// EXTERNAL MODULE: ./node_modules/react-hot-toast/dist/index.mjs + 1 modules
var dist = __webpack_require__(888);
// EXTERNAL MODULE: ./src/services/api.ts + 1 modules
var api = __webpack_require__(4768);
;// ./src/services/adminService.ts

/**
 * Serviciu pentru funcționalitățile de administrare
 */
class AdminService {
    /**
     * Obține statisticile generale pentru dashboard-ul admin
     */
    async getDashboardStats() {
        try {
            const response = await api/* default */.A.get('/admin/dashboard/stats');
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea statisticilor dashboard:', error);
            throw error;
        }
    }
    /**
     * Obține statisticile de abonament
     */
    async getSubscriptionStats() {
        try {
            const response = await api/* default */.A.get('/admin/subscriptions/stats');
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea statisticilor de abonament:', error);
            throw error;
        }
    }
    /**
     * Obține statisticile planurilor
     */
    async getPlanStats() {
        try {
            const response = await api/* default */.A.get('/admin/plans/stats');
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea statisticilor planurilor:', error);
            throw error;
        }
    }
    /**
     * Obține statisticile de utilizare
     */
    async getUsageStats() {
        try {
            const response = await api/* default */.A.get('/admin/usage/stats');
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea statisticilor de utilizare:', error);
            throw error;
        }
    }
    /**
     * Obține datele pentru graficele de venituri
     */
    async getRevenueData(period = '12months') {
        try {
            const response = await api/* default */.A.get(`/admin/revenue/data?period=${period}`);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea datelor de venituri:', error);
            throw error;
        }
    }
    /**
     * Obține lista utilizatorilor cu paginare și filtrare
     */
    async getUsers(params = {}) {
        try {
            const { page = 1, limit = 10, search = '', status = '', plan = '', sortBy = 'created_at', sortOrder = 'desc', } = params;
            const queryParams = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString(),
                ...(search && { search }),
                ...(status && { status }),
                ...(plan && { plan }),
                sortBy,
                sortOrder,
            });
            const response = await api/* default */.A.get(`/admin/users?${queryParams}`);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea utilizatorilor:', error);
            throw error;
        }
    }
    /**
     * Obține detaliile unui utilizator
     */
    async getUserDetails(userId) {
        try {
            const response = await api/* default */.A.get(`/admin/users/${userId}`);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea detaliilor utilizatorului:', error);
            throw error;
        }
    }
    /**
     * Blochează un utilizator
     */
    async blockUser(userId, reason = '') {
        try {
            const response = await api/* default */.A.post(`/admin/users/${userId}/block`, { reason });
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la blocarea utilizatorului:', error);
            throw error;
        }
    }
    /**
     * Deblochează un utilizator
     */
    async unblockUser(userId) {
        try {
            const response = await api/* default */.A.post(`/admin/users/${userId}/unblock`);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la deblocarea utilizatorului:', error);
            throw error;
        }
    }
    /**
     * Obține lista abonamentelor cu paginare și filtrare
     */
    async getSubscriptions(params = {}) {
        try {
            const { page = 1, limit = 10, status = '', plan = '', sortBy = 'created_at', sortOrder = 'desc', } = params;
            const queryParams = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString(),
                ...(status && { status }),
                ...(plan && { plan }),
                sortBy,
                sortOrder,
            });
            const response = await api/* default */.A.get(`/admin/subscriptions?${queryParams}`);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea abonamentelor:', error);
            throw error;
        }
    }
    /**
     * Obține detaliile unui abonament
     */
    async getSubscriptionDetails(subscriptionId) {
        try {
            const response = await api/* default */.A.get(`/admin/subscriptions/${subscriptionId}`);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea detaliilor abonamentului:', error);
            throw error;
        }
    }
    /**
     * Suspendă un abonament
     */
    async suspendSubscription(subscriptionId, reason = '') {
        try {
            const response = await api/* default */.A.post(`/admin/subscriptions/${subscriptionId}/suspend`, { reason });
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la suspendarea abonamentului:', error);
            throw error;
        }
    }
    /**
     * Reactivează un abonament
     */
    async reactivateSubscription(subscriptionId) {
        try {
            const response = await api/* default */.A.post(`/admin/subscriptions/${subscriptionId}/reactivate`);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la reactivarea abonamentului:', error);
            throw error;
        }
    }
    /**
     * Anulează un abonament
     */
    async cancelSubscription(subscriptionId, reason = '') {
        try {
            const response = await api/* default */.A.post(`/admin/subscriptions/${subscriptionId}/cancel`, { reason });
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la anularea abonamentului:', error);
            throw error;
        }
    }
    /**
     * Sincronizează un abonament cu Stripe
     */
    async syncSubscriptionWithStripe(subscriptionId) {
        try {
            const response = await api/* default */.A.post(`/admin/subscriptions/${subscriptionId}/sync`);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la sincronizarea cu Stripe:', error);
            throw error;
        }
    }
    /**
     * Obține activitatea recentă
     */
    async getActivityFeed(params = {}) {
        try {
            const { page = 1, limit = 20, type = '', timeRange = '7d', } = params;
            const queryParams = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString(),
                ...(type && { type }),
                timeRange,
            });
            const response = await api/* default */.A.get(`/admin/activity?${queryParams}`);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea activității:', error);
            throw error;
        }
    }
    /**
     * Obține statisticile activității
     */
    async getActivityStats(timeRange = '7d') {
        try {
            const response = await api/* default */.A.get(`/admin/activity/stats?timeRange=${timeRange}`);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea statisticilor activității:', error);
            throw error;
        }
    }
    /**
     * Exportă date pentru rapoarte
     */
    async exportData(type, params = {}) {
        try {
            const queryParams = new URLSearchParams(params);
            const response = await api/* default */.A.get(`/admin/export/${type}?${queryParams}`, {
                responseType: 'blob',
            });
            return response.data;
        }
        catch (error) {
            console.error('Eroare la exportul datelor:', error);
            throw error;
        }
    }
    /**
     * Obține alertele sistemului
     */
    async getSystemAlerts() {
        try {
            const response = await api/* default */.A.get('/admin/alerts');
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea alertelor:', error);
            throw error;
        }
    }
    /**
     * Marchează o alertă ca citită
     */
    async markAlertAsRead(alertId) {
        try {
            const response = await api/* default */.A.post(`/admin/alerts/${alertId}/read`);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la marcarea alertei:', error);
            throw error;
        }
    }
    /**
     * Obține configurările sistemului
     */
    async getSystemConfig() {
        try {
            const response = await api/* default */.A.get('/admin/config');
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la obținerea configurărilor:', error);
            throw error;
        }
    }
    /**
     * Actualizează configurările sistemului
     */
    async updateSystemConfig(config) {
        try {
            const response = await api/* default */.A.put('/admin/config', config);
            return response.data.data;
        }
        catch (error) {
            console.error('Eroare la actualizarea configurărilor:', error);
            throw error;
        }
    }
}
/* harmony default export */ const services_adminService = (new AdminService());

;// ./src/hooks/useAdminData.ts



/**
 * Hook pentru statisticile dashboard-ului admin
 */
function useAdminDashboardStats() {
    return (0,modern/* useQuery */.pw)({
        queryKey: ['admin', 'dashboard', 'stats'],
        queryFn: services_adminService.getDashboardStats,
        staleTime: 5 * 60 * 1000, // 5 minute
        gcTime: 10 * 60 * 1000, // 10 minute (renamed from cacheTime)
    });
}
/**
 * Hook pentru statisticile de abonament
 */
function useSubscriptionStats() {
    return (0,modern/* useQuery */.pw)({
        queryKey: ['admin', 'subscriptions', 'stats'],
        queryFn: services_adminService.getSubscriptionStats,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
    });
}
/**
 * Hook pentru statisticile planurilor
 */
function usePlanStats() {
    return (0,modern/* useQuery */.pw)({
        queryKey: ['admin', 'plans', 'stats'],
        queryFn: services_adminService.getPlanStats,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
    });
}
/**
 * Hook pentru statisticile de utilizare
 */
function useUsageStats() {
    return (0,modern/* useQuery */.pw)({
        queryKey: ['admin', 'usage', 'stats'],
        queryFn: services_adminService.getUsageStats,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
    });
}
/**
 * Hook pentru datele de venituri
 */
function useRevenueData(period = '12months') {
    return (0,modern/* useQuery */.pw)({
        queryKey: ['admin', 'revenue', 'data', period],
        queryFn: () => services_adminService.getRevenueData(period),
        staleTime: 10 * 60 * 1000, // 10 minute
        gcTime: 30 * 60 * 1000, // 30 minute
    });
}
/**
 * Hook pentru lista utilizatorilor cu paginare infinită
 */
function useUsers(params = {}) {
    return (0,modern/* useInfiniteQuery */.qu)({
        queryKey: ['admin', 'users', params],
        queryFn: ({ pageParam = 1 }) => services_adminService.getUsers({ ...params, page: pageParam }),
        getNextPageParam: lastPage => {
            if (!lastPage.data || !lastPage.pagination)
                return undefined;
            const { page: currentPage, totalPages } = lastPage.pagination;
            return currentPage < totalPages ? currentPage + 1 : undefined;
        },
        staleTime: 5 * 60 * 1000, // 5 minute
        gcTime: 15 * 60 * 1000, // 15 minute
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: 'always',
        initialPageParam: 1,
    });
}
/**
 * Hook pentru lista utilizatorilor (versiune simplă pentru compatibilitate)
 */
function useUsersSimple(params = {}) {
    return useQuery({
        queryKey: ['admin', 'users', 'simple', params],
        queryFn: () => adminService.getUsers(params),
        staleTime: 5 * 60 * 1000, // 5 minute
        gcTime: 15 * 60 * 1000, // 15 minute
        refetchOnWindowFocus: false,
    });
}
/**
 * Hook pentru detaliile unui utilizator
 */
function useUserDetails(userId) {
    return (0,modern/* useQuery */.pw)({
        queryKey: ['admin', 'users', userId],
        queryFn: () => services_adminService.getUserDetails(userId),
        enabled: !!userId,
        staleTime: 5 * 60 * 1000,
    });
}
/**
 * Hook pentru blocarea unui utilizator
 */
function useBlockUser() {
    const queryClient = (0,modern/* useQueryClient */.jE)();
    return (0,modern/* useMutation */.n_)({
        mutationFn: ({ userId, reason }) => services_adminService.blockUser(userId, reason),
        onSuccess: (data, { userId }) => {
            // Invalidează cache-ul pentru utilizatori
            queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
            queryClient.invalidateQueries({ queryKey: ['admin', 'users', userId] });
            dist/* toast */.oR.success('Utilizatorul a fost blocat cu succes');
        },
        onError: (error) => {
            dist/* toast */.oR.error(error.response?.data?.message || 'Eroare la blocarea utilizatorului');
        },
    });
}
/**
 * Hook pentru deblocarea unui utilizator
 */
function useUnblockUser() {
    const queryClient = (0,modern/* useQueryClient */.jE)();
    return (0,modern/* useMutation */.n_)({
        mutationFn: (userId) => services_adminService.unblockUser(userId),
        onSuccess: (data, userId) => {
            queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
            queryClient.invalidateQueries({ queryKey: ['admin', 'users', userId] });
            dist/* toast */.oR.success('Utilizatorul a fost deblocat cu succes');
        },
        onError: (error) => {
            dist/* toast */.oR.error(error.response?.data?.message || 'Eroare la deblocarea utilizatorului');
        },
    });
}
/**
 * Hook pentru lista abonamentelor cu paginare infinită
 */
function useSubscriptions(params = {}) {
    return (0,modern/* useInfiniteQuery */.qu)({
        queryKey: ['admin', 'subscriptions', params],
        queryFn: ({ pageParam = 1 }) => services_adminService.getSubscriptions({ ...params, page: pageParam }),
        getNextPageParam: lastPage => {
            if (!lastPage.data || !lastPage.pagination)
                return undefined;
            const { page: currentPage, totalPages } = lastPage.pagination;
            return currentPage < totalPages ? currentPage + 1 : undefined;
        },
        staleTime: 5 * 60 * 1000, // 5 minute
        gcTime: 15 * 60 * 1000, // 15 minute
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: 'always',
        initialPageParam: 1,
    });
}
/**
 * Hook pentru lista abonamentelor (versiune simplă pentru compatibilitate)
 */
function useSubscriptionsSimple(params = {}) {
    return useQuery({
        queryKey: ['admin', 'subscriptions', 'simple', params],
        queryFn: () => adminService.getSubscriptions(params),
        staleTime: 5 * 60 * 1000, // 5 minute
        gcTime: 15 * 60 * 1000, // 15 minute
        refetchOnWindowFocus: false,
    });
}
/**
 * Hook pentru detaliile unui abonament
 */
function useSubscriptionDetails(subscriptionId) {
    return (0,modern/* useQuery */.pw)({
        queryKey: ['admin', 'subscriptions', subscriptionId],
        queryFn: () => services_adminService.getSubscriptionDetails(subscriptionId),
        enabled: !!subscriptionId,
        staleTime: 5 * 60 * 1000,
    });
}
/**
 * Hook pentru suspendarea unui abonament
 */
function useSuspendSubscription() {
    const queryClient = (0,modern/* useQueryClient */.jE)();
    return (0,modern/* useMutation */.n_)({
        mutationFn: ({ subscriptionId, reason }) => services_adminService.suspendSubscription(subscriptionId, reason),
        onSuccess: (data, { subscriptionId }) => {
            queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions'] });
            queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', subscriptionId] });
            queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', 'stats'] });
            dist/* toast */.oR.success('Abonamentul a fost suspendat cu succes');
        },
        onError: (error) => {
            dist/* toast */.oR.error(error.response?.data?.message || 'Eroare la suspendarea abonamentului');
        },
    });
}
/**
 * Hook pentru reactivarea unui abonament
 */
function useReactivateSubscription() {
    const queryClient = (0,modern/* useQueryClient */.jE)();
    return (0,modern/* useMutation */.n_)({
        mutationFn: (subscriptionId) => services_adminService.reactivateSubscription(subscriptionId),
        onSuccess: (data, subscriptionId) => {
            queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions'] });
            queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', subscriptionId] });
            queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', 'stats'] });
            dist/* toast */.oR.success('Abonamentul a fost reactivat cu succes');
        },
        onError: (error) => {
            dist/* toast */.oR.error(error.response?.data?.message || 'Eroare la reactivarea abonamentului');
        },
    });
}
/**
 * Hook pentru anularea unui abonament
 */
function useCancelSubscription() {
    const queryClient = (0,modern/* useQueryClient */.jE)();
    return (0,modern/* useMutation */.n_)({
        mutationFn: ({ subscriptionId, reason }) => services_adminService.cancelSubscription(subscriptionId, reason),
        onSuccess: (data, { subscriptionId }) => {
            queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions'] });
            queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', subscriptionId] });
            queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', 'stats'] });
            dist/* toast */.oR.success('Abonamentul a fost anulat cu succes');
        },
        onError: (error) => {
            dist/* toast */.oR.error(error.response?.data?.message || 'Eroare la anularea abonamentului');
        },
    });
}
/**
 * Hook pentru sincronizarea cu Stripe
 */
function useSyncWithStripe() {
    const queryClient = (0,modern/* useQueryClient */.jE)();
    return (0,modern/* useMutation */.n_)({
        mutationFn: (subscriptionId) => services_adminService.syncSubscriptionWithStripe(subscriptionId),
        onSuccess: (data, subscriptionId) => {
            queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions'] });
            queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', subscriptionId] });
            dist/* toast */.oR.success('Sincronizarea cu Stripe a fost completă');
        },
        onError: (error) => {
            dist/* toast */.oR.error(error.response?.data?.message || 'Eroare la sincronizarea cu Stripe');
        },
    });
}
/**
 * Hook pentru activitatea recentă
 */
function useActivityFeed(params = {}) {
    return (0,modern/* useQuery */.pw)({
        queryKey: ['admin', 'activity', params],
        queryFn: async () => {
            const response = await services_adminService.getActivityFeed(params);
            // Returnează doar array-ul de activități din structura de răspuns
            return response.data || [];
        },
        staleTime: 1 * 60 * 1000, // 1 minut
        refetchInterval: 5 * 60 * 1000, // Refresh la 5 minute
    });
}
/**
 * Hook pentru statisticile activității
 */
function useActivityStats(timeRange = '7d') {
    return useQuery({
        queryKey: ['admin', 'activity', 'stats', timeRange],
        queryFn: () => adminService.getActivityStats(timeRange),
        staleTime: 5 * 60 * 1000,
    });
}
/**
 * Hook pentru alertele sistemului
 */
function useSystemAlerts() {
    return (0,modern/* useQuery */.pw)({
        queryKey: ['admin', 'alerts'],
        queryFn: services_adminService.getSystemAlerts,
        staleTime: 1 * 60 * 1000,
        refetchInterval: 2 * 60 * 1000, // Refresh la 2 minute
    });
}
/**
 * Hook pentru marcarea alertelor ca citite
 */
function useMarkAlertAsRead() {
    const queryClient = (0,modern/* useQueryClient */.jE)();
    return (0,modern/* useMutation */.n_)({
        mutationFn: (alertId) => services_adminService.markAlertAsRead(alertId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['admin', 'alerts'] });
        },
        onError: (error) => {
            dist/* toast */.oR.error(error.response?.data?.message || 'Eroare la marcarea alertei');
        },
    });
}
/**
 * Hook pentru exportul de date
 */
function useExportData() {
    return useMutation({
        mutationFn: ({ type, params }) => adminService.exportData(type, params),
        onSuccess: (blob, { type }) => {
            // Creează și descarcă fișierul
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${type}_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            toast.success('Exportul a fost descărcat cu succes');
        },
        onError: (error) => {
            toast.error(error.response?.data?.message || 'Eroare la exportul datelor');
        },
    });
}


/***/ }),

/***/ 125:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony exports CardHeader, CardTitle, CardDescription, CardContent, CardFooter, StatsCard, ListCard, FormCard, ImageCard, AlertCard */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3658);
/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9264);




/**
 * Componenta Card de bază pentru afișarea conținutului în containere stilizate
 */
const Card = ({ children, className = '', shadow = true, border = true, rounded = true, padding = true, hover = false, clickable = false, onClick, ...rest }) => {
    const isClickable = clickable || Boolean(onClick);
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)('bg-white', shadow && 'shadow-sm', border && 'border border-gray-200', rounded && 'rounded-lg', padding && 'p-6', hover && 'hover:shadow-md transition-shadow duration-200', isClickable && [
            'cursor-pointer',
            'hover:shadow-md hover:border-gray-300',
            'transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
        ], className), onClick: onClick, role: isClickable ? 'button' : undefined, tabIndex: isClickable ? 0 : undefined, onKeyDown: isClickable ? (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                onClick?.(e);
            }
        } : undefined, ...rest, children: children }));
};
/**
 * Header pentru card
 */
const CardHeader = ({ children, className = '', border = true, ...rest }) => {
    return (_jsx("div", { className: cn('px-6 py-4', border && 'border-b border-gray-200', className), ...rest, children: children }));
};
/**
 * Titlu pentru card header
 */
const CardTitle = ({ children, className = '', as: Component = 'h3', ...rest }) => {
    return (_jsx(Component, { className: cn('text-lg font-semibold text-gray-900', className), ...rest, children: children }));
};
/**
 * Descriere pentru card header
 */
const CardDescription = ({ children, className = '', ...rest }) => {
    return (_jsx("p", { className: cn('mt-1 text-sm text-gray-600', className), ...rest, children: children }));
};
/**
 * Conținutul principal al card-ului
 */
const CardContent = ({ children, className = '', padding = true, ...rest }) => {
    return (_jsx("div", { className: cn(padding && 'px-6 py-4', className), ...rest, children: children }));
};
/**
 * Footer pentru card
 */
const CardFooter = ({ children, className = '', border = true, padding = true, ...rest }) => {
    return (_jsx("div", { className: cn(border && 'border-t border-gray-200', padding && 'px-6 py-4', className), ...rest, children: children }));
};
/**
 * Card pentru statistici
 */
const StatsCard = ({ title, value, change, changeType = 'neutral', icon, className = '', loading = false, ...rest }) => {
    const changeColors = {
        positive: 'text-green-600',
        negative: 'text-red-600',
        neutral: 'text-gray-600',
    };
    return (_jsxs(Card, { className: cn('relative overflow-hidden', className), ...rest, children: [loading && (_jsx("div", { className: "absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center", children: _jsx(LoadingSpinner, { size: "md" }) })), _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex-1", children: [_jsx("p", { className: "text-sm font-medium text-gray-600 truncate", children: title }), _jsx("p", { className: "text-2xl font-bold text-gray-900 mt-1", children: value }), change && (_jsx("p", { className: cn('text-sm mt-1 flex items-center', changeColors[changeType]), children: change }))] }), icon && (_jsx("div", { className: "flex-shrink-0", children: _jsx("div", { className: "w-12 h-12 bg-primary-50 rounded-lg flex items-center justify-center", children: _jsx("span", { className: "text-primary-600", children: icon }) }) }))] })] }));
};
/**
 * Card pentru afișarea unei liste
 */
const ListCard = ({ title, description, items = [], renderItem, emptyMessage = 'Nu există elemente de afișat', className = '', loading = false, ...rest }) => {
    return (_jsxs(Card, { className: className, padding: false, ...rest, children: [(title || description) && (_jsxs(CardHeader, { children: [title && _jsx(CardTitle, { children: title }), description && _jsx(CardDescription, { children: description })] })), _jsx(CardContent, { padding: false, children: loading ? (_jsx("div", { className: "flex items-center justify-center py-8", children: _jsx(LoadingSpinner, { size: "md" }) })) : items.length > 0 ? (_jsx("div", { className: "divide-y divide-gray-200", children: items.map((item, index) => (_jsx("div", { className: "px-6 py-4", children: renderItem ? renderItem(item, index) : item }, index))) })) : (_jsx("div", { className: "text-center py-8 text-gray-500", children: emptyMessage })) })] }));
};
/**
 * Card pentru formulare
 */
const FormCard = ({ title, description, children, className = '', ...rest }) => {
    return (_jsxs(Card, { className: className, ...rest, children: [(title || description) && (_jsxs(CardHeader, { children: [title && _jsx(CardTitle, { children: title }), description && _jsx(CardDescription, { children: description })] })), _jsx(CardContent, { children: children })] }));
};
/**
 * Card pentru imagini
 */
const ImageCard = ({ src, alt, title, description, children, className = '', imageClassName = '', ...rest }) => {
    return (_jsxs(Card, { className: className, padding: false, ...rest, children: [src && (_jsx("div", { className: "aspect-w-16 aspect-h-9", children: _jsx("img", { src: src, alt: alt, className: cn('w-full h-48 object-cover rounded-t-lg', imageClassName) }) })), (title || description || children) && (_jsxs(CardContent, { children: [title && (_jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: title })), description && (_jsx("p", { className: "text-gray-600 mb-4", children: description })), children] }))] }));
};
/**
 * Card pentru alertă/notificare
 */
const AlertCard = ({ type = 'info', title, children, icon, dismissible = false, onDismiss, className = '', ...rest }) => {
    const typeStyles = {
        info: 'bg-blue-50 border-blue-200 text-blue-800',
        success: 'bg-green-50 border-green-200 text-green-800',
        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
        error: 'bg-red-50 border-red-200 text-red-800',
    };
    return (_jsx(Card, { className: cn(typeStyles[type], 'relative', className), ...rest, children: _jsxs("div", { className: "flex items-start", children: [icon && (_jsx("div", { className: "flex-shrink-0 mr-3", children: icon })), _jsxs("div", { className: "flex-1", children: [title && (_jsx("h3", { className: "font-medium mb-1", children: title })), children] }), dismissible && (_jsx("button", { onClick: onDismiss, className: "flex-shrink-0 ml-3 text-current opacity-70 hover:opacity-100", "aria-label": "\u00CEnchide", children: _jsx("svg", { className: "w-4 h-4", fill: "currentColor", viewBox: "0 0 20 20", children: _jsx("path", { fillRule: "evenodd", d: "M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z", clipRule: "evenodd" }) }) }))] }) }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Card);


/***/ }),

/***/ 714:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9582);
/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(6103);
/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(125);
/* harmony import */ var _components_ui_Dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(8724);
/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(9264);
/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(2552);
/* harmony import */ var _components_ui_Pagination__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(1235);
/* harmony import */ var _components_ui_Table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(5191);
/* harmony import */ var _hooks_useAdminData__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(71);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(3658);













const SubscriptionManager = () => {
    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');
    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');
    const [planFilter, setPlanFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');
    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);
    const [selectedSubscription, setSelectedSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);
    const [showDetailsModal, setShowDetailsModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const [showConfirmModal, setShowConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const [actionType, setActionType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');
    const itemsPerPage = 10;
    // Hook-uri pentru gestionarea abonamentelor
    const { data: subscriptionsData, isLoading, error, } = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_11__/* .useSubscriptions */ .Q2)({
        page: currentPage,
        search: searchTerm,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        plan: planFilter !== 'all' ? planFilter : undefined,
        limit: itemsPerPage,
    });
    const { data: subscriptionDetails } = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_11__/* .useSubscriptionDetails */ .fZ)(selectedSubscription?.id);
    const suspendSubscription = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_11__/* .useSuspendSubscription */ .d)();
    const reactivateSubscription = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_11__/* .useReactivateSubscription */ .l4)();
    const cancelSubscription = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_11__/* .useCancelSubscription */ .Xi)();
    const syncWithStripe = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_11__/* .useSyncWithStripe */ .gI)();
    const handleSubscriptionAction = (subscription, action) => {
        setActionType(action);
        setSelectedSubscription(subscription);
        setShowConfirmModal(true);
    };
    const confirmAction = async () => {
        if (!selectedSubscription || !actionType)
            return;
        try {
            switch (actionType) {
                case 'suspend':
                    await suspendSubscription.mutateAsync({
                        subscriptionId: selectedSubscription.id,
                        reason: 'Admin action',
                    });
                    break;
                case 'reactivate':
                    await reactivateSubscription.mutateAsync(selectedSubscription.id);
                    break;
                case 'cancel':
                    await cancelSubscription.mutateAsync({
                        subscriptionId: selectedSubscription.id,
                        reason: 'Admin action',
                    });
                    break;
                case 'sync':
                    await syncWithStripe.mutateAsync(selectedSubscription.id);
                    break;
                default:
                    break;
            }
            setShowConfirmModal(false);
            setSelectedSubscription(null);
        }
        catch (error) {
            console.error('Action failed:', error);
        }
    };
    const getStatusBadge = (status) => {
        const statusConfig = {
            active: { variant: 'success', label: 'Activ' },
            unpaid: { variant: 'secondary', label: 'Neplătit' },
            canceled: { variant: 'error', label: 'Anulat' },
            paused: { variant: 'warning', label: 'Suspendat' },
            past_due: { variant: 'error', label: 'Întârziat' },
            trialing: { variant: 'info', label: 'Perioadă de probă' },
        };
        const config = statusConfig[status] || statusConfig.unpaid;
        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, { variant: config?.variant, children: config?.label });
    };
    const getPlanBadge = (plan) => {
        const planConfig = {
            basic: { variant: 'primary', label: 'Basic', color: 'bg-blue-100 text-blue-800' },
            premium: { variant: 'success', label: 'Premium', color: 'bg-green-100 text-green-800' },
            enterprise: {
                variant: 'warning',
                label: 'Enterprise',
                color: 'bg-yellow-100 text-yellow-800',
            },
        };
        const config = planConfig[plan?.toLowerCase() ?? ''] ?? {
            variant: 'secondary',
            label: plan ?? 'Unknown',
        };
        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, { variant: config.variant, children: config.label });
    };
    const columns = [
        {
            key: 'user',
            title: 'Utilizator',
            render: (value, subscription) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "ml-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm font-medium text-gray-900", children: `${subscription.user.firstName || ''} ${subscription.user.lastName || ''}`.trim() ||
                                'Utilizator necunoscut' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-gray-500", children: subscription.user.email })] }) })),
        },
        {
            key: 'plan',
            title: 'Plan',
            render: (value, subscription) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [getPlanBadge(subscription.plan?.name ?? 'N/A'), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-xs text-gray-500 mt-1", children: [(0,_utils_helpers__WEBPACK_IMPORTED_MODULE_12__/* .formatCurrency */ .vv)(subscription.plan?.price ?? 0), "/lun\u0103"] })] })),
        },
        {
            key: 'status',
            title: 'Status',
            render: (value, subscription) => getStatusBadge(subscription.status ?? 'unpaid'),
        },
        {
            key: 'billing',
            title: 'Facturare',
            render: (value, subscription) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "font-medium text-gray-900", children: subscription.billingCycle === 'monthly' ? 'Lunar' : 'Anual' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-gray-500", children: ["Urm\u0103toarea:", ' ', subscription.nextBillingDate
                                ? new Date(subscription.nextBillingDate).toLocaleDateString('ro-RO')
                                : 'N/A'] })] })),
        },
        {
            key: 'revenue',
            title: 'Venituri',
            render: (value, subscription) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "font-medium text-gray-900", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_12__/* .formatCurrency */ .vv)(subscription.totalRevenue || 0) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-gray-500", children: [subscription.paymentsCount || 0, " pl\u0103\u021Bi"] }), subscription.failedPayments && subscription.failedPayments > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-red-500 text-xs", children: [subscription.failedPayments, " e\u0219uate"] }))] })),
        },
        {
            key: 'dates',
            title: 'Perioada',
            render: (value, subscription) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-gray-900", children: ["\u00CEnceput:", ' ', subscription.startDate
                                ? new Date(subscription.startDate).toLocaleDateString('ro-RO')
                                : 'N/A'] }), subscription.endDate && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-gray-500", children: ["Sf\u00E2r\u0219it: ", new Date(subscription.endDate).toLocaleDateString('ro-RO')] }))] })),
        },
        {
            key: 'actions',
            title: 'Acțiuni',
            render: (value, subscription) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Dropdown__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { trigger: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "ghost", size: "sm", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .EllipsisVerticalIcon */ .Lz$, { className: "h-4 w-4" }) }), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "py-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => {
                                setSelectedSubscription(subscription);
                                setShowDetailsModal(true);
                            }, className: "block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", children: "Vezi detalii" }), subscription.status === 'active' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => handleSubscriptionAction(subscription, 'suspend'), className: "block w-full text-left px-4 py-2 text-sm text-yellow-700 hover:bg-yellow-50", children: "Suspend\u0103" })), (subscription.status === 'unpaid' || subscription.status === 'canceled') && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => handleSubscriptionAction(subscription, 'reactivate'), className: "block w-full text-left px-4 py-2 text-sm text-green-700 hover:bg-green-50", children: "Reactiveaz\u0103" })), subscription.status !== 'canceled' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => handleSubscriptionAction(subscription, 'cancel'), className: "block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50", children: "Anuleaz\u0103" })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => handleSubscriptionAction(subscription, 'sync'), className: "block w-full text-left px-4 py-2 text-sm text-blue-700 hover:bg-blue-50", children: "Sincronizeaz\u0103 cu Stripe" })] }) })),
        },
    ];
    if (isLoading) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center justify-center h-32", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, { size: "lg" }) }) }));
    }
    if (error) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center text-red-600", children: "Eroare la \u00EEnc\u0103rcarea abonamentelor" }) }));
    }
    const subscriptions = subscriptionsData?.pages?.flatMap(page => page.data || []) || [];
    const totalItems = subscriptionsData?.pages?.[0]?.pagination?.total || 0;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900", children: "Gestionarea abonamentelor" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-sm text-gray-500", children: [totalItems, " abonamente"] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-between items-center mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex gap-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "text", placeholder: "Caut\u0103 utilizatori...", value: searchTerm, onChange: e => setSearchTerm(e.target.value), className: "px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select", { value: statusFilter, onChange: e => setStatusFilter(e.target.value), className: "px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "all", children: "Toate statusurile" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "active", children: "Activ" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "paused", children: "Suspendat" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "canceled", children: "Anulat" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "past_due", children: "\u00CEnt\u00E2rziat" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select", { value: planFilter, onChange: e => setPlanFilter(e.target.value), className: "px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "all", children: "Toate planurile" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "basic", children: "Basic" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "premium", children: "Premium" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "enterprise", children: "Enterprise" })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex gap-4 text-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "font-semibold text-green-600", children: subscriptions.filter(s => s.status === 'active').length }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-gray-500", children: "Active" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "font-semibold text-yellow-600", children: subscriptions.filter(s => s.status === 'unpaid').length }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-gray-500", children: "Nepl\u0103tite" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "font-semibold text-red-600", children: subscriptions.filter(s => s.status === 'canceled').length }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-gray-500", children: "Anulate" })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Table__WEBPACK_IMPORTED_MODULE_10__/* .DataTable */ .bQ, { columns: columns, data: subscriptions, emptyMessage: "Nu au fost g\u0103site abonamente", loading: isLoading }), totalPages > 1 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Pagination__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { currentPage: currentPage, totalPages: totalPages, onPageChange: setCurrentPage }) }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay, { isOpen: showDetailsModal, onClose: () => setShowDetailsModal(false), title: "Detalii abonament", size: "lg", children: selectedSubscription && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-gray-50 p-4 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Informa\u021Bii Utilizator" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Nume:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-900", children: subscriptionDetails?.user
                                                        ? `${subscriptionDetails.user.firstName} ${subscriptionDetails.user.lastName}`
                                                        : selectedSubscription?.user
                                                            ? `${selectedSubscription.user.firstName} ${selectedSubscription.user.lastName}`
                                                            : 'Utilizator necunoscut' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Email:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-900", children: subscriptionDetails?.user?.email || selectedSubscription?.user?.email })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-gray-50 p-4 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Informa\u021Bii Plan" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Plan:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-900", children: subscriptionDetails?.plan?.name || selectedSubscription?.plan?.name })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Pre\u021B:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-sm font-medium text-gray-900", children: [(0,_utils_helpers__WEBPACK_IMPORTED_MODULE_12__/* .formatCurrency */ .vv)(subscriptionDetails?.plan?.price || selectedSubscription?.plan?.price || 0), "/lun\u0103"] })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-gray-50 p-4 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Status \u0219i Facturare" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Status:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-1", children: getStatusBadge(subscriptionDetails?.status || selectedSubscription?.status || '') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Ciclu facturare:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-900", children: (subscriptionDetails?.billingCycle || selectedSubscription?.billingCycle) ===
                                                        'monthly'
                                                        ? 'Lunar'
                                                        : 'Anual' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Data \u00EEnceperii:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-900", children: subscriptionDetails?.startDate || selectedSubscription?.startDate
                                                        ? new Date((subscriptionDetails?.startDate || selectedSubscription.startDate)).toLocaleDateString('ro-RO')
                                                        : 'N/A' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Urm\u0103toarea facturare:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-900", children: subscriptionDetails?.nextBillingDate || selectedSubscription?.nextBillingDate
                                                        ? new Date((subscriptionDetails?.nextBillingDate ||
                                                            selectedSubscription.nextBillingDate)).toLocaleDateString('ro-RO')
                                                        : 'N/A' })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-gray-50 p-4 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Statistici Financiare" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-3 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Venituri totale:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-green-600", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_12__/* .formatCurrency */ .vv)(subscriptionDetails?.totalRevenue || selectedSubscription?.totalRevenue || 0) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Pl\u0103\u021Bi procesate:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-blue-600", children: subscriptionDetails?.paymentsCount || selectedSubscription?.paymentsCount || 0 })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Pl\u0103\u021Bi e\u0219uate:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-red-600", children: subscriptionDetails?.failedPayments ||
                                                        selectedSubscription?.failedPayments ||
                                                        0 })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-gray-50 p-4 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Informa\u021Bii Stripe" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "ID Abonament Stripe:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-mono text-gray-900", children: subscriptionDetails?.stripeSubscriptionId ||
                                                        selectedSubscription?.stripeSubscriptionId ||
                                                        'N/A' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "ID Client Stripe:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-mono text-gray-900", children: subscriptionDetails?.stripeCustomerId ||
                                                        selectedSubscription?.stripeCustomerId ||
                                                        'N/A' })] })] })] })] })) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay, { isOpen: showConfirmModal, onClose: () => setShowConfirmModal(false), title: "Confirm\u0103 ac\u021Biunea", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ExclamationTriangleIcon */ .Pip, { className: "h-6 w-6 text-yellow-600" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-sm text-gray-700", children: ["E\u0219ti sigur c\u0103 vrei s\u0103", ' ', actionType === 'suspend'
                                            ? 'suspenzi'
                                            : actionType === 'reactivate'
                                                ? 'reactivezi'
                                                : actionType === 'cancel'
                                                    ? 'anulezi'
                                                    : actionType === 'sync'
                                                        ? 'sincronizezi'
                                                        : 'modifici', ' ', "acest abonament?"] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-end space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "secondary", onClick: () => setShowConfirmModal(false), children: "Anuleaz\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: actionType === 'cancel' ? 'danger' : 'primary', onClick: confirmAction, disabled: suspendSubscription.isPending ||
                                        reactivateSubscription.isPending ||
                                        cancelSubscription.isPending ||
                                        syncWithStripe.isPending, children: suspendSubscription.isPending ||
                                        reactivateSubscription.isPending ||
                                        cancelSubscription.isPending ||
                                        syncWithStripe.isPending
                                        ? 'Se procesează...'
                                        : 'Confirmă' })] })] }) })] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SubscriptionManager);


/***/ }),

/***/ 1235:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony exports SimplePagination, DetailedPagination, CompactPagination, usePagination */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3658);
/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(6103);





/**
 * Componenta Pagination principală
 */
const Pagination = ({ currentPage = 1, totalPages = 1, onPageChange, showFirstLast = true, showPrevNext = true, showPageNumbers = true, maxVisiblePages = 5, size = 'md', variant = 'default', disabled = false, className = '', ...rest }) => {
    // Validare props
    const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));
    const validTotalPages = Math.max(1, totalPages);
    const handlePageChange = (page) => {
        if (disabled || page === validCurrentPage || page < 1 || page > validTotalPages) {
            return;
        }
        onPageChange?.(page);
    };
    // Calculează paginile vizibile
    const getVisiblePages = () => {
        if (validTotalPages <= maxVisiblePages) {
            return Array.from({ length: validTotalPages }, (_, i) => i + 1);
        }
        const half = Math.floor(maxVisiblePages / 2);
        let start = validCurrentPage - half;
        let end = validCurrentPage + half;
        if (start < 1) {
            start = 1;
            end = maxVisiblePages;
        }
        if (end > validTotalPages) {
            end = validTotalPages;
            start = validTotalPages - maxVisiblePages + 1;
        }
        return Array.from({ length: end - start + 1 }, (_, i) => start + i);
    };
    const visiblePages = getVisiblePages();
    const showStartEllipsis = visiblePages.length > 0 && visiblePages[0] > 2;
    const showEndEllipsis = visiblePages.length > 0 && visiblePages[visiblePages.length - 1] < validTotalPages - 1;
    const sizeClasses = {
        sm: 'h-8 min-w-[2rem] text-sm',
        md: 'h-10 min-w-[2.5rem] text-sm',
        lg: 'h-12 min-w-[3rem] text-base',
    };
    const variantClasses = {
        default: {
            button: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50',
            active: 'border-primary-500 bg-primary-500 text-white hover:bg-primary-600',
            disabled: 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed',
        },
        outline: {
            button: 'border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50',
            active: 'border-primary-500 bg-primary-50 text-primary-600 hover:bg-primary-100',
            disabled: 'border-gray-200 bg-transparent text-gray-400 cursor-not-allowed',
        },
        ghost: {
            button: 'border-0 bg-transparent text-gray-700 hover:bg-gray-100',
            active: 'border-0 bg-primary-100 text-primary-600 hover:bg-primary-200',
            disabled: 'border-0 bg-transparent text-gray-400 cursor-not-allowed',
        },
    };
    const PaginationButton = ({ children, page, isActive = false, isDisabled = false, 'aria-label': ariaLabel, ...buttonProps }) => {
        const buttonClass = isDisabled
            ? variantClasses[variant].disabled
            : isActive
                ? variantClasses[variant].active
                : variantClasses[variant].button;
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { type: "button", className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('inline-flex items-center justify-center rounded-md font-medium transition-colors', 'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2', sizeClasses[size], buttonClass), onClick: () => handlePageChange(page), disabled: disabled || isDisabled, "aria-label": ariaLabel, "aria-current": isActive ? 'page' : undefined, ...buttonProps, children: children }));
    };
    const Ellipsis = () => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('inline-flex items-center justify-center text-gray-500', sizeClasses[size]), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .EllipsisHorizontalIcon */ .riS, { className: "w-5 h-5" }) }));
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("nav", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center gap-1', className), role: "navigation", "aria-label": "Navigare pagini", ...rest, children: [showFirstLast && validTotalPages > 1 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PaginationButton, { page: 1, isDisabled: validCurrentPage === 1, "aria-label": "Prima pagin\u0103", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronDoubleLeftIcon */ .RqJ, { className: "w-4 h-4" }) })), showPrevNext && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PaginationButton, { page: validCurrentPage - 1, isDisabled: validCurrentPage === 1, "aria-label": "Pagina anterioar\u0103", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronLeftIcon */ .YJP, { className: "w-4 h-4" }) })), showPageNumbers && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [showStartEllipsis && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PaginationButton, { page: 1, "aria-label": "Pagina 1", children: "1" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Ellipsis, {})] })), visiblePages.map(page => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PaginationButton, { page: page, isActive: page === validCurrentPage, "aria-label": `Pagina ${page}`, children: page }, page))), showEndEllipsis && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Ellipsis, {}), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PaginationButton, { page: validTotalPages, "aria-label": `Pagina ${validTotalPages}`, children: validTotalPages })] }))] })), showPrevNext && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PaginationButton, { page: validCurrentPage + 1, isDisabled: validCurrentPage === validTotalPages, "aria-label": "Pagina urm\u0103toare", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronRightIcon */ .vKP, { className: "w-4 h-4" }) })), showFirstLast && validTotalPages > 1 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PaginationButton, { page: validTotalPages, isDisabled: validCurrentPage === validTotalPages, "aria-label": "Ultima pagin\u0103", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronDoubleRightIcon */ .Y5X, { className: "w-4 h-4" }) }))] }));
};
/**
 * Pagination simplu cu doar prev/next
 */
const SimplePagination = ({ currentPage = 1, totalPages = 1, onPageChange, size = 'md', disabled = false, className = '', showPageInfo = true, ...rest }) => {
    const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));
    const validTotalPages = Math.max(1, totalPages);
    const handlePrevious = () => {
        if (validCurrentPage > 1) {
            onPageChange?.(validCurrentPage - 1);
        }
    };
    const handleNext = () => {
        if (validCurrentPage < validTotalPages) {
            onPageChange?.(validCurrentPage + 1);
        }
    };
    return (_jsxs("div", { className: cn('flex items-center justify-between', className), ...rest, children: [_jsx(Button, { variant: "outline", size: size, onClick: handlePrevious, disabled: disabled || validCurrentPage === 1, leftIcon: _jsx(ChevronLeftIcon, { className: "w-4 h-4" }), children: "Anterior" }), showPageInfo && (_jsxs("span", { className: "text-sm text-gray-700", children: ["Pagina ", validCurrentPage, " din ", validTotalPages] })), _jsx(Button, { variant: "outline", size: size, onClick: handleNext, disabled: disabled || validCurrentPage === validTotalPages, rightIcon: _jsx(ChevronRightIcon, { className: "w-4 h-4" }), children: "Urm\u0103torul" })] }));
};
/**
 * Pagination cu informații detaliate
 */
const DetailedPagination = ({ currentPage = 1, totalPages = 1, totalItems = 0, itemsPerPage = 10, onPageChange, onItemsPerPageChange, itemsPerPageOptions = [10, 25, 50, 100], showItemsPerPage = true, showItemsInfo = true, className = '', ...rest }) => {
    const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));
    const validTotalPages = Math.max(1, totalPages);
    const startItem = (validCurrentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(validCurrentPage * itemsPerPage, totalItems);
    return (_jsxs("div", { className: cn('flex flex-col sm:flex-row items-center justify-between gap-4', className), children: [_jsxs("div", { className: "flex items-center gap-4", children: [showItemsInfo && totalItems > 0 && (_jsxs("span", { className: "text-sm text-gray-700", children: ["Afi\u0219eaz\u0103 ", startItem, "-", endItem, " din ", totalItems, " rezultate"] })), showItemsPerPage && onItemsPerPageChange && (_jsxs("div", { className: "flex items-center gap-2", children: [_jsx("label", { htmlFor: "items-per-page", className: "text-sm text-gray-700", children: "Elemente per pagin\u0103:" }), _jsx("select", { id: "items-per-page", value: itemsPerPage, onChange: (e) => onItemsPerPageChange(Number(e.target.value)), className: "border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500", children: itemsPerPageOptions.map(option => (_jsx("option", { value: option, children: option }, option))) })] }))] }), _jsx(Pagination, { currentPage: validCurrentPage, totalPages: validTotalPages, onPageChange: onPageChange, ...rest })] }));
};
/**
 * Pagination compact pentru spații mici
 */
const CompactPagination = ({ currentPage = 1, totalPages = 1, onPageChange, disabled = false, className = '', ...rest }) => {
    const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));
    const validTotalPages = Math.max(1, totalPages);
    const handlePageChange = (page) => {
        if (disabled || page === validCurrentPage || page < 1 || page > validTotalPages) {
            return;
        }
        onPageChange?.(page);
    };
    return (_jsxs("div", { className: cn('flex items-center gap-2', className), ...rest, children: [_jsx(Button, { variant: "outline", size: "sm", onClick: () => handlePageChange(validCurrentPage - 1), disabled: disabled || validCurrentPage === 1, "aria-label": "Pagina anterioar\u0103", children: _jsx(ChevronLeftIcon, { className: "w-4 h-4" }) }), _jsxs("div", { className: "flex items-center gap-1", children: [_jsx("input", { type: "number", min: 1, max: validTotalPages, value: validCurrentPage, onChange: (e) => {
                            const page = parseInt(e.target.value, 10);
                            if (!isNaN(page)) {
                                handlePageChange(page);
                            }
                        }, className: "w-16 px-2 py-1 text-center border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500", disabled: disabled }), _jsxs("span", { className: "text-sm text-gray-500", children: ["din ", validTotalPages] })] }), _jsx(Button, { variant: "outline", size: "sm", onClick: () => handlePageChange(validCurrentPage + 1), disabled: disabled || validCurrentPage === validTotalPages, "aria-label": "Pagina urm\u0103toare", children: _jsx(ChevronRightIcon, { className: "w-4 h-4" }) })] }));
};
/**
 * Hook pentru gestionarea paginării
 */
const usePagination = ({ totalItems = 0, itemsPerPage = 10, initialPage = 1, } = {}) => {
    const [currentPage, setCurrentPage] = React.useState(initialPage);
    const [itemsPerPageState, setItemsPerPageState] = React.useState(itemsPerPage);
    const totalPages = Math.ceil(totalItems / itemsPerPageState);
    const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));
    // Ajustează pagina curentă dacă totalPages se schimbă
    React.useEffect(() => {
        if (validCurrentPage !== currentPage) {
            setCurrentPage(validCurrentPage);
        }
    }, [totalPages, validCurrentPage, currentPage]);
    const goToPage = (page) => {
        const newPage = Math.max(1, Math.min(page, totalPages));
        setCurrentPage(newPage);
    };
    const goToNextPage = () => {
        goToPage(currentPage + 1);
    };
    const goToPreviousPage = () => {
        goToPage(currentPage - 1);
    };
    const goToFirstPage = () => {
        goToPage(1);
    };
    const goToLastPage = () => {
        goToPage(totalPages);
    };
    const setItemsPerPage = (newItemsPerPage) => {
        setItemsPerPageState(newItemsPerPage);
        // Recalculează pagina curentă pentru a menține aproximativ aceleași elemente vizibile
        const currentFirstItem = (currentPage - 1) * itemsPerPageState + 1;
        const newPage = Math.ceil(currentFirstItem / newItemsPerPage);
        setCurrentPage(Math.max(1, newPage));
    };
    const getPageItems = (items = []) => {
        const startIndex = (validCurrentPage - 1) * itemsPerPageState;
        const endIndex = startIndex + itemsPerPageState;
        return items.slice(startIndex, endIndex);
    };
    const getPageInfo = () => {
        const startItem = (validCurrentPage - 1) * itemsPerPageState + 1;
        const endItem = Math.min(validCurrentPage * itemsPerPageState, totalItems);
        return {
            startItem,
            endItem,
            totalItems,
            currentPage: validCurrentPage,
            totalPages,
            itemsPerPage: itemsPerPageState,
            hasNextPage: validCurrentPage < totalPages,
            hasPreviousPage: validCurrentPage > 1,
        };
    };
    return {
        currentPage: validCurrentPage,
        totalPages,
        itemsPerPage: itemsPerPageState,
        goToPage,
        goToNextPage,
        goToPreviousPage,
        goToFirstPage,
        goToLastPage,
        setItemsPerPage,
        getPageItems,
        getPageInfo,
    };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Pagination);


/***/ }),

/***/ 1307:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   o: () => (/* binding */ useLanguage)
/* harmony export */ });
/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(888);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2389);
/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5518);



/**
 * Hook personalizat pentru gestionarea limbii în aplicație
 */
const useLanguage = () => {
    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__/* .useTranslation */ .Bd)();
    /**
     * Schimbă limba aplicației
     */
    const setLanguage = async (language) => {
        try {
            await (0,_i18n__WEBPACK_IMPORTED_MODULE_2__/* .changeLanguage */ .v2)(language);
            // Afișează notificare de succes
            const successMessage = language === 'ro'
                ? 'Limba a fost schimbată cu succes!'
                : 'Language changed successfully!';
            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__/* .toast */ .oR.success(successMessage);
        }
        catch (error) {
            console.error('Error changing language:', error);
            const errorMessage = (0,_i18n__WEBPACK_IMPORTED_MODULE_2__/* .getCurrentLanguage */ .UK)() === 'ro'
                ? 'Eroare la schimbarea limbii'
                : 'Error changing language';
            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__/* .toast */ .oR.error(errorMessage);
        }
    };
    /**
     * Obține limba curentă
     */
    const currentLanguage = (0,_i18n__WEBPACK_IMPORTED_MODULE_2__/* .getCurrentLanguage */ .UK)();
    /**
     * Obține lista limbilor suportate
     */
    const supportedLanguages = (0,_i18n__WEBPACK_IMPORTED_MODULE_2__/* .getSupportedLanguages */ .qX)();
    /**
     * Verifică dacă o limbă este suportată
     */
    const isLanguageSupported = (language) => {
        return supportedLanguages.includes(language);
    };
    /**
     * Obține numele afișat al unei limbi
     */
    const getLanguageDisplayName = (languageCode) => {
        return t(`languages.${languageCode}`, { defaultValue: languageCode.toUpperCase() });
    };
    /**
     * Obține toate limbile cu numele lor afișat
     */
    const getLanguagesWithNames = () => {
        return supportedLanguages.map(code => ({
            code,
            name: getLanguageDisplayName(code),
        }));
    };
    /**
     * Verifică dacă aplicația este în modul RTL (Right-to-Left)
     */
    const isRTL = () => {
        // Adaugă aici limbile RTL dacă sunt suportate în viitor
        const rtlLanguages = ['ar', 'he', 'fa'];
        return rtlLanguages.includes(currentLanguage);
    };
    return {
        // Funcția de traducere
        t,
        // Obiectul i18n pentru acces direct
        i18n,
        // Funcții pentru gestionarea limbii
        setLanguage,
        currentLanguage,
        supportedLanguages,
        isLanguageSupported,
        getLanguageDisplayName,
        getLanguagesWithNames,
        isRTL,
    };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (useLanguage)));


/***/ }),

/***/ 2552:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony exports ModalHeader, ModalTitle, ModalDescription, ModalBody, ModalFooter, ConfirmModal, AlertModal, LoadingModal, FormModal */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(961);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3658);
/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6103);
/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(9264);







/**
 * Hook pentru gestionarea focus-ului în modal
 */
const useFocusTrap = (isOpen, modalRef) => {
    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {
        if (!isOpen || !modalRef.current)
            return;
        const modal = modalRef.current;
        const focusableElements = modal.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        const handleTabKey = (e) => {
            if (e.key !== 'Tab')
                return;
            if (e.shiftKey) {
                if (document.activeElement === firstElement) {
                    lastElement?.focus();
                    e.preventDefault();
                }
            }
            else {
                if (document.activeElement === lastElement) {
                    firstElement?.focus();
                    e.preventDefault();
                }
            }
        };
        modal.addEventListener('keydown', handleTabKey);
        firstElement?.focus();
        return () => {
            modal.removeEventListener('keydown', handleTabKey);
        };
    }, [isOpen, modalRef]);
};
/**
 * Hook pentru gestionarea scroll-ului body când modal-ul este deschis
 */
const useBodyScrollLock = (isOpen) => {
    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {
        if (isOpen) {
            const originalStyle = window.getComputedStyle(document.body).overflow;
            document.body.style.overflow = 'hidden';
            return () => {
                document.body.style.overflow = originalStyle;
            };
        }
        return undefined;
    }, [isOpen]);
};
/**
 * Componenta Modal de bază
 */
const Modal = ({ isOpen = false, onClose, children, size = 'md', closeOnOverlayClick = true, closeOnEscape = true, showCloseButton = true, className = '', overlayClassName = '', preventBodyScroll = true, ...rest }) => {
    const modalRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
    // Hooks pentru funcționalitate
    useFocusTrap(isOpen, modalRef);
    if (preventBodyScroll) {
        useBodyScrollLock(isOpen);
    }
    // Gestionarea tastei Escape
    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {
        if (!isOpen || !closeOnEscape)
            return;
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                onClose?.();
            }
        };
        document.addEventListener('keydown', handleEscape);
        return () => document.removeEventListener('keydown', handleEscape);
    }, [isOpen, closeOnEscape, onClose]);
    if (!isOpen)
        return null;
    const sizeClasses = {
        xs: 'max-w-xs',
        sm: 'max-w-sm',
        md: 'max-w-md',
        lg: 'max-w-lg',
        xl: 'max-w-xl',
        '2xl': 'max-w-2xl',
        '3xl': 'max-w-3xl',
        '4xl': 'max-w-4xl',
        '5xl': 'max-w-5xl',
        '6xl': 'max-w-6xl',
        full: 'max-w-full',
    };
    const handleOverlayClick = (e) => {
        if (closeOnOverlayClick && e.target === e.currentTarget) {
            onClose?.();
        }
    };
    const modalContent = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_4__.cn)('fixed inset-0 z-50 flex items-center justify-center p-4', 'bg-black bg-opacity-50 backdrop-blur-sm', overlayClassName), onClick: handleOverlayClick, role: "dialog", "aria-modal": "true", "aria-labelledby": "modal-title", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { ref: modalRef, className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_4__.cn)('relative w-full bg-white rounded-lg shadow-xl', 'transform transition-all duration-300 ease-out', 'animate-scale-in', sizeClasses[size], className), ...rest, children: [showCloseButton && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: onClose, className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_4__.cn)('absolute top-4 right-4 z-10', 'p-1 rounded-md text-gray-400 hover:text-gray-600', 'hover:bg-gray-100 transition-colors duration-200', 'focus:outline-none focus:ring-2 focus:ring-primary-500'), "aria-label": "\u00CEnchide modal", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .XMarkIcon */ .fKY, { className: "w-5 h-5" }) })), children] }) }));
    return (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(modalContent, document.body);
};
/**
 * Header pentru modal
 */
const ModalHeader = ({ children, className = '', border = true, ...rest }) => {
    return (_jsx("div", { className: cn('px-6 py-4', border && 'border-b border-gray-200', className), ...rest, children: children }));
};
/**
 * Titlu pentru modal
 */
const ModalTitle = ({ children, className = '', as: Component = 'h2', ...rest }) => {
    return (_jsx(Component, { id: "modal-title", className: cn('text-lg font-semibold text-gray-900', className), ...rest, children: children }));
};
/**
 * Descriere pentru modal
 */
const ModalDescription = ({ children, className = '', ...rest }) => {
    return (_jsx("p", { className: cn('mt-1 text-sm text-gray-600', className), ...rest, children: children }));
};
/**
 * Conținutul principal al modal-ului
 */
const ModalBody = ({ children, className = '', padding = true, ...rest }) => {
    return (_jsx("div", { className: cn(padding && 'px-6 py-4', className), ...rest, children: children }));
};
/**
 * Footer pentru modal
 */
const ModalFooter = ({ children, className = '', border = true, justify = 'end', ...rest }) => {
    const justifyClasses = {
        start: 'justify-start',
        center: 'justify-center',
        end: 'justify-end',
        between: 'justify-between',
    };
    return (_jsx("div", { className: cn('px-6 py-4 flex items-center gap-3', border && 'border-t border-gray-200', justifyClasses[justify], className), ...rest, children: children }));
};
/**
 * Modal pentru confirmare
 */
const ConfirmModal = ({ isOpen, onClose, onConfirm, title = 'Confirmare', message = 'Ești sigur că vrei să continui?', confirmText = 'Confirmă', cancelText = 'Anulează', type = 'danger', loading = false, ...rest }) => {
    const handleConfirm = async () => {
        if (loading)
            return;
        await onConfirm?.();
    };
    return (_jsxs(Modal, { isOpen: isOpen, onClose: onClose, size: "sm", closeOnOverlayClick: !loading, closeOnEscape: !loading, showCloseButton: !loading, ...rest, children: [_jsx(ModalHeader, { children: _jsx(ModalTitle, { children: title }) }), _jsx(ModalBody, { children: _jsx("p", { className: "text-gray-600", children: message }) }), _jsxs(ModalFooter, { children: [_jsx(Button, { variant: "outline", onClick: onClose, disabled: loading, children: cancelText }), _jsx(Button, { variant: type, onClick: handleConfirm, loading: loading, children: confirmText })] })] }));
};
/**
 * Modal pentru alertă
 */
const AlertModal = ({ isOpen, onClose, title = 'Alertă', message, type = 'info', buttonText = 'OK', ...rest }) => {
    const typeIcons = {
        info: (_jsx("div", { className: "w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center", children: _jsx("svg", { className: "w-6 h-6 text-blue-600", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: _jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" }) }) })),
        success: (_jsx("div", { className: "w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center", children: _jsx("svg", { className: "w-6 h-6 text-green-600", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: _jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M5 13l4 4L19 7" }) }) })),
        warning: (_jsx("div", { className: "w-12 h-12 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center", children: _jsx("svg", { className: "w-6 h-6 text-yellow-600", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: _jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" }) }) })),
        error: (_jsx("div", { className: "w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center", children: _jsx("svg", { className: "w-6 h-6 text-red-600", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: _jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" }) }) })),
    };
    return (_jsx(Modal, { isOpen: isOpen, onClose: onClose, size: "sm", ...rest, children: _jsxs(ModalBody, { className: "text-center", children: [typeIcons[type], _jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: title }), _jsx("p", { className: "text-gray-600 mb-6", children: message }), _jsx(Button, { variant: "primary", onClick: onClose, fullWidth: true, children: buttonText })] }) }));
};
/**
 * Modal pentru loading
 */
const LoadingModal = ({ isOpen, message = 'Se încarcă...', ...rest }) => {
    return (_jsx(Modal, { isOpen: isOpen, size: "sm", closeOnOverlayClick: false, closeOnEscape: false, showCloseButton: false, ...rest, children: _jsxs(ModalBody, { className: "text-center py-8", children: [_jsx(LoadingSpinner, { size: "lg", className: "mx-auto mb-4" }), _jsx("p", { className: "text-gray-600", children: message })] }) }));
};
/**
 * Modal pentru formulare
 */
const FormModal = ({ isOpen, onClose, onSubmit, title, children, submitText = 'Salvează', cancelText = 'Anulează', loading = false, submitDisabled = false, ...rest }) => {
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (loading || submitDisabled)
            return;
        await onSubmit?.(e);
    };
    return (_jsx(Modal, { isOpen: isOpen, onClose: onClose, closeOnOverlayClick: !loading, closeOnEscape: !loading, showCloseButton: !loading, ...rest, children: _jsxs("form", { onSubmit: handleSubmit, children: [_jsx(ModalHeader, { children: _jsx(ModalTitle, { children: title }) }), _jsx(ModalBody, { children: children }), _jsxs(ModalFooter, { children: [_jsx(Button, { type: "button", variant: "outline", onClick: onClose, disabled: loading, children: cancelText }), _jsx(Button, { type: "submit", variant: "primary", loading: loading, disabled: submitDisabled, children: submitText })] })] }) }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Modal);


/***/ }),

/***/ 2948:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Rj: () => (/* binding */ useExportExpenses)
});

// UNUSED EXPORTS: useCreateExpense, useDeleteExpense, useExpenseStats, useExpenses, useUpdateExpense

// EXTERNAL MODULE: ./node_modules/@tanstack/react-query/build/modern/index.js + 21 modules
var modern = __webpack_require__(8035);
// EXTERNAL MODULE: ./node_modules/react-hot-toast/dist/index.mjs + 1 modules
var dist = __webpack_require__(888);
// EXTERNAL MODULE: ./src/services/api.ts + 1 modules
var api = __webpack_require__(4768);
;// ./src/services/expenseService.ts

/**
 * Serviciu pentru gestionarea cheltuielilor utilizatorilor
 */
class ExpenseService {
    /**
     * Exportă cheltuielile în format specificat
     */
    async exportExpenses(format, params = {}) {
        try {
            const queryParams = new URLSearchParams(params);
            const response = await api/* default */.A.get(`/export/${format}?${queryParams}`, {
                responseType: 'blob',
            });
            return response.data;
        }
        catch (error) {
            console.error('Eroare la exportul cheltuielilor:', error);
            throw error;
        }
    }
    /**
     * Obține lista cheltuielilor
     */
    async getExpenses(params = {}) {
        try {
            const queryParams = new URLSearchParams(params);
            const response = await api/* default */.A.get(`/expenses?${queryParams}`);
            return response.data;
        }
        catch (error) {
            console.error('Eroare la obținerea cheltuielilor:', error);
            throw error;
        }
    }
    /**
     * Creează o cheltuială nouă
     */
    async createExpense(expenseData) {
        try {
            const response = await api/* default */.A.post('/expenses', expenseData);
            return response.data;
        }
        catch (error) {
            console.error('Eroare la crearea cheltuielii:', error);
            throw error;
        }
    }
    /**
     * Actualizează o cheltuială
     */
    async updateExpense(id, expenseData) {
        try {
            const response = await api/* default */.A.put(`/expenses/${id}`, expenseData);
            return response.data;
        }
        catch (error) {
            console.error('Eroare la actualizarea cheltuielii:', error);
            throw error;
        }
    }
    /**
     * Șterge o cheltuială
     */
    async deleteExpense(id) {
        try {
            await api/* default */.A.delete(`/expenses/${id}`);
        }
        catch (error) {
            console.error('Eroare la ștergerea cheltuielii:', error);
            throw error;
        }
    }
    /**
     * Obține statisticile cheltuielilor
     */
    async getExpenseStats(params = {}) {
        try {
            const queryParams = new URLSearchParams(params);
            const response = await api/* default */.A.get(`/expenses/stats?${queryParams}`);
            return response.data;
        }
        catch (error) {
            console.error('Eroare la obținerea statisticilor:', error);
            throw error;
        }
    }
}
/* harmony default export */ const services_expenseService = (new ExpenseService());

;// ./src/hooks/useExpenses.ts



/**
 * Hook pentru obținerea cheltuielilor
 */
function useExpenses(params = {}) {
    return useQuery({
        queryKey: ['expenses', params],
        queryFn: () => expenseService.getExpenses(params),
        staleTime: 2 * 60 * 1000, // 2 minute
        gcTime: 10 * 60 * 1000, // 10 minute (renamed from cacheTime)
    });
}
/**
 * Hook pentru obținerea statisticilor cheltuielilor
 */
function useExpenseStats(params = {}) {
    return useQuery({
        queryKey: ['expense-stats', params],
        queryFn: () => expenseService.getExpenseStats(params),
        staleTime: 5 * 60 * 1000, // 5 minute
        gcTime: 15 * 60 * 1000, // 15 minute
    });
}
/**
 * Hook pentru crearea unei cheltuieli
 */
function useCreateExpense() {
    return useMutation({
        mutationFn: (data) => expenseService.createExpense(data),
        onSuccess: () => {
            toast.success('Cheltuiala a fost adăugată cu succes');
        },
        onError: (error) => {
            toast.error(error.response?.data?.message || 'Eroare la adăugarea cheltuielii');
        },
    });
}
/**
 * Hook pentru actualizarea unei cheltuieli
 */
function useUpdateExpense() {
    return useMutation({
        mutationFn: ({ id, data }) => expenseService.updateExpense(id, data),
        onSuccess: () => {
            toast.success('Cheltuiala a fost actualizată cu succes');
        },
        onError: (error) => {
            toast.error(error.response?.data?.message || 'Eroare la actualizarea cheltuielii');
        },
    });
}
/**
 * Hook pentru ștergerea unei cheltuieli
 */
function useDeleteExpense() {
    return useMutation({
        mutationFn: (id) => expenseService.deleteExpense(id),
        onSuccess: () => {
            toast.success('Cheltuiala a fost ștearsă cu succes');
        },
        onError: (error) => {
            toast.error(error.response?.data?.message || 'Eroare la ștergerea cheltuielii');
        },
    });
}
/**
 * Hook pentru exportul cheltuielilor
 */
function useExportExpenses() {
    return (0,modern/* useMutation */.n_)({
        mutationFn: ({ format, params = {} }) => services_expenseService.exportExpenses(format, params),
        onSuccess: (blob, { format }) => {
            // Determină extensia fișierului bazată pe format
            let fileExtension = 'csv';
            let mimeType = 'text/csv';
            if (format === 'pdf') {
                fileExtension = 'pdf';
                mimeType = 'application/pdf';
            }
            else if (format === 'excel') {
                fileExtension = 'xlsx';
                mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            }
            // Creează și descarcă fișierul
            const url = window.URL.createObjectURL(new Blob([blob], { type: mimeType }));
            const link = document.createElement('a');
            link.href = url;
            link.download = `cheltuieli_export_${new Date().toISOString().split('T')[0]}.${fileExtension}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            dist/* toast */.oR.success(`Exportul ${format.toUpperCase()} a fost descărcat cu succes`);
        },
        onError: (error) => {
            const errorMessage = error.response?.data?.message || `Eroare la exportul cheltuielilor`;
            dist/* toast */.oR.error(errorMessage);
        },
    });
}


/***/ }),

/***/ 4370:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2389);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4976);
/* harmony import */ var _hooks_useLanguage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1307);
/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(6103);
/* harmony import */ var _ui_Dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(8724);








/**
 * Layout pentru paginile publice care include header și footer
 */
const PublicLayout = ({ children }) => {
    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__/* .useTranslation */ .Bd)();
    const { currentLanguage, setLanguage } = (0,_hooks_useLanguage__WEBPACK_IMPORTED_MODULE_5__/* .useLanguage */ .o)();
    // Opțiuni pentru selectorul de limbă
    const languageOptions = [
        { value: 'ro', label: 'Română' },
        { value: 'en', label: 'English' },
    ];
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "min-h-screen bg-white", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("header", { className: "bg-white shadow-sm sticky top-0 z-50", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-between items-center py-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/", className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CurrencyDollarIcon */ .xmO, { className: "w-5 h-5 text-white" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent", children: "FinanceFlow" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("nav", { className: "hidden md:flex space-x-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/#features", className: "text-gray-600 hover:text-blue-600 transition-colors", children: t('landing.header.features') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/#testimonials", className: "text-gray-600 hover:text-blue-600 transition-colors", children: t('landing.header.testimonials') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/#pricing", className: "text-gray-600 hover:text-blue-600 transition-colors", children: t('landing.header.pricing') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ui_Dropdown__WEBPACK_IMPORTED_MODULE_7__/* .Select */ .l6, { value: languageOptions.find(opt => opt.value === currentLanguage), onChange: (option) => setLanguage(option.value), options: languageOptions, className: "w-32", buttonClassName: "bg-white hover:bg-gray-50 border-gray-300 hover:border-blue-400 rounded-t-lg px-4 py-2.5 text-sm font-medium text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm hover:shadow-md", optionClassName: "hover:bg-blue-50 font-medium", menuClassName: "border-t-0 rounded-t-none rounded-b-lg border-gray-300 shadow-lg", offset: 0 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/login", className: "text-gray-600 hover:text-blue-600 transition-colors", children: t('landing.header.login') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/register", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { variant: "primary", size: "sm", children: t('landing.header.register') }) })] })] }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("main", { className: "flex-1", children: children }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("footer", { className: "bg-gray-900 text-white py-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CurrencyDollarIcon */ .xmO, { className: "w-5 h-5 text-white" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-xl font-bold", children: "FinanceFlow" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-400", children: t('landing.footer.description') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-semibold mb-4", children: t('landing.footer.product.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "space-y-2 text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/features", className: "hover:text-white transition-colors", children: t('landing.footer.product.features') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/pricing", className: "hover:text-white transition-colors", children: t('landing.footer.product.pricing') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "#", className: "hover:text-white transition-colors", children: t('landing.footer.product.api') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "#", className: "hover:text-white transition-colors", children: t('landing.footer.product.integrations') }) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-semibold mb-4", children: t('landing.footer.support.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "space-y-2 text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/documentation", className: "hover:text-white transition-colors", children: t('landing.footer.support.documentation') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/help", className: "hover:text-white transition-colors", children: t('landing.footer.support.guides') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/contact", className: "hover:text-white transition-colors", children: t('landing.footer.support.contact') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "#", className: "hover:text-white transition-colors", children: t('landing.footer.support.status') }) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-semibold mb-4", children: t('landing.footer.legal.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "space-y-2 text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/terms", className: "hover:text-white transition-colors", children: t('landing.footer.legal.terms') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/privacy", className: "hover:text-white transition-colors", children: t('landing.footer.legal.privacy') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/cookies", className: "hover:text-white transition-colors", children: t('landing.footer.legal.cookies') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "#", className: "hover:text-white transition-colors", children: t('landing.footer.legal.gdpr') }) })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "border-t border-gray-800 mt-8 pt-8 text-center text-gray-400", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { children: t('landing.footer.copyright') }) })] }) })] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PublicLayout);


/***/ }),

/***/ 4768:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ services_api)
});

// EXTERNAL MODULE: ./node_modules/axios/index.js + 49 modules
var axios = __webpack_require__(4447);
;// ./src/utils/caseConverter.ts
/**
 * Utilitare pentru conversie între snake_case și camelCase în frontend
 * Sincronizat cu backend-ul pentru consistență
 */
/**
 * Convertește un string de la snake_case la camelCase
 */
function snakeToCamel(str) {
    if (!str || typeof str !== 'string')
        return str;
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}
/**
 * Convertește un string de la camelCase la snake_case
 */
function camelToSnake(str) {
    if (!str || typeof str !== 'string')
        return str;
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}
/**
 * Mapping specific pentru câmpurile aplicației
 * Sincronizat cu backend-ul
 */
const FIELD_MAPPING = {
    // Conversii speciale snake_case -> camelCase
    snake_to_camel: {
        created_at: 'createdAt',
        updated_at: 'updatedAt',
        email_verified: 'emailVerified',
        is_active: 'isActive',
        is_default: 'isDefault',
        is_recurring: 'isRecurring',
        category_id: 'categoryId',
        user_id: 'userId',
        expense_id: 'expenseId',
        plan_id: 'planId',
        subscription_id: 'subscriptionId',
        expense_date: 'date',
        payment_method: 'paymentMethod',
        receipt_url: 'receiptUrl',
        last_login: 'lastLogin',
        login_count: 'loginCount',
        password_reset_token: 'passwordResetToken',
        password_reset_expires: 'passwordResetExpires',
        email_verification_token: 'emailVerificationToken',
        subscription_status: 'subscriptionStatus',
        subscription_current_period_start: 'subscriptionCurrentPeriodStart',
        subscription_current_period_end: 'subscriptionCurrentPeriodEnd',
        stripe_customer_id: 'stripeCustomerId',
        plan_type: 'planType',
        monthly_expense_count: 'monthlyExpenseCount',
        monthly_expense_limit: 'monthlyExpenseLimit',
        last_usage_reset: 'lastUsageReset',
        trial_ends_at: 'trialEndsAt',
        refresh_token: 'refreshToken',
        first_name: 'firstName',
        last_name: 'lastName',
        budget_limit: 'budgetLimit',
        budget_period: 'budgetPeriod',
        sort_order: 'sortOrder',
        recurring_frequency: 'recurringFrequency',
        recurring_end_date: 'recurringEndDate',
        original_expense_id: 'originalExpenseId',
        stripe_price_id: 'stripePriceId',
        current_period_start: 'currentPeriodStart',
        current_period_end: 'currentPeriodEnd',
        trial_start: 'trialStart',
        trial_end: 'trialEnd',
        canceled_at: 'canceledAt',
        ended_at: 'endedAt',
        processed_at: 'processedAt',
        retry_count: 'retryCount',
        stripe_id: 'stripeId',
    },
    // Conversii inverse camelCase -> snake_case
    camel_to_snake: {},
};
// Generează mapping-ul invers automat
for (const [snake, camel] of Object.entries(FIELD_MAPPING.snake_to_camel)) {
    FIELD_MAPPING.camel_to_snake[camel] = snake;
}
/**
 * Convertește un obiect folosind mapping-ul specificat
 */
function convertWithMapping(obj, type) {
    if (obj === null || obj === undefined) {
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map(item => convertWithMapping(item, type));
    }
    if (typeof obj === 'object' && obj.constructor === Object) {
        const result = {};
        const mapping = FIELD_MAPPING[type];
        for (const [key, value] of Object.entries(obj)) {
            // Folosește mapping-ul specific dacă există
            const newKey = mapping?.[key] || (type === 'snake_to_camel' ? snakeToCamel(key) : camelToSnake(key));
            result[newKey] = convertWithMapping(value, type);
        }
        return result;
    }
    return obj;
}
/**
 * Funcții de conveniență pentru conversii rapide
 */
const CaseConverter = {
    /**
     * Convertește de la snake_case la camelCase
     */
    toCamel: (obj) => convertWithMapping(obj, 'snake_to_camel'),
    /**
     * Convertește de la camelCase la snake_case
     */
    toSnake: (obj) => convertWithMapping(obj, 'camel_to_snake'),
    /**
     * Convertește un string individual
     */
    stringToCamel: snakeToCamel,
    stringToSnake: camelToSnake,
    /**
     * Verifică dacă un string este în snake_case
     */
    isSnakeCase: (str) => {
        return /^[a-z]+(_[a-z]+)*$/.test(str);
    },
    /**
     * Verifică dacă un string este în camelCase
     */
    isCamelCase: (str) => {
        return /^[a-z]+([A-Z][a-z]*)*$/.test(str);
    },
    /**
     * Convertește parametrii de query pentru API
     */
    convertQueryParams: (params) => {
        return CaseConverter.toSnake(params);
    },
    /**
     * Convertește response-ul de la API
     */
    convertApiResponse: (response) => {
        return CaseConverter.toCamel(response);
    },
};
/**
 * Interceptor pentru axios pentru transformarea automată
 */
const createCaseInterceptors = () => {
    return {
        // Request interceptor - convertește la snake_case
        request: (config) => {
            if (config.data && typeof config.data === 'object') {
                config.data = CaseConverter.toSnake(config.data);
            }
            if (config.params && typeof config.params === 'object') {
                config.params = CaseConverter.toSnake(config.params);
            }
            return config;
        },
        // Response interceptor - convertește la camelCase
        response: (response) => {
            if (response.data && typeof response.data === 'object') {
                response.data = CaseConverter.toCamel(response.data);
            }
            return response;
        },
        // Error interceptor - convertește erorile la camelCase
        error: (error) => {
            if (error.response?.data && typeof error.response.data === 'object') {
                error.response.data = CaseConverter.toCamel(error.response.data);
            }
            return Promise.reject(error);
        },
    };
};
/**
 * Hook pentru transformarea automată în React Query
 */
const useApiTransform = () => {
    return {
        // Transformă datele înainte de trimitere
        transformRequest: (data) => {
            return CaseConverter.toSnake(data);
        },
        // Transformă response-ul după primire
        transformResponse: (data) => {
            return CaseConverter.toCamel(data);
        },
    };
};
/* harmony default export */ const caseConverter = ((/* unused pure expression or super */ null && (CaseConverter)));

;// ./src/services/api.ts


// Configurația de bază pentru API
const API_BASE_URL = typeof process !== 'undefined' && process.env && "http://localhost:3000/api"
    ? "http://localhost:3000/api"
    : 'http://localhost:3000/api';
// Creează instanța axios
const api = axios/* default */.Ay.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
        'X-Case-Format': 'camelCase', // Indică backend-ului că preferăm camelCase
    },
});
// Creează interceptorii de transformare
const caseInterceptors = createCaseInterceptors();
// Interceptor pentru request - adaugă token-ul de autentificare și transformă datele
api.interceptors.request.use(config => {
    const token = localStorage.getItem('accessToken');
    if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Aplică transformarea de case pentru request
    return caseInterceptors.request(config);
}, (error) => {
    return Promise.reject(error);
});
// Interceptor pentru response - gestionează erorile globale și transformă datele
api.interceptors.response.use((response) => {
    // Aplică transformarea de case pentru response
    return caseInterceptors.response(response);
}, (error) => {
    // Aplică transformarea de case pentru erori
    const transformedError = caseInterceptors.error(error);
    // Gestionează erorile de autentificare
    if (error.response?.status === 401) {
        // Token expirat sau invalid
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        window.location.href = '/login';
    }
    // Gestionează erorile de autorizare
    if (error.response?.status === 403) {
        // Acces interzis
        console.error('Acces interzis:', error.response.data?.message);
    }
    // Gestionează erorile de server
    if (error.response?.status && error.response.status >= 500) {
        console.error('Eroare de server:', error.response.data?.message);
    }
    return transformedError;
});
/* harmony default export */ const services_api = (api);


/***/ }),

/***/ 5191:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   bQ: () => (/* binding */ DataTable)
/* harmony export */ });
/* unused harmony exports TableHeader, TableBody, TableRow, TableHeaderCell, TableCell, TablePagination */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3658);
/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(6103);
/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(9264);






/**
 * Componenta Table de bază
 */
const Table = ({ children, className = '', striped = false, bordered = false, hover = false, compact = false, ...rest }) => {
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "overflow-x-auto", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("table", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('min-w-full divide-y divide-gray-200', striped && 'divide-y-0', bordered && 'border border-gray-200', className), ...rest, children: children }) }));
};
/**
 * Header pentru tabel
 */
const TableHeader = ({ children, className = '', ...rest }) => {
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("thead", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('bg-gray-50', className), ...rest, children: children }));
};
/**
 * Body pentru tabel
 */
const TableBody = ({ children, className = '', striped = false, hover = false, ...rest }) => {
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("tbody", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('bg-white divide-y divide-gray-200', striped && '[&>tr:nth-child(even)]:bg-gray-50', hover && '[&>tr]:hover:bg-gray-50 [&>tr]:transition-colors', className), ...rest, children: children }));
};
/**
 * Rând pentru tabel
 */
const TableRow = ({ children, className = '', clickable = false, onClick, selected = false, ...rest }) => {
    const isClickable = clickable || Boolean(onClick);
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("tr", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)(isClickable && [
            'cursor-pointer',
            'hover:bg-gray-50',
            'focus:bg-gray-50 focus:outline-none',
        ], selected && 'bg-primary-50', className), onClick: onClick, role: isClickable ? 'button' : undefined, tabIndex: isClickable ? 0 : undefined, onKeyDown: isClickable
            ? (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onClick?.(e);
                }
            }
            : undefined, ...rest, children: children }));
};
/**
 * Celulă header pentru tabel
 */
const TableHeaderCell = ({ children, className = '', sortable = false, sortDirection = null, onSort, align = 'left', width, ...rest }) => {
    const alignClasses = {
        left: 'text-left',
        center: 'text-center',
        right: 'text-right',
    };
    const handleSort = () => {
        if (sortable && onSort) {
            const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            onSort(newDirection);
        }
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("th", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider', alignClasses[align], sortable && 'cursor-pointer hover:bg-gray-100 select-none', className), style: { width }, onClick: handleSort, ...rest, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center gap-1', align === 'center' && 'justify-center', align === 'right' && 'justify-end'), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: children }), sortable && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "flex flex-col", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronUpIcon */ .Mtm, { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('w-3 h-3 -mb-1', sortDirection === 'asc' ? 'text-gray-900' : 'text-gray-400') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronDownIcon */ .D3D, { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('w-3 h-3', sortDirection === 'desc' ? 'text-gray-900' : 'text-gray-400') })] }))] }) }));
};
/**
 * Celulă pentru tabel
 */
const TableCell = ({ children, className = '', align = 'left', width, truncate = false, ...rest }) => {
    const alignClasses = {
        left: 'text-left',
        center: 'text-center',
        right: 'text-right',
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("td", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('px-6 py-4 whitespace-nowrap text-sm text-gray-900', alignClasses[align], truncate && 'truncate max-w-0', className), style: { width }, ...rest, children: children }));
};
/**
 * Tabel cu funcționalități avansate
 */
const DataTable = ({ data = [], columns = [], loading = false, emptyMessage = 'Nu există date de afișat', sortable = true, paginated = true, pageSize = 10, currentPage = 1, totalItems, onPageChange, onSort, onRowClick, selectedRows = [], onRowSelect, selectable = false, className = '', ...rest }) => {
    const [sortConfig, setSortConfig] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({ key: null, direction: null });
    const [internalPage, setInternalPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(currentPage);
    // Sortare locală dacă nu este controlată extern
    const sortedData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => {
        if (!sortable || !sortConfig.key || onSort) {
            return data;
        }
        return [...data].sort((a, b) => {
            const aValue = sortConfig.key ? a[sortConfig.key] : null;
            const bValue = sortConfig.key ? b[sortConfig.key] : null;
            if (aValue === bValue)
                return 0;
            const comparison = aValue < bValue ? -1 : 1;
            return sortConfig.direction === 'desc' ? -comparison : comparison;
        });
    }, [data, sortConfig, sortable, onSort]);
    // Paginare locală dacă nu este controlată extern
    const paginatedData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => {
        if (!paginated || onPageChange) {
            return sortedData;
        }
        const startIndex = (internalPage - 1) * pageSize;
        return sortedData.slice(startIndex, startIndex + pageSize);
    }, [sortedData, paginated, internalPage, pageSize, onPageChange]);
    const handleSort = (columnKey, direction) => {
        if (onSort) {
            onSort(columnKey, direction);
        }
        else {
            setSortConfig({ key: columnKey, direction });
        }
    };
    const handlePageChange = (page) => {
        if (onPageChange) {
            onPageChange(page);
        }
        else {
            setInternalPage(page);
        }
    };
    const handleRowSelect = (rowData, index) => {
        if (onRowSelect) {
            onRowSelect(rowData, index.toString());
        }
    };
    const handleSelectAll = () => {
        if (onRowSelect) {
            const allSelected = selectedRows.length === paginatedData.length;
            if (allSelected) {
                onRowSelect([], 'clear');
            }
            else {
                onRowSelect(paginatedData, 'all');
            }
        }
    };
    const isRowSelected = (rowData, index) => {
        return selectedRows.some(row => JSON.stringify(row) === JSON.stringify(rowData));
    };
    const totalPages = Math.ceil((totalItems || sortedData.length) / pageSize);
    const currentPageNumber = onPageChange ? currentPage : internalPage;
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('space-y-4', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Table, { striped: true, hover: true, ...rest, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TableHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(TableRow, { children: [selectable && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TableHeaderCell, { width: "50px", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", checked: selectedRows.length === paginatedData.length && paginatedData.length > 0, onChange: handleSelectAll, className: "rounded border-gray-300 text-primary-600 focus:ring-primary-500" }) })), columns.map(column => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TableHeaderCell, { sortable: sortable && column.sortable !== false, sortDirection: sortConfig.key === column.key ? sortConfig.direction : null, onSort: direction => handleSort(column.key, direction), align: column.align || 'left', width: column.width, children: column.title }, column.key)))] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TableBody, { children: loading ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TableRow, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TableCell, { colSpan: columns.length + (selectable ? 1 : 0), className: "text-center py-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { size: "md", className: "mx-auto" }) }) })) : paginatedData.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TableRow, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TableCell, { colSpan: columns.length + (selectable ? 1 : 0), className: "text-center py-8 text-gray-500", children: emptyMessage }) })) : (paginatedData.map((row, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(TableRow, { clickable: Boolean(onRowClick), onClick: () => onRowClick?.(row, index), selected: isRowSelected(row, index), children: [selectable && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TableCell, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", checked: isRowSelected(row, index), onChange: () => handleRowSelect(row, index), onClick: e => e.stopPropagation(), className: "rounded border-gray-300 text-primary-600 focus:ring-primary-500" }) })), columns.map(column => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TableCell, { align: column.align || 'left', width: column.width, truncate: column.truncate || undefined, children: column.render ? column.render(row[column.key], row, index) : row[column.key] }, column.key)))] }, index)))) })] }), paginated && totalPages > 1 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TablePagination, { currentPage: currentPageNumber, totalPages: totalPages, onPageChange: handlePageChange, totalItems: totalItems || sortedData.length, pageSize: pageSize }))] }));
};
/**
 * Componenta pentru paginare
 */
const TablePagination = ({ currentPage = 1, totalPages = 1, onPageChange, totalItems = 0, pageSize = 10, showInfo = true, showPageNumbers = true, maxPageNumbers = 5, className = '', }) => {
    const startItem = (currentPage - 1) * pageSize + 1;
    const endItem = Math.min(currentPage * pageSize, totalItems);
    const getPageNumbers = () => {
        const pages = [];
        const halfRange = Math.floor(maxPageNumbers / 2);
        let startPage = Math.max(1, currentPage - halfRange);
        const endPage = Math.min(totalPages, startPage + maxPageNumbers - 1);
        if (endPage - startPage + 1 < maxPageNumbers) {
            startPage = Math.max(1, endPage - maxPageNumbers + 1);
        }
        for (let i = startPage; i <= endPage; i++) {
            pages.push(i);
        }
        return pages;
    };
    const pageNumbers = getPageNumbers();
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200', className), children: [showInfo && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1 flex justify-between sm:hidden", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", size: "sm", onClick: () => onPageChange?.(currentPage - 1), disabled: currentPage <= 1, children: "Anterior" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", size: "sm", onClick: () => onPageChange?.(currentPage + 1), disabled: currentPage >= totalPages, children: "Urm\u0103tor" })] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "hidden sm:flex-1 sm:flex sm:items-center sm:justify-between", children: [showInfo && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-sm text-gray-700", children: ["Afi\u0219eaz\u0103 ", (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "font-medium", children: startItem }), " -", ' ', (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "font-medium", children: endItem }), " din", ' ', (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "font-medium", children: totalItems }), " rezultate"] }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", size: "sm", onClick: () => onPageChange?.(1), disabled: currentPage <= 1, className: "p-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronDoubleLeftIcon */ .RqJ, { className: "w-4 h-4" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", size: "sm", onClick: () => onPageChange?.(currentPage - 1), disabled: currentPage <= 1, className: "p-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronLeftIcon */ .YJP, { className: "w-4 h-4" }) }), showPageNumbers &&
                                pageNumbers.map(page => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: page === currentPage ? 'primary' : 'outline', size: "sm", onClick: () => onPageChange?.(page), className: "min-w-[2.5rem]", children: page }, page))), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", size: "sm", onClick: () => onPageChange?.(currentPage + 1), disabled: currentPage >= totalPages, className: "p-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronRightIcon */ .vKP, { className: "w-4 h-4" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", size: "sm", onClick: () => onPageChange?.(totalPages), disabled: currentPage >= totalPages, className: "p-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronDoubleRightIcon */ .Y5X, { className: "w-4 h-4" }) })] })] })] }));
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (Table)));


/***/ }),

/***/ 6103:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony exports PrimaryButton, SecondaryButton, OutlineButton, GhostButton, DangerButton, SuccessButton, WarningButton, IconButton, ButtonGroup, LinkButton, FloatingActionButton */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3658);
/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9264);




/**
 * Componenta Button reutilizabilă cu multiple variante și stări
 */
const Button = ({ children, variant = 'primary', size = 'md', loading = false, disabled = false, fullWidth = false, leftIcon = null, rightIcon = null, className = '', onClick, type = 'button', ...rest }) => {
    // Clase pentru variante
    const variantClasses = {
        primary: [
            'bg-primary-600 text-white border-primary-600',
            'hover:bg-primary-700 hover:border-primary-700',
            'focus:ring-primary-500',
            'disabled:bg-primary-300 disabled:border-primary-300',
        ].join(' '),
        secondary: [
            'bg-gray-600 text-white border-gray-600',
            'hover:bg-gray-700 hover:border-gray-700',
            'focus:ring-gray-500',
            'disabled:bg-gray-300 disabled:border-gray-300',
        ].join(' '),
        outline: [
            'bg-transparent text-primary-600 border-primary-600',
            'hover:bg-primary-50 hover:text-primary-700',
            'focus:ring-primary-500',
            'disabled:text-primary-300 disabled:border-primary-300',
        ].join(' '),
        ghost: [
            'bg-transparent text-gray-700 border-transparent',
            'hover:bg-gray-100 hover:text-gray-900',
            'focus:ring-gray-500',
            'disabled:text-gray-400',
        ].join(' '),
        danger: [
            'bg-red-600 text-white border-red-600',
            'hover:bg-red-700 hover:border-red-700',
            'focus:ring-red-500',
            'disabled:bg-red-300 disabled:border-red-300',
        ].join(' '),
        success: [
            'bg-green-600 text-white border-green-600',
            'hover:bg-green-700 hover:border-green-700',
            'focus:ring-green-500',
            'disabled:bg-green-300 disabled:border-green-300',
        ].join(' '),
        warning: [
            'bg-yellow-600 text-white border-yellow-600',
            'hover:bg-yellow-700 hover:border-yellow-700',
            'focus:ring-yellow-500',
            'disabled:bg-yellow-300 disabled:border-yellow-300',
        ].join(' '),
        white: [
            'bg-white text-gray-900 border-white',
            'hover:bg-gray-50 hover:text-gray-900',
            'focus:ring-gray-500',
            'disabled:bg-gray-100 disabled:text-gray-400',
        ].join(' '),
    };
    // Clase pentru mărimi
    const sizeClasses = {
        xs: 'px-2 py-1 text-xs',
        sm: 'px-3 py-1.5 text-sm',
        md: 'px-4 py-2 text-sm',
        lg: 'px-6 py-3 text-base',
        xl: 'px-8 py-4 text-lg',
    };
    // Clase pentru iconițe în funcție de mărime
    const iconSizeClasses = {
        xs: 'h-3 w-3',
        sm: 'h-4 w-4',
        md: 'h-4 w-4',
        lg: 'h-5 w-5',
        xl: 'h-6 w-6',
    };
    const isDisabled = disabled || loading;
    const handleClick = (e) => {
        if (isDisabled) {
            e.preventDefault();
            return;
        }
        onClick?.(e);
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { type: type, onClick: handleClick, disabled: isDisabled, className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)(
        // Clase de bază
        'inline-flex items-center justify-center', 'border font-medium rounded-lg', 'transition-all duration-200 ease-in-out', 'focus:outline-none focus:ring-2 focus:ring-offset-2', 'disabled:cursor-not-allowed disabled:opacity-60', 
        // Varianta
        variantClasses[variant], 
        // Mărimea
        sizeClasses[size], 
        // Lățime completă
        fullWidth && 'w-full', 
        // Clase personalizate
        className), ...rest, children: [leftIcon && !loading && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)(iconSizeClasses[size], children && 'mr-2'), children: leftIcon })), loading && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)(children && 'mr-2'), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, { size: size === 'xs' || size === 'sm' ? 'sm' : 'md', color: "currentColor" }) })), children && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: loading ? 'opacity-70' : '', children: children })), rightIcon && !loading && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_2__.cn)(iconSizeClasses[size], children && 'ml-2'), children: rightIcon }))] }));
};
/**
 * Buton primar - varianta principală
 */
const PrimaryButton = (props) => (_jsx(Button, { variant: "primary", ...props }));
/**
 * Buton secundar
 */
const SecondaryButton = (props) => (_jsx(Button, { variant: "secondary", ...props }));
/**
 * Buton cu contur
 */
const OutlineButton = (props) => (_jsx(Button, { variant: "outline", ...props }));
/**
 * Buton transparent
 */
const GhostButton = (props) => (_jsx(Button, { variant: "ghost", ...props }));
/**
 * Buton de pericol
 */
const DangerButton = (props) => (_jsx(Button, { variant: "danger", ...props }));
/**
 * Buton de succes
 */
const SuccessButton = (props) => (_jsx(Button, { variant: "success", ...props }));
/**
 * Buton de avertizare
 */
const WarningButton = (props) => (_jsx(Button, { variant: "warning", ...props }));
/**
 * Buton pentru iconițe - rotund și fără text
 */
const IconButton = ({ icon, size = 'md', variant = 'ghost', className = '', ...props }) => {
    const iconSizes = {
        xs: 'p-1',
        sm: 'p-1.5',
        md: 'p-2',
        lg: 'p-3',
        xl: 'p-4',
    };
    return (_jsx(Button, { variant: variant, className: cn('rounded-full', iconSizes[size], className), ...props, children: icon }));
};
/**
 * Grup de butoane
 */
const ButtonGroup = ({ children, className = '', orientation = 'horizontal', ...props }) => {
    return (_jsx("div", { className: cn('inline-flex', orientation === 'horizontal' ? 'flex-row' : 'flex-col', '[&>button]:rounded-none', '[&>button:first-child]:rounded-l-lg', '[&>button:last-child]:rounded-r-lg', orientation === 'vertical' && [
            '[&>button:first-child]:rounded-t-lg [&>button:first-child]:rounded-l-none',
            '[&>button:last-child]:rounded-b-lg [&>button:last-child]:rounded-r-none',
        ], '[&>button:not(:first-child)]:border-l-0', orientation === 'vertical' && '[&>button:not(:first-child)]:border-l [&>button:not(:first-child)]:border-t-0', className), ...props, children: children }));
};
/**
 * Buton de tip link
 */
const LinkButton = ({ children, className = '', ...props }) => {
    return (_jsx(Button, { variant: "ghost", className: cn('p-0 h-auto font-normal text-primary-600 hover:text-primary-700 hover:bg-transparent hover:underline', className), ...props, children: children }));
};
/**
 * Buton floating action (FAB)
 */
const FloatingActionButton = ({ icon, className = '', ...props }) => {
    return (_jsx(Button, { variant: "primary", className: cn('fixed bottom-6 right-6 rounded-full p-4 shadow-lg hover:shadow-xl z-50', className), ...props, children: icon }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);


/***/ }),

/***/ 6215:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony exports EmailInput, PasswordInput, NumberInput, SearchInput, PhoneInput, UrlInput, DateInput, TimeInput, DateTimeInput */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3658);




/**
 * Componenta Input reutilizabilă pentru formulare
 */
const Input = (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(({ label, type = 'text', placeholder, value, onChange, onBlur, onFocus, error, success, hint, required = false, disabled = false, readOnly = false, size = 'md', leftIcon, rightIcon, className = '', inputClassName = '', labelClassName = '', id, name, ...rest }, ref) => {
    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const [focused, setFocused] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    // Generează un ID unic dacă nu este furnizat
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    // Determină tipul real al input-ului
    const inputType = type === 'password' && showPassword ? 'text' : type;
    // Clase pentru mărimi
    const sizeClasses = {
        sm: 'px-3 py-2 text-sm',
        md: 'px-4 py-2.5 text-sm',
        lg: 'px-4 py-3 text-base',
    };
    // Clase pentru iconițe în funcție de mărime
    const iconSizeClasses = {
        sm: 'h-4 w-4',
        md: 'h-5 w-5',
        lg: 'h-6 w-6',
    };
    // Determină starea input-ului
    const hasError = Boolean(error);
    const hasSuccess = Boolean(success) && !hasError;
    const hasIcon = Boolean(leftIcon || rightIcon || type === 'password');
    const handleFocus = (e) => {
        setFocused(true);
        onFocus?.(e);
    };
    const handleBlur = (e) => {
        setFocused(false);
        onBlur?.(e);
    };
    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('w-full', className), children: [label && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", { htmlFor: inputId, className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('block text-sm font-medium mb-2', hasError ? 'text-red-700' : 'text-gray-700', disabled && 'text-gray-400', labelClassName), children: [label, required && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-red-500 ml-1", "aria-label": "obligatoriu", children: "*" }))] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative", children: [leftIcon && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)(iconSizeClasses[size], hasError ? 'text-red-400' : 'text-gray-400'), children: leftIcon }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { ref: ref, id: inputId, name: name, type: inputType, value: value, onChange: onChange, onFocus: handleFocus, onBlur: handleBlur, placeholder: placeholder, required: required, disabled: disabled, readOnly: readOnly, className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)(
                        // Clase de bază
                        'block w-full border rounded-lg transition-all duration-200', 'focus:outline-none focus:ring-2 focus:ring-offset-0', 'placeholder:text-gray-400', 
                        // Mărime
                        sizeClasses[size], 
                        // Padding pentru iconițe
                        leftIcon && 'pl-10', (rightIcon || type === 'password') && 'pr-10', 
                        // Stări
                        !disabled &&
                            !readOnly && [
                            hasError
                                ? ['border-red-300 text-red-900', 'focus:border-red-500 focus:ring-red-500']
                                : hasSuccess
                                    ? [
                                        'border-green-300 text-green-900',
                                        'focus:border-green-500 focus:ring-green-500',
                                    ]
                                    : [
                                        'border-gray-300 text-gray-900',
                                        'focus:border-primary-500 focus:ring-primary-500',
                                        'hover:border-gray-400',
                                    ],
                        ], disabled && ['bg-gray-50 border-gray-200 text-gray-500', 'cursor-not-allowed'], readOnly && ['bg-gray-50 border-gray-200', 'cursor-default'], inputClassName), ...rest }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute inset-y-0 right-0 pr-3 flex items-center", children: type === 'password' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { type: "button", onClick: togglePasswordVisibility, className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('text-gray-400 hover:text-gray-600 focus:outline-none', iconSizeClasses[size]), "aria-label": showPassword ? 'Ascunde parola' : 'Arată parola', children: showPassword ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .EyeSlashIcon */ .G3N, { className: iconSizeClasses[size] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .EyeIcon */ .bMW, { className: iconSizeClasses[size] })) })) : hasError ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ExclamationCircleIcon */ .$QZ, { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)(iconSizeClasses[size], 'text-red-400') })) : hasSuccess ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CheckCircleIcon */ .C1y, { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)(iconSizeClasses[size], 'text-green-400') })) : rightIcon ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)(iconSizeClasses[size], 'text-gray-400'), children: rightIcon })) : null })] }), (error || success || hint) && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mt-2 flex items-start space-x-1", children: [(error || success) && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "flex-shrink-0 mt-0.5", children: error ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ExclamationCircleIcon */ .$QZ, { className: "h-4 w-4 text-red-400" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CheckCircleIcon */ .C1y, { className: "h-4 w-4 text-green-400" })) })), hint && !error && !success && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .InformationCircleIcon */ .KSg, { className: "h-4 w-4 text-gray-400 flex-shrink-0 mt-0.5" })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('text-sm', error ? 'text-red-600' : success ? 'text-green-600' : 'text-gray-600'), children: error || success || hint })] }))] }));
});
Input.displayName = 'Input';
/**
 * Input pentru email cu validare
 */
const EmailInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)((props, ref) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Input, { ref: ref, type: "email", placeholder: "<EMAIL>", ...props })));
EmailInput.displayName = 'EmailInput';
/**
 * Input pentru parolă
 */
const PasswordInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)((props, ref) => (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Input, { ref: ref, type: "password", placeholder: "\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022", ...props }));
PasswordInput.displayName = 'PasswordInput';
/**
 * Input pentru numere
 */
const NumberInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(({ min, max, step = 1, ...props }, ref) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Input, { ref: ref, type: "number", min: min, max: max, step: step, ...props })));
NumberInput.displayName = 'NumberInput';
/**
 * Input pentru căutare
 */
const SearchInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(({ onSearch, ...props }, ref) => {
    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && onSearch) {
            onSearch(e.currentTarget.value);
        }
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Input, { ref: ref, type: "search", placeholder: "Caut\u0103...", onKeyDown: handleKeyDown, ...props }));
});
SearchInput.displayName = 'SearchInput';
/**
 * Input pentru telefon
 */
const PhoneInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)((props, ref) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Input, { ref: ref, type: "tel", placeholder: "+40 123 456 789", ...props })));
PhoneInput.displayName = 'PhoneInput';
/**
 * Input pentru URL
 */
const UrlInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)((props, ref) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Input, { ref: ref, type: "url", placeholder: "https://exemplu.com", ...props })));
UrlInput.displayName = 'UrlInput';
/**
 * Input pentru dată
 */
const DateInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)((props, ref) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Input, { ref: ref, type: "date", ...props })));
DateInput.displayName = 'DateInput';
/**
 * Input pentru timp
 */
const TimeInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)((props, ref) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Input, { ref: ref, type: "time", ...props })));
TimeInput.displayName = 'TimeInput';
/**
 * Input pentru dată și timp
 */
const DateTimeInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)((props, ref) => (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Input, { ref: ref, type: "datetime-local", ...props }));
DateTimeInput.displayName = 'DateTimeInput';
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Input);


/***/ }),

/***/ 6392:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ admin_RevenueCharts)
});

// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(4848);
// EXTERNAL MODULE: ./node_modules/chart.js/dist/chart.js + 1 modules
var chart = __webpack_require__(68);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(6540);
// EXTERNAL MODULE: ./node_modules/react-chartjs-2/dist/index.js
var dist = __webpack_require__(4731);
// EXTERNAL MODULE: ./src/utils/helpers.ts
var helpers = __webpack_require__(3658);
// EXTERNAL MODULE: ./src/components/ui/Card.tsx
var ui_Card = __webpack_require__(125);
// EXTERNAL MODULE: ./src/components/ui/LoadingSpinner.tsx
var LoadingSpinner = __webpack_require__(9264);
;// ./src/components/ui/ChartContainer.tsx





/**
 * Container pentru grafice cu loading state și error handling
 */
const ChartContainer = ({ title, subtitle, children, isLoading = false, error = null, actions, className, ...props }) => {
    if (error) {
        return ((0,jsx_runtime.jsxs)(ui_Card/* default */.Ay, { className: (0,helpers.cn)('p-6', className), ...props, children: [(0,jsx_runtime.jsxs)("div", { className: "flex items-center justify-between mb-4", children: [(0,jsx_runtime.jsxs)("div", { children: [title && ((0,jsx_runtime.jsx)("h3", { className: "text-lg font-semibold text-gray-900", children: title })), subtitle && ((0,jsx_runtime.jsx)("p", { className: "text-sm text-gray-600 mt-1", children: subtitle }))] }), actions && ((0,jsx_runtime.jsx)("div", { className: "flex items-center space-x-2", children: actions }))] }), (0,jsx_runtime.jsx)("div", { className: "flex items-center justify-center h-64 bg-gray-50 rounded-lg", children: (0,jsx_runtime.jsxs)("div", { className: "text-center", children: [(0,jsx_runtime.jsx)("svg", { className: "w-12 h-12 text-gray-400 mx-auto mb-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: (0,jsx_runtime.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" }) }), (0,jsx_runtime.jsx)("p", { className: "text-gray-600 font-medium", children: "Eroare la \u00EEnc\u0103rcarea graficului" }), (0,jsx_runtime.jsx)("p", { className: "text-sm text-gray-500 mt-1", children: error })] }) })] }));
    }
    return ((0,jsx_runtime.jsxs)(ui_Card/* default */.Ay, { className: (0,helpers.cn)('p-6', className), ...props, children: [(0,jsx_runtime.jsxs)("div", { className: "flex items-center justify-between mb-4", children: [(0,jsx_runtime.jsxs)("div", { children: [title && ((0,jsx_runtime.jsx)("h3", { className: "text-lg font-semibold text-gray-900", children: title })), subtitle && ((0,jsx_runtime.jsx)("p", { className: "text-sm text-gray-600 mt-1", children: subtitle }))] }), actions && ((0,jsx_runtime.jsx)("div", { className: "flex items-center space-x-2", children: actions }))] }), (0,jsx_runtime.jsxs)("div", { className: "relative", children: [isLoading && ((0,jsx_runtime.jsx)("div", { className: "absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg", children: (0,jsx_runtime.jsx)(LoadingSpinner/* default */.Ay, { size: "lg" }) })), (0,jsx_runtime.jsx)("div", { className: (0,helpers.cn)('transition-opacity duration-200', isLoading && 'opacity-50'), children: children })] })] }));
};
/**
 * Container simplu pentru metrici rapide
 */
const MetricsContainer = ({ title, metrics = [], className, ...props }) => {
    return (_jsxs(Card, { className: cn('p-6', className), ...props, children: [title && (_jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: title })), _jsx("div", { className: "grid grid-cols-2 gap-4", children: metrics.map((metric, index) => (_jsxs("div", { className: "text-center", children: [_jsx("p", { className: "text-2xl font-bold text-gray-900", children: metric.value }), _jsx("p", { className: "text-sm text-gray-600", children: metric.label }), metric.change && (_jsxs("p", { className: cn('text-xs mt-1', metric.change > 0 ? 'text-green-600' :
                                metric.change < 0 ? 'text-red-600' : 'text-gray-600'), children: [metric.change > 0 ? '+' : '', metric.change, "%"] }))] }, index))) })] }));
};
/* harmony default export */ const ui_ChartContainer = (ChartContainer);

// EXTERNAL MODULE: ./src/hooks/useAdminData.ts + 1 modules
var useAdminData = __webpack_require__(71);
;// ./src/pages/admin/RevenueCharts.tsx







// Înregistrează componentele Chart.js
chart/* Chart */.t1.register(chart/* CategoryScale */.PP, chart/* LinearScale */.kc, chart/* PointElement */.FN, chart/* LineElement */.No, chart/* BarElement */.E8, chart/* ArcElement */.Bs, chart/* Title */.hE, chart/* Tooltip */.m_, chart/* Legend */.s$, chart/* Filler */.dN);
const RevenueCharts = ({ dashboardStats }) => {
    const { data: revenueStats, isLoading } = (0,useAdminData/* useRevenueData */.wr)();
    const { data: planStats } = (0,useAdminData/* usePlanStats */.gm)();
    const { data: subscriptionStats } = (0,useAdminData/* useSubscriptionStats */.cV)();
    // Generează date mock pentru ultimele 12 luni
    const monthlyRevenueData = (0,react.useMemo)(() => {
        const months = [];
        const revenues = [];
        const currentDate = new Date();
        for (let i = 11; i >= 0; i--) {
            const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            months.push(date.toLocaleDateString('ro-RO', { month: 'short', year: '2-digit' }));
            // Simulează creșterea veniturilor cu variații
            const baseRevenue = 5000;
            const growth = (11 - i) * 200;
            const variation = Math.random() * 1000 - 500;
            revenues.push(Math.max(0, baseRevenue + growth + variation));
        }
        return { months, revenues };
    }, []);
    // Date pentru graficul de linie - venituri în timp
    const lineChartData = {
        labels: monthlyRevenueData.months,
        datasets: [
            {
                label: 'Venituri lunare',
                data: monthlyRevenueData.revenues,
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgb(59, 130, 246)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
            },
        ],
    };
    const lineChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false,
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 1,
                callbacks: {
                    label(context) {
                        return `Venituri: ${(0,helpers/* formatCurrency */.vv)(context.parsed.y)}`;
                    },
                },
            },
        },
        scales: {
            x: {
                grid: {
                    display: false,
                },
                ticks: {
                    color: '#6B7280',
                },
            },
            y: {
                grid: {
                    color: 'rgba(107, 114, 128, 0.1)',
                },
                ticks: {
                    color: '#6B7280',
                    callback(value) {
                        return (0,helpers/* formatCurrency */.vv)(Number(value));
                    },
                },
            },
        },
    };
    // Date pentru graficul cu bare - venituri pe planuri
    const planRevenueData = (0,react.useMemo)(() => {
        if (!planStats || !Array.isArray(planStats)) {
            return {
                labels: ['Gratuit', 'Basic', 'Premium', 'Enterprise'],
                data: [0, 2500, 4500, 8000],
                colors: ['#6B7280', '#3B82F6', '#8B5CF6', '#10B981'],
            };
        }
        return {
            labels: planStats.map((plan) => plan.planName),
            data: planStats.map((plan) => plan.revenue || 0),
            colors: ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B'],
        };
    }, [planStats]);
    const barChartData = {
        labels: planRevenueData.labels,
        datasets: [
            {
                label: 'Venituri pe plan',
                data: planRevenueData.data,
                backgroundColor: planRevenueData.colors,
                borderColor: planRevenueData.colors,
                borderWidth: 1,
                borderRadius: 8,
                borderSkipped: false,
            },
        ],
    };
    const barChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false,
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                callbacks: {
                    label(context) {
                        return `Venituri: ${(0,helpers/* formatCurrency */.vv)(context.parsed.y)}`;
                    },
                },
            },
        },
        scales: {
            x: {
                grid: {
                    display: false,
                },
                ticks: {
                    color: '#6B7280',
                },
            },
            y: {
                grid: {
                    color: 'rgba(107, 114, 128, 0.1)',
                },
                ticks: {
                    color: '#6B7280',
                    callback(value) {
                        return (0,helpers/* formatCurrency */.vv)(Number(value));
                    },
                },
            },
        },
    };
    // Date pentru graficul circular - distribuția utilizatorilor
    const userDistributionData = (0,react.useMemo)(() => {
        const totalUsers = subscriptionStats?.totalSubscriptions || 100;
        const activeUsers = subscriptionStats?.activeSubscriptions || 25;
        const inactiveUsers = totalUsers - activeUsers;
        return {
            labels: ['Abonamente inactive', 'Abonamente active'],
            data: [inactiveUsers, activeUsers],
            colors: ['#6B7280', '#8B5CF6'],
        };
    }, [subscriptionStats]);
    const doughnutChartData = {
        labels: userDistributionData.labels,
        datasets: [
            {
                data: userDistributionData.data,
                backgroundColor: userDistributionData.colors,
                borderColor: '#fff',
                borderWidth: 3,
                hoverOffset: 4,
            },
        ],
    };
    const doughnutChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true,
                    color: '#6B7280',
                },
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                callbacks: {
                    label(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed.y / total) * 100).toFixed(1);
                        return `${context.label}: ${context.parsed.y} (${percentage}%)`;
                    },
                },
            },
        },
        cutout: '60%',
    };
    if (isLoading) {
        return ((0,jsx_runtime.jsxs)("div", { className: "space-y-6", children: [(0,jsx_runtime.jsx)(ui_ChartContainer, { title: "Evolu\u021Bia veniturilor", isLoading: true }), (0,jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [(0,jsx_runtime.jsx)(ui_ChartContainer, { title: "Venituri pe planuri", isLoading: true }), (0,jsx_runtime.jsx)(ui_ChartContainer, { title: "Distribu\u021Bia utilizatorilor", isLoading: true })] }), (0,jsx_runtime.jsx)(ui_ChartContainer, { title: "Metrici rapide", isLoading: true })] }));
    }
    return ((0,jsx_runtime.jsxs)("div", { className: "space-y-6", children: [(0,jsx_runtime.jsx)(ui_ChartContainer, { title: "Evolu\u021Bia veniturilor", subtitle: "Ultimele 12 luni", className: "h-80", children: (0,jsx_runtime.jsx)(dist/* Line */.N1, { data: lineChartData, options: lineChartOptions }) }), (0,jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [(0,jsx_runtime.jsx)(ui_ChartContainer, { title: "Venituri pe planuri", className: "h-64", children: (0,jsx_runtime.jsx)(dist/* Bar */.yP, { data: barChartData, options: barChartOptions }) }), (0,jsx_runtime.jsx)(ui_ChartContainer, { title: "Distribu\u021Bia utilizatorilor", className: "h-64", children: (0,jsx_runtime.jsx)(dist/* Doughnut */.nu, { data: doughnutChartData, options: doughnutChartOptions }) })] }), (0,jsx_runtime.jsx)(ui_ChartContainer, { title: "Metrici rapide", children: (0,jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-4", children: [(0,jsx_runtime.jsxs)("div", { className: "text-center p-4 bg-blue-50 rounded-lg", children: [(0,jsx_runtime.jsx)("p", { className: "text-2xl font-bold text-blue-600", children: (0,helpers/* formatCurrency */.vv)(subscriptionStats?.monthlyRecurringRevenue || 0) }), (0,jsx_runtime.jsx)("p", { className: "text-sm text-blue-600 font-medium", children: "MRR" }), (0,jsx_runtime.jsx)("p", { className: "text-xs text-gray-500", children: "Monthly Recurring Revenue" })] }), (0,jsx_runtime.jsxs)("div", { className: "text-center p-4 bg-green-50 rounded-lg", children: [(0,jsx_runtime.jsx)("p", { className: "text-2xl font-bold text-green-600", children: (0,helpers/* formatCurrency */.vv)((subscriptionStats?.monthlyRecurringRevenue || 0) * 12) }), (0,jsx_runtime.jsx)("p", { className: "text-sm text-green-600 font-medium", children: "ARR" }), (0,jsx_runtime.jsx)("p", { className: "text-xs text-gray-500", children: "Annual Recurring Revenue" })] }), (0,jsx_runtime.jsxs)("div", { className: "text-center p-4 bg-purple-50 rounded-lg", children: [(0,jsx_runtime.jsx)("p", { className: "text-2xl font-bold text-purple-600", children: subscriptionStats?.activeSubscriptions || 0 }), (0,jsx_runtime.jsx)("p", { className: "text-sm text-purple-600 font-medium", children: "Active" }), (0,jsx_runtime.jsx)("p", { className: "text-xs text-gray-500", children: "Abonamente active" })] }), (0,jsx_runtime.jsxs)("div", { className: "text-center p-4 bg-orange-50 rounded-lg", children: [(0,jsx_runtime.jsxs)("p", { className: "text-2xl font-bold text-orange-600", children: [subscriptionStats?.conversionRate ?
                                            subscriptionStats.conversionRate.toFixed(1) : '0.0', "%"] }), (0,jsx_runtime.jsx)("p", { className: "text-sm text-orange-600 font-medium", children: "Conversion" }), (0,jsx_runtime.jsx)("p", { className: "text-xs text-gray-500", children: "Rata de conversie" })] })] }) })] }));
};
/* harmony default export */ const admin_RevenueCharts = (RevenueCharts);


/***/ }),

/***/ 7074:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(4976);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3658);




/**
 * Componenta Logo pentru aplicația FinanceNinja
 */
const Logo = ({ size = 'md', showText = true, className = '', to = '/', animated = false, }) => {
    const sizeClasses = {
        sm: {
            container: 'w-6 h-6',
            text: 'text-sm font-semibold',
        },
        md: {
            container: 'w-8 h-8',
            text: 'text-lg font-bold',
        },
        lg: {
            container: 'w-12 h-12',
            text: 'text-xl font-bold',
        },
        xl: {
            container: 'w-16 h-16',
            text: 'text-2xl font-bold',
        },
    };
    const LogoIcon = () => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center shadow-lg', sizeClasses[size].container, animated && 'transition-all duration-300 hover:scale-110 hover:shadow-xl'), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("svg", { viewBox: "0 0 24 24", fill: "none", className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('text-white', size === 'sm' ? 'w-3 h-3' : size === 'md' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-8 h-8'), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("path", { d: "M12 2C13.1 2 14 2.9 14 4V6H16C17.1 6 18 6.9 18 8V18C18 19.1 17.1 20 16 20H8C6.9 20 6 19.1 6 18V8C6 6.9 6.9 6 8 6H10V4C10 2.9 10.9 2 12 2ZM12 4V6H12V4ZM8 8V18H16V8H8ZM10 10H14V12H10V10ZM10 14H14V16H10V14Z", fill: "currentColor" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("circle", { cx: "12", cy: "13", r: "1.5", fill: "currentColor", opacity: "0.7" })] }) }));
    const content = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center space-x-3 group cursor-pointer', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(LogoIcon, {}), showText && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('text-gray-900 group-hover:text-primary-600 transition-colors duration-200', sizeClasses[size].text), children: "FinanceNinja" }), size === 'lg' || size === 'xl' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-xs text-gray-500 -mt-1", children: "Smart Expense Tracker" })) : null] }))] }));
    if (to) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__/* .Link */ .N_, { to: to, className: "inline-block", children: content }));
    }
    return content;
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Logo);


/***/ }),

/***/ 7530:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9582);
/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(6103);
/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(125);
/* harmony import */ var _components_ui_Dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(8724);
/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6215);
/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(9264);
/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(2552);
/* harmony import */ var _components_ui_Pagination__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(1235);
/* harmony import */ var _components_ui_Table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(5191);
/* harmony import */ var _components_ui_UserAvatar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(5649);
/* harmony import */ var _hooks_useAdminData__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(71);
/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(2082);
/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(4123);
















// Funcție locală pentru formatarea monedei
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ro-RO', {
        style: 'currency',
        currency: 'RON',
        minimumFractionDigits: 2,
    }).format(amount);
};
const UsersList = () => {
    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');
    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');
    const [planFilter, setPlanFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');
    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);
    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);
    const [showUserModal, setShowUserModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const [showConfirmModal, setShowConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const [actionType, setActionType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');
    const itemsPerPage = 10;
    // Hook-uri pentru gestionarea utilizatorilor
    const { data: usersData, isLoading, error, } = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_13__/* .useUsers */ .kp)({
        search: searchTerm,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        plan: planFilter !== 'all' ? planFilter : undefined,
        limit: itemsPerPage,
    });
    const { data: userDetails } = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_13__/* .useUserDetails */ .RB)(selectedUser?.id);
    const blockUser = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_13__/* .useBlockUser */ .o4)();
    const unblockUser = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_13__/* .useUnblockUser */ .b4)();
    const handleUserAction = (user, action) => {
        setSelectedUser(user);
        setActionType(action);
        setShowConfirmModal(true);
    };
    const confirmAction = () => {
        if (selectedUser && actionType && selectedUser?.id) {
            if (actionType === 'block') {
                blockUser.mutate({ userId: selectedUser.id, reason: '' });
            }
            else if (actionType === 'unblock') {
                unblockUser.mutate(selectedUser.id);
            }
            setShowConfirmModal(false);
            setSelectedUser(null);
        }
    };
    const getStatusBadge = (status) => {
        const statusConfig = {
            active: { variant: 'default', label: 'Activ' },
            inactive: { variant: 'secondary', label: 'Inactiv' },
            blocked: { variant: 'destructive', label: 'Blocat' },
            pending: { variant: 'outline', label: 'În așteptare' },
        };
        const config = statusConfig[status] || statusConfig.inactive;
        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, { variant: config?.variant || 'secondary', children: config?.label || 'Necunoscut' });
    };
    const getPlanBadge = (plan) => {
        const planConfig = {
            free: { variant: 'secondary', label: 'Gratuit' },
            basic: { variant: 'default', label: 'Basic' },
            premium: { variant: 'default', label: 'Premium' },
            enterprise: { variant: 'outline', label: 'Enterprise' },
        };
        const config = planConfig[plan?.toLowerCase() ?? 'free'] ?? planConfig.free;
        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, { variant: config?.variant || 'secondary', children: config?.label || 'Necunoscut' });
    };
    const columns = [
        {
            key: 'user',
            title: 'Utilizator',
            render: (user) => {
                if (!user) {
                    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-gray-500", children: "Date indisponibile" }) }));
                }
                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-shrink-0", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_UserAvatar__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .A, { user: {
                                    firstName: user.firstName,
                                    lastName: user.lastName,
                                    avatar: user.avatar,
                                    subscription: user.subscription,
                                }, size: "sm" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "ml-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm font-medium text-gray-900", children: `${user.firstName || ''} ${user.lastName || ''}` }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-gray-500", children: user.email })] })] }));
            },
        },
        {
            key: 'plan',
            title: 'Plan',
            render: (user) => {
                if (!user)
                    return getPlanBadge('N/A');
                return getPlanBadge(user.subscription?.plan?.name ?? 'N/A');
            },
        },
        {
            key: 'status',
            title: 'Status',
            render: (user) => {
                if (!user)
                    return getStatusBadge('inactive');
                const status = user.status || 'active';
                return getStatusBadge(status);
            },
        },
        {
            key: 'revenue',
            title: 'Venituri',
            render: (user) => {
                if (!user) {
                    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "font-medium text-gray-900", children: "-" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-gray-500", children: "-" })] }));
                }
                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "font-medium text-gray-900", children: formatCurrency(user.totalRevenue ?? 0) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-gray-500", children: user.subscription?.plan?.price
                                ? `${formatCurrency(user.subscription.plan.price)}/lună`
                                : 'Gratuit' })] }));
            },
        },
        {
            key: 'joinDate',
            title: 'Data înregistrării',
            render: (user) => {
                if (!user)
                    return '-';
                return user.createdAt ? (0,date_fns__WEBPACK_IMPORTED_MODULE_14__/* .format */ .GPZ)(new Date(user.createdAt), 'dd/MM/yyyy') : '-';
            },
        },
        {
            key: 'lastActive',
            title: 'Ultima activitate',
            render: (user) => {
                if (!user)
                    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-400", children: "-" });
                return user.lastActiveAt ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-600", children: (0,date_fns__WEBPACK_IMPORTED_MODULE_14__/* .formatDistanceToNow */ .mor)(new Date(user.lastActiveAt), { addSuffix: true, locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_15__.ro }) })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-400", children: "Niciodat\u0103" }));
            },
        },
        {
            key: 'actions',
            title: 'Acțiuni',
            render: (user) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Dropdown__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { trigger: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "ghost", size: "sm", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .EllipsisVerticalIcon */ .Lz$, { className: "h-4 w-4" }) }), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "ghost", size: "sm", onClick: () => {
                            setSelectedUser(user);
                            setShowUserModal(true);
                        }, className: "w-full justify-start", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UserIcon */ .nys, { className: "h-4 w-4 mr-2" }), "Vezi detalii"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "ghost", size: "sm", onClick: () => handleUserAction(user, (user.status || 'active') === 'blocked' ? 'unblock' : 'block'), className: `w-full justify-start ${(user.status || 'active') === 'blocked' ? 'text-green-600' : 'text-red-600'}`, children: [(user.status || 'active') === 'blocked' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CheckCircleIcon */ .C1y, { className: "h-4 w-4 mr-2" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .NoSymbolIcon */ .tZr, { className: "h-4 w-4 mr-2" })), (user.status || 'active') === 'blocked' ? 'Deblochează' : 'Blochează'] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "ghost", size: "sm", onClick: () => handleUserAction(user, 'manage-subscription'), className: "w-full justify-start", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CreditCardIcon */ .BFk, { className: "h-4 w-4 mr-2" }), "Gestioneaz\u0103 abonament"] })] })),
        },
    ];
    if (isLoading) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center justify-center h-32", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay, { size: "lg" }) }) }));
    }
    if (error) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center text-red-600", children: "Eroare la \u00EEnc\u0103rcarea utilizatorilor" }) }));
    }
    const users = usersData?.pages?.flatMap(page => page.data) || [];
    const totalPages = usersData?.pages?.[0]?.pagination?.totalPages || 1;
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900", children: "Gestionarea utilizatorilor" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-sm text-gray-500", children: [usersData?.pages?.[0]?.pagination?.total || 0, " utilizatori"] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col sm:flex-row gap-4 mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, { type: "text", placeholder: "Caut\u0103 dup\u0103 nume sau email...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), leftIcon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .MagnifyingGlassIcon */ .$p$, { className: "h-4 w-4" }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex gap-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select", { value: statusFilter, onChange: (e) => setStatusFilter(e.target.value), className: "px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "all", children: "Toate statusurile" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "active", children: "Activ" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "inactive", children: "Inactiv" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "blocked", children: "Blocat" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select", { value: planFilter, onChange: (e) => setPlanFilter(e.target.value), className: "px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "all", children: "Toate planurile" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "free", children: "Gratuit" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "basic", children: "Basic" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "premium", children: "Premium" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "enterprise", children: "Enterprise" })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Table__WEBPACK_IMPORTED_MODULE_11__/* .DataTable */ .bQ, { columns: columns, data: users, emptyMessage: "Nu au fost g\u0103si\u021Bi utilizatori" }), totalPages > 1 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Pagination__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay, { currentPage: currentPage, totalPages: totalPages, onPageChange: setCurrentPage }) }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { isOpen: showUserModal, onClose: () => setShowUserModal(false), title: "Detalii utilizator", size: "lg", children: selectedUser && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Nume complet" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-900", children: `${selectedUser?.firstName || ''} ${selectedUser?.lastName || ''}` || 'N/A' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Email" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-900", children: selectedUser?.email })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Status" }), getStatusBadge(selectedUser?.status || 'active')] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Plan curent" }), getPlanBadge(selectedUser?.subscription?.plan?.name)] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Data \u00EEnregistr\u0103rii" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-900", children: selectedUser?.createdAt
                                                ? new Date(selectedUser.createdAt).toLocaleDateString('ro-RO')
                                                : 'N/A' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Ultima activitate" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-900", children: selectedUser?.lastActiveAt
                                                ? new Date(selectedUser.lastActiveAt).toLocaleDateString('ro-RO')
                                                : 'Niciodată' })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "border-t pt-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900 mb-2", children: "Informa\u021Bii suplimentare" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 gap-4 mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Moned\u0103 preferat\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-900", children: selectedUser?.currency || 'USD' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Fus orar" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-900", children: selectedUser?.timezone || 'UTC' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Email verificat" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-900", children: selectedUser?.email_verified ? 'Da' : 'Nu' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Limit\u0103 cheltuieli lunare" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-sm text-gray-900", children: [selectedUser?.monthly_expense_count || 0, " /", ' ', selectedUser?.monthly_expense_limit || 50] })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "border-t pt-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900 mb-2", children: "Statistici" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-4 gap-4 text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-gray-900", children: selectedUser?.totalExpenses || 0 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500", children: "Cheltuieli" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-gray-900", children: formatCurrency(selectedUser?.totalExpenseAmount || 0) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500", children: "Suma cheltuieli" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-gray-900", children: formatCurrency(selectedUser?.totalRevenue || 0) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500", children: "Venituri generate" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-gray-900", children: selectedUser?.loginCount || 0 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500", children: "Autentific\u0103ri" })] })] })] }), selectedUser?.subscription && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "border-t pt-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900 mb-2", children: "Detalii abonament" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Status abonament" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-900", children: selectedUser?.subscription.status || 'N/A' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Perioada curent\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-900", children: selectedUser?.subscription.currentPeriodStart &&
                                                        selectedUser?.subscription.currentPeriodEnd
                                                        ? `${new Date(selectedUser.subscription.currentPeriodStart).toLocaleDateString('ro-RO')} - ${new Date(selectedUser.subscription.currentPeriodEnd).toLocaleDateString('ro-RO')}`
                                                        : 'N/A' })] }), selectedUser?.subscription.currentPeriodStart &&
                                            selectedUser?.subscription.currentPeriodEnd && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Perioada de facturare" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-900", children: `${new Date(selectedUser?.subscription.currentPeriodStart).toLocaleDateString('ro-RO')} - ${new Date(selectedUser?.subscription.currentPeriodEnd).toLocaleDateString('ro-RO')}` })] }))] })] }))] })) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { isOpen: showConfirmModal, onClose: () => setShowConfirmModal(false), title: "Confirm\u0103 ac\u021Biunea", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ExclamationTriangleIcon */ .Pip, { className: "h-6 w-6 text-yellow-600" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-sm text-gray-700", children: ["E\u0219ti sigur c\u0103 vrei s\u0103", ' ', actionType === 'block'
                                            ? 'blochezi'
                                            : actionType === 'unblock'
                                                ? 'deblochezi'
                                                : 'modifici', ' ', "acest utilizator?"] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-end space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "secondary", onClick: () => setShowConfirmModal(false), children: "Anuleaz\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: actionType === 'block' ? 'danger' : 'primary', onClick: confirmAction, disabled: blockUser.isPending || unblockUser.isPending, children: blockUser.isPending || unblockUser.isPending ? 'Se procesează...' : 'Confirmă' })] })] }) })] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UsersList);


/***/ }),

/***/ 8598:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9582);
/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(125);
/* harmony import */ var _hooks_useAdminData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(71);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(3658);







const AdminStats = ({ dashboardStats }) => {
    const { data: subscriptionStats } = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_5__/* .useSubscriptionStats */ .cV)();
    const { data: planStats } = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_5__/* .usePlanStats */ .gm)();
    const { data: usageStats } = (0,_hooks_useAdminData__WEBPACK_IMPORTED_MODULE_5__/* .useUsageStats */ .aZ)();
    // Calculează metrici derivate
    const totalUsers = subscriptionStats?.totalUsers || 0;
    const activeUsers = subscriptionStats?.activeUsers || 0;
    const premiumUsers = subscriptionStats?.premiumUsers || 0;
    const freeUsers = totalUsers - premiumUsers;
    const monthlyRevenue = subscriptionStats?.monthlyRevenue || 0;
    const yearlyRevenue = subscriptionStats?.yearlyRevenue || 0;
    const averageRevenuePerUser = premiumUsers > 0 ? monthlyRevenue / premiumUsers : 0;
    const conversionRate = totalUsers > 0 ? (premiumUsers / totalUsers) * 100 : 0;
    const churnRate = subscriptionStats?.churnRate || 0;
    const totalActions = usageStats?.totalActions || 0;
    const dailyActiveUsers = usageStats?.dailyActiveUsers || 0;
    // Calculează tendințele (mock data pentru demonstrație)
    const userGrowth = subscriptionStats?.userGrowth || 5.2;
    const revenueGrowth = subscriptionStats?.revenueGrowth || 12.8;
    const usageGrowth = usageStats?.usageGrowth || 8.4;
    const StatCard = ({ title, value, icon: Icon, trend, trendValue, color = 'blue', subtitle }) => {
        const colorClasses = {
            blue: 'text-blue-600',
            green: 'text-green-600',
            purple: 'text-purple-600',
            orange: 'text-orange-600',
            red: 'text-red-600',
            indigo: 'text-indigo-600',
        };
        const TrendIcon = trend === 'up' ? _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowTrendingUpIcon */ .LOZ : _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowTrendingDownIcon */ .upo;
        const trendColor = trend === 'up' ? 'text-green-600' : 'text-red-600';
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-shrink-0", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Icon, { className: `h-8 w-8 ${colorClasses[color]}` }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "ml-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-gray-600", children: title }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-2xl font-bold text-gray-900", children: value }), subtitle && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500 mt-1", children: subtitle }))] })] }) }), trendValue && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TrendIcon, { className: `h-4 w-4 ${trendColor} mr-1` }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: `text-sm font-medium ${trendColor}`, children: [Math.abs(trendValue), "%"] })] }))] }) }));
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatCard, { title: "Total utilizatori", value: totalUsers, subtitle: `${activeUsers} activi astăzi`, icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UsersIcon */ .c2u, trend: userGrowth > 0 ? 'up' : 'down', trendValue: userGrowth, color: "blue" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatCard, { title: "Venituri lunare", value: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_6__/* .formatCurrency */ .vv)(monthlyRevenue), subtitle: `ARPU: ${(0,_utils_helpers__WEBPACK_IMPORTED_MODULE_6__/* .formatCurrency */ .vv)(averageRevenuePerUser)}`, icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .BanknotesIcon */ .t_p, trend: revenueGrowth > 0 ? 'up' : 'down', trendValue: revenueGrowth, color: "green" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatCard, { title: "Utilizatori Premium", value: premiumUsers, subtitle: `${conversionRate.toFixed(1)}% conversion rate`, icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CreditCardIcon */ .BFk, trend: conversionRate > 0 ? 'up' : 'down', trendValue: conversionRate, color: "purple" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatCard, { title: "Ac\u021Biuni zilnice", value: totalActions, subtitle: `${dailyActiveUsers} utilizatori activi`, icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChartBarIcon */ .r95, trend: usageGrowth > 0 ? 'up' : 'down', trendValue: usageGrowth, color: "indigo" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { className: "p-6 md:col-span-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: "Distribu\u021Bia planurilor" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-3 h-3 bg-gray-400 rounded-full mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium text-gray-700", children: "Gratuit" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-600", children: freeUsers }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, { variant: "secondary", children: [totalUsers > 0 ? ((freeUsers / totalUsers) * 100).toFixed(1) : 0, "%"] })] })] }), planStats?.plans?.map((plan, index) => {
                                const planUsers = plan.userCount || 0;
                                const planPercentage = totalUsers > 0 ? (planUsers / totalUsers) * 100 : 0;
                                const colors = ['bg-blue-500', 'bg-purple-500', 'bg-green-500'];
                                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: `w-3 h-3 ${colors[index % colors.length]} rounded-full mr-3` }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium text-gray-700", children: plan.name })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-600", children: planUsers }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, { variant: "primary", children: [planPercentage.toFixed(1), "%"] })] })] }, plan.id));
                            }) || ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-3 h-3 bg-purple-500 rounded-full mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium text-gray-700", children: "Premium" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-600", children: premiumUsers }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay, { variant: "primary", children: [conversionRate.toFixed(1), "%"] })] })] }))] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { className: "p-6 md:col-span-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: "Metrici cheie" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-2xl font-bold text-gray-900", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_6__/* .formatCurrency */ .vv)(yearlyRevenue) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-600", children: "ARR (Annual Recurring Revenue)" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-2xl font-bold text-gray-900", children: [churnRate.toFixed(1), "%"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-600", children: "Churn Rate" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-2xl font-bold text-gray-900", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_6__/* .formatCurrency */ .vv)(averageRevenuePerUser * 12) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-600", children: "LTV (Customer Lifetime Value)" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-2xl font-bold text-gray-900", children: [dailyActiveUsers > 0 ? ((dailyActiveUsers / totalUsers) * 100).toFixed(1) : 0, "%"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-600", children: "Daily Active Users" })] })] })] })] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AdminStats);


/***/ }),

/***/ 8724:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   l6: () => (/* binding */ Select)
/* harmony export */ });
/* unused harmony exports DropdownItem, DropdownSeparator, DropdownHeader, ActionMenu */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3658);
/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9264);





/**
 * Hook pentru detectarea click-urilor în afara componentei
 */
const useClickOutside = (ref, callback) => {
    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {
        const handleClick = (event) => {
            if (ref.current && !ref.current.contains(event.target)) {
                callback();
            }
        };
        document.addEventListener('mousedown', handleClick);
        return () => document.removeEventListener('mousedown', handleClick);
    }, [ref, callback]);
};
/**
 * Hook pentru gestionarea navigării cu tastatura
 */
const useKeyboardNavigation = (isOpen, items, onSelect, onClose) => {
    const [focusedIndex, setFocusedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(-1);
    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {
        if (!isOpen) {
            setFocusedIndex(-1);
            return;
        }
        const handleKeyDown = (e) => {
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    setFocusedIndex(prev => (prev < items.length - 1 ? prev + 1 : 0));
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    setFocusedIndex(prev => (prev > 0 ? prev - 1 : items.length - 1));
                    break;
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    if (focusedIndex >= 0 && items[focusedIndex]) {
                        onSelect(items[focusedIndex]);
                    }
                    break;
                case 'Escape':
                    e.preventDefault();
                    onClose();
                    break;
            }
        };
        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [isOpen, items, focusedIndex, onSelect, onClose]);
    return focusedIndex;
};
/**
 * Componenta Dropdown de bază
 */
const Dropdown = ({ trigger, children, isOpen: controlledIsOpen, onToggle, position = 'bottom-left', offset = 8, className = '', menuClassName = '', disabled = false, closeOnSelect = true, ...rest }) => {
    const [internalIsOpen, setInternalIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
    const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;
    const handleToggle = () => {
        if (disabled)
            return;
        if (onToggle) {
            onToggle(!isOpen);
        }
        else {
            setInternalIsOpen(!isOpen);
        }
    };
    const handleClose = () => {
        if (onToggle) {
            onToggle(false);
        }
        else {
            setInternalIsOpen(false);
        }
    };
    useClickOutside(dropdownRef, handleClose);
    const positionClasses = {
        'top-left': 'bottom-full left-0 mb-2',
        'top-right': 'bottom-full right-0 mb-2',
        'bottom-left': 'top-full left-0 mt-2',
        'bottom-right': 'top-full right-0 mt-2',
        left: 'right-full top-0 mr-2',
        right: 'left-full top-0 ml-2',
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { ref: dropdownRef, className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('relative inline-block', className), ...rest, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { onClick: handleToggle, children: trigger }), isOpen && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('absolute z-50 min-w-full', 'bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5', 'transform transition-all duration-200 ease-out', 'animate-scale-in origin-top', positionClasses[position], menuClassName), style: { marginTop: position.includes('bottom') ? offset : undefined }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "py-1", children: react__WEBPACK_IMPORTED_MODULE_2___default().Children.map(children, child => {
                        if (react__WEBPACK_IMPORTED_MODULE_2___default().isValidElement(child)) {
                            return react__WEBPACK_IMPORTED_MODULE_2___default().cloneElement(child, {
                                onSelect: closeOnSelect
                                    ? () => {
                                        child.props.onSelect?.();
                                        handleClose();
                                    }
                                    : child.props.onSelect,
                            });
                        }
                        return child;
                    }) }) }))] }));
};
/**
 * Item pentru dropdown
 */
const DropdownItem = ({ children, onClick, onSelect, disabled = false, active = false, className = '', icon, shortcut, ...rest }) => {
    const handleClick = (e) => {
        if (disabled)
            return;
        onClick?.(e);
        onSelect?.();
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { type: "button", className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('w-full text-left px-4 py-2 text-sm', 'flex items-center justify-between', 'transition-colors duration-150', disabled
            ? 'text-gray-400 cursor-not-allowed'
            : [
                'text-gray-700 hover:bg-gray-100 hover:text-gray-900',
                'focus:bg-gray-100 focus:text-gray-900 focus:outline-none',
            ], active && 'bg-primary-50 text-primary-700', className), onClick: handleClick, disabled: disabled, ...rest, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-3 min-w-0 flex-1", children: [icon && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "flex-shrink-0", children: icon }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "truncate", children: children })] }), shortcut && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-xs text-gray-400 ml-2", children: shortcut }))] }));
};
/**
 * Separator pentru dropdown
 */
const DropdownSeparator = ({ className = '' }) => {
    return _jsx("div", { className: cn('border-t border-gray-100 my-1', className) });
};
/**
 * Header pentru dropdown
 */
const DropdownHeader = ({ children, className = '', ...rest }) => {
    return (_jsx("div", { className: cn('px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider', className), ...rest, children: children }));
};
/**
 * Select dropdown personalizat
 */
const Select = ({ value, onChange, options = [], placeholder = 'Selectează o opțiune', disabled = false, loading = false, error = false, searchable = false, clearable = false, multiple = false, className = '', buttonClassName = '', optionClassName = '', menuClassName = '', renderOption, renderValue, getOptionLabel = option => option?.label || option, getOptionValue = option => option?.value || option, offset = 8, ...rest }) => {
    // Nu transmitem proprietățile rest la button pentru a evita conflictele de tip
    // Acestea sunt destinate pentru containerul Dropdown
    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');
    const searchInputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
    const selectedOptions = multiple ? (Array.isArray(value) ? value : []) : value ? [value] : [];
    const filteredOptions = searchable && searchTerm
        ? options.filter(option => getOptionLabel(option).toLowerCase().includes(searchTerm.toLowerCase()))
        : options;
    const focusedIndex = useKeyboardNavigation(isOpen, filteredOptions, handleSelect, () => setIsOpen(false));
    function handleSelect(option) {
        if (multiple) {
            const optionValue = getOptionValue(option);
            const isSelected = selectedOptions.some(selected => getOptionValue(selected) === optionValue);
            let newValue;
            if (isSelected) {
                newValue = selectedOptions.filter(selected => getOptionValue(selected) !== optionValue);
            }
            else {
                newValue = [...selectedOptions, option];
            }
            onChange?.(newValue);
        }
        else {
            onChange?.(option);
            setIsOpen(false);
        }
        setSearchTerm('');
    }
    const handleClear = (e) => {
        e.stopPropagation();
        onChange?.(multiple ? [] : null);
    };
    const handleToggle = () => {
        if (disabled || loading)
            return;
        setIsOpen(!isOpen);
        if (!isOpen && searchable) {
            setTimeout(() => {
                searchInputRef.current?.focus();
            }, 100);
        }
    };
    const displayValue = () => {
        if (renderValue) {
            return renderValue(value);
        }
        if (multiple) {
            if (selectedOptions.length === 0) {
                return placeholder;
            }
            if (selectedOptions.length === 1) {
                return getOptionLabel(selectedOptions[0]);
            }
            return `${selectedOptions.length} opțiuni selectate`;
        }
        return value ? getOptionLabel(value) : placeholder;
    };
    const isSelected = (option) => {
        const optionValue = getOptionValue(option);
        return selectedOptions.some(selected => getOptionValue(selected) === optionValue);
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Dropdown, { isOpen: isOpen, onToggle: setIsOpen, className: className, menuClassName: menuClassName, offset: offset, trigger: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { type: "button", className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('relative w-full bg-white border rounded-md shadow-sm pl-3 pr-10 py-2 text-left', 'focus:outline-none focus:ring-1 focus:border-primary-500', disabled
                ? 'bg-gray-50 text-gray-500 cursor-not-allowed border-gray-200'
                : 'cursor-pointer border-gray-300 hover:border-gray-400', error && 'border-red-300 focus:border-red-500 focus:ring-red-500', buttonClassName), disabled: disabled || loading, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('block truncate', !value && 'text-gray-500'), children: displayValue() }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none", children: loading ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { size: "sm" })) : clearable && value && !disabled ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { type: "button", className: "p-1 hover:bg-gray-100 rounded pointer-events-auto", onClick: handleClear, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("svg", { className: "w-4 h-4 text-gray-400", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" }) }) })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronDownIcon */ .D3D, { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('w-5 h-5 text-gray-400 transition-transform duration-200', isOpen && 'transform rotate-180') })) })] }), closeOnSelect: !multiple, children: [searchable && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "p-2 border-b border-gray-100", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { ref: searchInputRef, type: "text", className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500", placeholder: "Caut\u0103...", value: searchTerm, onChange: e => setSearchTerm(e.target.value), onClick: e => e.stopPropagation() }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "max-h-60 overflow-auto", children: filteredOptions.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "px-4 py-2 text-sm text-gray-500", children: searchTerm ? 'Nu s-au găsit rezultate' : 'Nu există opțiuni' })) : (filteredOptions.map((option, index) => {
                    const selected = isSelected(option);
                    const focused = index === focusedIndex;
                    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DropdownItem, { onClick: () => handleSelect(option), active: focused, className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)(selected && 'bg-primary-50 text-primary-700', optionClassName), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between w-full", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: renderOption ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: renderOption(option) })) : getOptionLabel(option) }), selected && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CheckIcon */ .Srz, { className: "w-4 h-4 text-primary-600" })] }) }, getOptionValue(option)));
                })) })] }));
};
/**
 * Menu dropdown pentru acțiuni
 */
const ActionMenu = ({ trigger, actions = [], position = 'bottom-right', className = '', ...rest }) => {
    return (_jsx(Dropdown, { trigger: trigger, position: position, className: className, ...rest, children: actions.map((action, index) => {
            if (action.type === 'separator') {
                return _jsx(DropdownSeparator, {}, index);
            }
            if (action.type === 'header') {
                return _jsx(DropdownHeader, { children: action.label }, index);
            }
            const itemProps = {
                key: index,
                disabled: action.disabled,
                icon: action.icon,
                shortcut: action.shortcut,
            };
            if (action.onClick) {
                itemProps.onClick = (e) => action.onClick();
            }
            return _jsx(DropdownItem, { ...itemProps, children: action.label });
        }) }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dropdown);


/***/ }),

/***/ 9582:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony exports StatusBadge, DotBadge, NotificationBadge, PriorityBadge, CategoryBadge, ProgressBadge, BadgeGroup, TooltipBadge */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3658);




/**
 * Componenta Badge de bază
 */
const Badge = ({ children, variant = 'primary', size = 'md', rounded = false, outline = false, removable = false, onRemove, icon, rightIcon, className = '', ...rest }) => {
    const baseClasses = [
        'inline-flex items-center font-medium',
        'transition-colors duration-200',
    ];
    const sizeClasses = {
        xs: 'px-2 py-0.5 text-xs gap-1',
        sm: 'px-2.5 py-0.5 text-xs gap-1',
        md: 'px-3 py-1 text-sm gap-1.5',
        lg: 'px-4 py-1.5 text-base gap-2',
    };
    const roundedClasses = {
        xs: rounded ? 'rounded-full' : 'rounded',
        sm: rounded ? 'rounded-full' : 'rounded',
        md: rounded ? 'rounded-full' : 'rounded-md',
        lg: rounded ? 'rounded-full' : 'rounded-lg',
    };
    const variantClasses = {
        primary: outline
            ? 'text-primary-700 bg-primary-50 ring-1 ring-inset ring-primary-600/20'
            : 'text-primary-50 bg-primary-600',
        secondary: outline
            ? 'text-gray-700 bg-gray-50 ring-1 ring-inset ring-gray-600/20'
            : 'text-gray-50 bg-gray-600',
        success: outline
            ? 'text-green-700 bg-green-50 ring-1 ring-inset ring-green-600/20'
            : 'text-green-50 bg-green-600',
        warning: outline
            ? 'text-yellow-700 bg-yellow-50 ring-1 ring-inset ring-yellow-600/20'
            : 'text-yellow-50 bg-yellow-600',
        danger: outline
            ? 'text-red-700 bg-red-50 ring-1 ring-inset ring-red-600/20'
            : 'text-red-50 bg-red-600',
        info: outline
            ? 'text-blue-700 bg-blue-50 ring-1 ring-inset ring-blue-600/20'
            : 'text-blue-50 bg-blue-600',
    };
    const iconSizes = {
        xs: 'w-3 h-3',
        sm: 'w-3 h-3',
        md: 'w-4 h-4',
        lg: 'w-5 h-5',
    };
    const handleRemove = (e) => {
        e.stopPropagation();
        onRemove?.();
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)(baseClasses, sizeClasses[size], roundedClasses[size], variantClasses[variant], className), ...rest, children: [icon && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('flex-shrink-0', iconSizes[size]), children: icon })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "truncate", children: children }), rightIcon && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('flex-shrink-0', iconSizes[size]), children: rightIcon })), removable && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { type: "button", className: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.cn)('flex-shrink-0 ml-1 rounded-full p-0.5', 'hover:bg-black hover:bg-opacity-10', 'focus:outline-none focus:bg-black focus:bg-opacity-10', iconSizes[size]), onClick: handleRemove, "aria-label": "\u0218terge", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .XMarkIcon */ .fKY, { className: "w-full h-full" }) }))] }));
};
/**
 * Badge pentru status
 */
const StatusBadge = ({ status, children, className = '', ...rest }) => {
    const statusConfig = {
        active: { variant: 'success', children: children?.toString() || 'Activ' },
        inactive: { variant: 'secondary', children: children?.toString() || 'Inactiv' },
        pending: { variant: 'warning', children: children?.toString() || 'În așteptare' },
        completed: { variant: 'success', children: children?.toString() || 'Finalizat' },
        cancelled: { variant: 'danger', children: children?.toString() || 'Anulat' },
        draft: { variant: 'secondary', children: children?.toString() || 'Ciornă' },
        published: { variant: 'primary', children: children?.toString() || 'Publicat' },
        archived: { variant: 'secondary', children: children?.toString() || 'Arhivat' },
    };
    const config = statusConfig[status] || { variant: 'secondary', children: status };
    return (_jsx(Badge, { variant: config.variant, className: className, ...rest, children: config.children }));
};
/**
 * Badge cu punct indicator
 */
const DotBadge = ({ children, variant = 'primary', size = 'md', className = '', ...rest }) => {
    const dotSizes = {
        xs: 'w-1.5 h-1.5',
        sm: 'w-2 h-2',
        md: 'w-2 h-2',
        lg: 'w-2.5 h-2.5',
    };
    const dotColors = {
        primary: 'bg-primary-600',
        secondary: 'bg-gray-600',
        success: 'bg-green-600',
        warning: 'bg-yellow-600',
        danger: 'bg-red-600',
        info: 'bg-blue-600',
    };
    return (_jsx(Badge, { variant: variant, size: size, outline: true, className: className, icon: _jsx("span", { className: cn('rounded-full', dotSizes[size], dotColors[variant]) }), ...rest, children: children }));
};
/**
 * Badge numeric pentru notificări
 */
const NotificationBadge = ({ count = 0, max = 99, showZero = false, variant = 'danger', size = 'sm', className = '', ...rest }) => {
    if (count === 0 && !showZero) {
        return null;
    }
    const displayCount = count > max ? `${max}+` : count.toString();
    return (_jsx(Badge, { variant: variant, size: size, rounded: true, className: cn('min-w-0 justify-center', size === 'xs' && 'min-w-[1rem] h-4', size === 'sm' && 'min-w-[1.25rem] h-5', size === 'md' && 'min-w-[1.5rem] h-6', size === 'lg' && 'min-w-[2rem] h-8', className), ...rest, children: displayCount }));
};
/**
 * Badge pentru priorități
 */
const PriorityBadge = ({ priority, children, className = '', ...rest }) => {
    const priorityConfig = {
        low: { variant: 'success', children: children?.toString() || 'Scăzută' },
        medium: { variant: 'warning', children: children?.toString() || 'Medie' },
        high: { variant: 'danger', children: children?.toString() || 'Ridicată' },
        urgent: { variant: 'danger', children: children?.toString() || 'Urgentă' },
    };
    const config = priorityConfig[priority] || { variant: 'secondary', children: priority };
    return (_jsx(Badge, { variant: config.variant, size: "sm", className: className, ...rest, children: config.children }));
};
/**
 * Badge pentru categorii
 */
const CategoryBadge = ({ category, color, children, className = '', ...rest }) => {
    const customStyle = color ? {
        backgroundColor: `${color}20`,
        color,
        borderColor: `${color}40`,
    } : {};
    return (_jsx(Badge, { variant: "secondary", outline: true, className: cn('border', className), style: customStyle, ...rest, children: children || category }));
};
/**
 * Badge pentru progres
 */
const ProgressBadge = ({ progress = 0, total = 100, showPercentage = true, variant = 'primary', className = '', ...rest }) => {
    const percentage = Math.round((progress / total) * 100);
    let badgeVariant = variant === 'auto' ? 'primary' : variant;
    if (variant === 'auto') {
        if (percentage >= 100)
            badgeVariant = 'success';
        else if (percentage >= 75)
            badgeVariant = 'primary';
        else if (percentage >= 50)
            badgeVariant = 'warning';
        else
            badgeVariant = 'danger';
    }
    return (_jsx(Badge, { variant: badgeVariant, outline: true, className: className, ...rest, children: showPercentage ? `${percentage}%` : `${progress}/${total}` }));
};
/**
 * Grup de badge-uri
 */
const BadgeGroup = ({ children, className = '', spacing = 'normal', wrap = true, ...rest }) => {
    const spacingClasses = {
        tight: 'gap-1',
        normal: 'gap-2',
        loose: 'gap-3',
    };
    return (_jsx("div", { className: cn('flex items-center', spacingClasses[spacing], wrap && 'flex-wrap', className), ...rest, children: children }));
};
/**
 * Badge cu tooltip
 */
const TooltipBadge = ({ children, tooltip, className = '', ...rest }) => {
    return (_jsxs("div", { className: "relative group", children: [_jsx(Badge, { className: className, ...rest, children: children }), tooltip && (_jsxs("div", { className: "absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10", children: [tooltip, _jsx("div", { className: "absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900" })] }))] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Badge);


/***/ })

}]);