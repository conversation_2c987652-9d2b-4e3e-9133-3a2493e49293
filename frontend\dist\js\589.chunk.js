"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[589],{

/***/ 5589:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(893);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9785);
/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(888);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(4976);
/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(4862);
/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(6103);
/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(6215);










// Schema de validare pentru formularul de reset password
const resetPasswordSchema = zod__WEBPACK_IMPORTED_MODULE_7__.z.object({
    password: zod__WEBPACK_IMPORTED_MODULE_7__.z.string()
        .min(1, 'Parola este obligatorie')
        .min(8, 'Parola trebuie să aibă cel puțin 8 caractere')
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Parola trebuie să conțină cel puțin o literă mică, o literă mare și o cifră'),
    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_7__.z.string()
        .min(1, 'Confirmarea parolei este obligatorie'),
}).refine((data) => data.password === data.confirmPassword, {
    message: 'Parolele nu se potrivesc',
    path: ['confirmPassword'],
});
const ResetPassword = () => {
    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);
    const [searchParams] = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .useSearchParams */ .ok)();
    const navigate = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .useNavigate */ .Zp)();
    const token = searchParams.get('token');
    const { register, handleSubmit, formState: { errors }, } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_4__/* .useForm */ .mN)({
        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__/* .zodResolver */ .u)(resetPasswordSchema),
    });
    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(() => {
        // Verifică dacă token-ul este prezent
        if (!token) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.error('Token invalid sau lipsă');
            navigate('/forgot-password');
        }
    }, [token, navigate]);
    const onSubmit = async (data) => {
        setIsLoading(true);
        try {
            // Aici ar trebui să faci request către API pentru resetarea parolei
            // await authService.resetPassword(token, data.password);
            // Simulare request
            await new Promise(resolve => setTimeout(resolve, 2000));
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.success('Parola a fost resetată cu succes!');
            navigate('/login');
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Eroare la resetarea parolei';
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.error(errorMessage);
        }
        finally {
            setIsLoading(false);
        }
    };
    if (!token) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-md w-full space-y-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("svg", { className: "h-6 w-6 text-red-600", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "mt-6 text-center text-3xl font-extrabold text-gray-900", children: "Link invalid" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "mt-2 text-center text-sm text-gray-600", children: "Link-ul de resetare a parolei este invalid sau a expirat." })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .Link */ .N_, { to: "/forgot-password", className: "group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500", children: "Solicit\u0103 un nou link" }) })] }) }));
    }
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-md w-full space-y-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "mt-6 text-center text-3xl font-extrabold text-gray-900", children: "Seteaz\u0103 parola nou\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "mt-2 text-center text-sm text-gray-600", children: "Introdu noua parol\u0103 pentru contul t\u0103u." })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("form", { className: "mt-8 space-y-6", onSubmit: handleSubmit(onSubmit), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { ...register('password'), type: "password", label: "Parola nou\u0103", placeholder: "Introdu parola nou\u0103", error: errors.password?.message, autoComplete: "new-password" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { ...register('confirmPassword'), type: "password", label: "Confirm\u0103 parola nou\u0103", placeholder: "Confirm\u0103 parola nou\u0103", error: errors.confirmPassword?.message, autoComplete: "new-password" }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay, { type: "submit", variant: "primary", size: "lg", className: "w-full", loading: isLoading, disabled: isLoading, children: isLoading ? 'Se resetează...' : 'Resetează parola' }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .Link */ .N_, { to: "/login", className: "font-medium text-primary-600 hover:text-primary-500 flex items-center justify-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowLeftIcon */ .A60, { className: "h-4 w-4 mr-1" }), "\u00CEnapoi la conectare"] }) })] })] }) }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResetPassword);


/***/ })

}]);