"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[671],{

/***/ 2671:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8035);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(6103);
/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(125);
/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(9264);
/* harmony import */ var _hooks_useExpenses__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(2948);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(3658);









// Mock data pentru demonstrație
const mockExpenses = [
    {
        id: 'exp_1',
        description: 'Cumpărături Carrefour',
        amount: 85.5,
        category: 'Mâncare',
        date: '2024-01-15',
        paymentMethod: 'Card',
    },
    {
        id: 'exp_2',
        description: 'Benzină',
        amount: 120.0,
        category: 'Transport',
        date: '2024-01-14',
        paymentMethod: 'Card',
    },
    {
        id: 'exp_3',
        description: 'Factură electricitate',
        amount: 75.25,
        category: 'Utilități',
        date: '2024-01-13',
        paymentMethod: 'Transfer bancar',
    },
    {
        id: 'exp_4',
        description: 'Cinema',
        amount: 25.0,
        category: 'Divertisment',
        date: '2024-01-12',
        paymentMethod: 'Numerar',
    },
    {
        id: 'exp_5',
        description: 'Prânz restaurant',
        amount: 45.75,
        category: 'Mâncare',
        date: '2024-01-11',
        paymentMethod: 'Card',
    },
];
const Expenses = () => {
    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');
    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');
    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);
    // Hook pentru export
    const exportExpensesMutation = (0,_hooks_useExpenses__WEBPACK_IMPORTED_MODULE_7__/* .useExportExpenses */ .Rj)();
    // Simulare query pentru cheltuieli
    const { data: expenses, isLoading, error, } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__/* .useQuery */ .pw)({
        queryKey: ['expenses', searchTerm, selectedCategory],
        queryFn: async () => {
            // Simulare API call
            await new Promise(resolve => setTimeout(resolve, 500));
            let filteredExpenses = mockExpenses;
            if (searchTerm) {
                filteredExpenses = filteredExpenses.filter(expense => expense.description.toLowerCase().includes(searchTerm.toLowerCase()));
            }
            if (selectedCategory) {
                filteredExpenses = filteredExpenses.filter(expense => expense.category === selectedCategory);
            }
            return filteredExpenses;
        },
    });
    const categories = ['Mâncare', 'Transport', 'Utilități', 'Divertisment', 'Sănătate', 'Educație'];
    const handleAddExpense = () => {
        // Aici ar trebui să deschizi un modal sau să navighezi către o pagină de adăugare
        console.log('Adaugă cheltuială nouă');
    };
    const handleExport = async (format) => {
        try {
            // Pregătește parametrii pentru export bazați pe filtrele curente
            const exportParams = {};
            if (selectedCategory !== '') {
                exportParams.categoryId = selectedCategory;
            }
            // Apelează funcția de export
            await exportExpensesMutation.mutateAsync({ format, params: exportParams });
        }
        catch (exportError) {
            // Eroarea este gestionată în hook
            console.error('Export failed:', exportError);
        }
    };
    if (error) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center py-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-red-600", children: "Eroare la \u00EEnc\u0103rcarea cheltuielilor" }) }));
    }
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col sm:flex-row sm:items-center sm:justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1", { className: "text-2xl font-bold text-gray-900", children: "Cheltuieli" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: "Gestioneaz\u0103 \u0219i urm\u0103re\u0219te toate cheltuielile tale" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex gap-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", size: "sm", onClick: () => handleExport('csv'), disabled: exportExpensesMutation.isPending, className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DocumentArrowDownIcon */ .A50, { className: "h-4 w-4 mr-1" }), "CSV"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", size: "sm", onClick: () => handleExport('pdf'), disabled: exportExpensesMutation.isPending, className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DocumentArrowDownIcon */ .A50, { className: "h-4 w-4 mr-1" }), "PDF"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", size: "sm", onClick: () => handleExport('excel'), disabled: exportExpensesMutation.isPending, className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DocumentArrowDownIcon */ .A50, { className: "h-4 w-4 mr-1" }), "Excel"] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { onClick: handleAddExpense, variant: "primary", className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .PlusIcon */ .c11, { className: "h-5 w-5 mr-2" }), "Adaug\u0103 cheltuial\u0103"] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col sm:flex-row gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .MagnifyingGlassIcon */ .$p$, { className: "h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "text", placeholder: "Caut\u0103 cheltuieli...", value: searchTerm, onChange: e => setSearchTerm(e.target.value), className: "pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500" })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex gap-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select", { value: selectedCategory, onChange: e => setSelectedCategory(e.target.value), className: "px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "", children: "Toate categoriile" }), categories.map(category => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: category, children: category }, category)))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay, { variant: "outline", onClick: () => setShowFilters(!showFilters), className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .FunnelIcon */ .mWh, { className: "h-5 w-5 mr-2" }), "Filtre"] })] })] }), showFilters && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-4 pt-4 border-t border-gray-200", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 sm:grid-cols-3 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Data de la" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "date", className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Data p\u00E2n\u0103 la" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "date", className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Suma minim\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "number", placeholder: "0.00", className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500" })] })] }) }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { children: isLoading ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center justify-center py-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { size: "lg" }) })) : expenses && expenses.length > 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "overflow-x-auto", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("table", { className: "min-w-full divide-y divide-gray-200", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("thead", { className: "bg-gray-50", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("tr", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Descriere" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Categorie" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Suma" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Data" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Metoda de plat\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("th", { className: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Ac\u021Biuni" })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("tbody", { className: "bg-white divide-y divide-gray-200", children: expenses.map(expense => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("tr", { className: "hover:bg-gray-50", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("td", { className: "px-6 py-4 whitespace-nowrap", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm font-medium text-gray-900", children: expense.description }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("td", { className: "px-6 py-4 whitespace-nowrap", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800", children: expense.category }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("td", { className: "px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_8__/* .formatCurrency */ .vv)(expense.amount) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_8__/* .formatDate */ .Yq)(expense.date) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500", children: expense.paymentMethod }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("td", { className: "px-6 py-4 whitespace-nowrap text-right text-sm font-medium", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-end space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { className: "text-primary-600 hover:text-primary-900", children: "Editeaz\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { className: "text-red-600 hover:text-red-900", children: "\u0218terge" })] }) })] }, expense.id))) })] }) })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center py-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-500", children: "Nu au fost g\u0103site cheltuieli" }) })) })] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Expenses);


/***/ })

}]);