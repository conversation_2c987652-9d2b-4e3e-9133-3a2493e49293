"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[179],{

/***/ 560:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(888);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4976);
/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6103);
/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(125);
/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(2552);
/* harmony import */ var _hooks_useLanguage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(1307);
/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(250);










const Settings = () => {
    const navigate = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .useNavigate */ .Zp)();
    const { logout } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_9__/* .useAuthStore */ .nc)();
    const { t, setLanguage, currentLanguage, getLanguagesWithNames } = (0,_hooks_useLanguage__WEBPACK_IMPORTED_MODULE_8__/* .useLanguage */ .o)();
    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const [showLogoutModal, setShowLogoutModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
        currency: 'RON',
        language: currentLanguage,
        theme: 'light',
        dateFormat: 'dd/mm/yyyy',
        notifications: {
            email: true,
            push: true,
            weekly: true,
            budget: true,
        },
        privacy: {
            dataSharing: false,
            analytics: true,
            marketing: false,
        },
    });
    const currencies = [
        { value: 'RON', label: t('currencies.RON') },
        { value: 'EUR', label: t('currencies.EUR') },
        { value: 'USD', label: t('currencies.USD') },
        { value: 'GBP', label: t('currencies.GBP') },
    ];
    const languages = getLanguagesWithNames().map(lang => ({
        value: lang.code,
        label: lang.name,
    }));
    const themes = [
        { value: 'light', label: t('settings.general.themes.light') },
        { value: 'dark', label: t('settings.general.themes.dark') },
        { value: 'auto', label: t('settings.general.themes.auto') },
    ];
    const dateFormats = [
        { value: 'dd/mm/yyyy', label: 'DD/MM/YYYY' },
        { value: 'mm/dd/yyyy', label: 'MM/DD/YYYY' },
        { value: 'yyyy-mm-dd', label: 'YYYY-MM-DD' },
    ];
    const handleSettingChange = (category, key, value) => {
        setSettings(prev => {
            const newSettings = { ...prev };
            if (key && (category === 'notifications' || category === 'privacy')) {
                newSettings[category][key] = value;
            }
            else {
                newSettings[category] = value;
            }
            return newSettings;
        });
        // Dacă se schimbă limba, actualizează și sistemul i18n
        if (category === 'language' && typeof value === 'string') {
            setLanguage(value);
        }
    };
    const handleSaveSettings = async () => {
        try {
            // Aici ar trebui să faci request către API
            await new Promise(resolve => setTimeout(resolve, 1000));
            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__/* .toast */ .oR.success(t('settings.saveSuccess'));
        }
        catch (error) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__/* .toast */ .oR.error(t('settings.saveError'));
        }
    };
    const handleLogout = async () => {
        try {
            await logout();
            navigate('/auth/login');
            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__/* .toast */ .oR.success(t('auth.logoutSuccess'));
        }
        catch (error) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__/* .toast */ .oR.error(t('auth.loginError'));
        }
        setShowLogoutModal(false);
    };
    const handleDeleteAccount = async () => {
        try {
            // Aici ar trebui să faci request către API
            await new Promise(resolve => setTimeout(resolve, 1000));
            await logout();
            navigate('/auth/login');
            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__/* .toast */ .oR.success(t('settings.dangerZone.deleteAccount.success'));
        }
        catch (error) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__/* .toast */ .oR.error(t('settings.dangerZone.deleteAccount.error'));
        }
        setShowDeleteModal(false);
    };
    const handleExportData = async () => {
        try {
            // Aici ar trebui să faci request către API
            await new Promise(resolve => setTimeout(resolve, 2000));
            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__/* .toast */ .oR.success(t('settings.export.success'));
        }
        catch (error) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__/* .toast */ .oR.error(t('settings.export.error'));
        }
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1", { className: "text-2xl font-bold text-gray-900", children: t('settings.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: t('settings.subtitle') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CogIcon */ .DP5, { className: "h-6 w-6 text-gray-400 mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-lg font-medium text-gray-900", children: t('settings.general.title') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: t('settings.general.currency') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("select", { value: settings.currency, onChange: (e) => handleSettingChange('currency', null, e.target.value), className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500", children: currencies.map((currency) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: currency.value, children: currency.label }, currency.value))) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: t('settings.general.language') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("select", { value: settings.language, onChange: (e) => handleSettingChange('language', null, e.target.value), className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500", children: languages.map((language) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: language.value, children: language.label }, language.value))) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: t('settings.general.theme') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("select", { value: settings.theme, onChange: (e) => handleSettingChange('theme', null, e.target.value), className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500", children: themes.map((theme) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: theme.value, children: theme.label }, theme.value))) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: t('settings.general.dateFormat') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("select", { value: settings.dateFormat, onChange: (e) => handleSettingChange('dateFormat', null, e.target.value), className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500", children: dateFormats.map((format) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: format.value, children: format.label }, format.value))) })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .BellIcon */ .XFE, { className: "h-6 w-6 text-gray-400 mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-lg font-medium text-gray-900", children: t('settings.notifications.title') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-gray-900", children: "Notific\u0103ri email" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: "Prime\u0219te notific\u0103ri prin email" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", checked: settings.notifications.email, onChange: (e) => handleSettingChange('notifications', 'email', e.target.checked), className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-gray-900", children: "Notific\u0103ri push" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: "Prime\u0219te notific\u0103ri \u00EEn browser" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", checked: settings.notifications.push, onChange: (e) => handleSettingChange('notifications', 'push', e.target.checked), className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-gray-900", children: "Rapoarte s\u0103pt\u0103m\u00E2nale" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: "Prime\u0219te un rezumat s\u0103pt\u0103m\u00E2nal" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", checked: settings.notifications.weekly, onChange: (e) => handleSettingChange('notifications', 'weekly', e.target.checked), className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-gray-900", children: "Alerte buget" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: "Prime\u0219te alerte c\u00E2nd dep\u0103\u0219e\u0219ti bugetul" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", checked: settings.notifications.budget, onChange: (e) => handleSettingChange('notifications', 'budget', e.target.checked), className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ShieldCheckIcon */ .Zus, { className: "h-6 w-6 text-gray-400 mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-lg font-medium text-gray-900", children: t('settings.privacy.title') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-gray-900", children: t('settings.privacy.dataSharing.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: t('settings.privacy.dataSharing.description') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", checked: settings.privacy.dataSharing, onChange: (e) => handleSettingChange('privacy', 'dataSharing', e.target.checked), className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-gray-900", children: t('settings.privacy.analytics.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: t('settings.privacy.analytics.description') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", checked: settings.privacy.analytics, onChange: (e) => handleSettingChange('privacy', 'analytics', e.target.checked), className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-gray-900", children: t('settings.privacy.marketing.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: t('settings.privacy.marketing.description') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", checked: settings.privacy.marketing, onChange: (e) => handleSettingChange('privacy', 'marketing', e.target.checked), className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CurrencyDollarIcon */ .xmO, { className: "h-6 w-6 text-gray-400 mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-lg font-medium text-gray-900", children: t('settings.export.title') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-gray-900", children: t('settings.export.allData') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: t('settings.export.description') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "outline", onClick: handleExportData, children: t('settings.export.button') })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ExclamationTriangleIcon */ .Pip, { className: "h-6 w-6 text-red-500 mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-lg font-medium text-gray-900", children: t('settings.dangerZone.title') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between py-4 border-b border-gray-200", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-gray-900", children: t('settings.dangerZone.logout.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: t('settings.dangerZone.logout.description') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "outline", onClick: () => setShowLogoutModal(true), className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowRightOnRectangleIcon */ .RzF, { className: "h-4 w-4 mr-2" }), t('settings.dangerZone.logout.button')] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between pt-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-red-900", children: t('settings.dangerZone.deleteAccount.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-red-500", children: t('settings.dangerZone.deleteAccount.description') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "danger", onClick: () => setShowDeleteModal(true), className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .TrashIcon */ .ucK, { className: "h-4 w-4 mr-2" }), t('settings.dangerZone.deleteAccount.button')] })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex justify-end", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "primary", onClick: handleSaveSettings, children: t('settings.saveButton') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, { isOpen: showLogoutModal, onClose: () => setShowLogoutModal(false), title: t('settings.dangerZone.logout.confirmTitle'), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: t('settings.dangerZone.logout.confirmMessage') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-end space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "outline", onClick: () => setShowLogoutModal(false), children: t('common.cancel') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "primary", onClick: handleLogout, children: t('settings.dangerZone.logout.confirmButton') })] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, { isOpen: showDeleteModal, onClose: () => setShowDeleteModal(false), title: t('settings.dangerZone.deleteAccount.confirmTitle'), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-red-50 border border-red-200 rounded-md p-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .TrashIcon */ .ucK, { className: "h-5 w-5 text-red-400 mr-2" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-red-800", children: t('settings.dangerZone.deleteAccount.warningTitle') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-red-700 mt-1", children: t('settings.dangerZone.deleteAccount.warningMessage') })] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: t('settings.dangerZone.deleteAccount.confirmInstructions') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "text", placeholder: t('settings.dangerZone.deleteAccount.confirmPlaceholder'), className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-end space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "outline", onClick: () => setShowDeleteModal(false), children: t('common.cancel') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "danger", onClick: handleDeleteAccount, children: t('settings.dangerZone.deleteAccount.confirmButton') })] })] }) })] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Settings);


/***/ })

}]);