/**
 * Teste pentru tipurile actualizate din frontend
 */

import {
  User,
  Category,
  Expense,
  Subscription,
  SubscriptionPlan,
  UsageLog,
  WebhookEvent,
  RegisterForm,
  LoginForm,
  CreateExpenseForm,
  CreateCategoryForm,
  ExpenseFilters,
  SortOptions,
  ApiResponse,
  PaginatedResponse
} from '../index';

describe('Frontend Types', () => {
  describe('User Type', () => {
    it('should have correct structure with camelCase fields', () => {
      const user: User = {
        id: 'c1234567890123456789012345',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'user',
        currency: 'USD',
        timezone: 'UTC',
        isActive: true,
        emailVerified: false,
        loginCount: 0,
        preferences: {},
        lastUsageReset: '2023-01-01T00:00:00.000Z',
        monthlyExpenseCount: 0,
        monthlyExpenseLimit: 50,
        planType: 'free',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z'
      };

      expect(user.firstName).toBe('John');
      expect(user.lastName).toBe('Doe');
      expect(user.isActive).toBe(true);
      expect(user.emailVerified).toBe(false);
      expect(user.monthlyExpenseCount).toBe(0);
      expect(user.planType).toBe('free');
    });

    it('should support all role types', () => {
      const userRole: User['role'] = 'user';
      const adminRole: User['role'] = 'admin';

      expect(['user', 'admin']).toContain(userRole);
      expect(['user', 'admin']).toContain(adminRole);
    });

    it('should support all plan types', () => {
      const freePlan: User['planType'] = 'free';
      const basicPlan: User['planType'] = 'basic';
      const premiumPlan: User['planType'] = 'premium';

      expect(['free', 'basic', 'premium']).toContain(freePlan);
      expect(['free', 'basic', 'premium']).toContain(basicPlan);
      expect(['free', 'basic', 'premium']).toContain(premiumPlan);
    });
  });

  describe('Category Type', () => {
    it('should have correct structure with camelCase fields', () => {
      const category: Category = {
        id: 'c1234567890123456789012345',
        name: 'Food',
        description: 'Food and dining expenses',
        color: '#FF0000',
        icon: 'food',
        budgetLimit: 500,
        budgetPeriod: 'monthly',
        isActive: true,
        isDefault: false,
        sortOrder: 1,
        userId: 'c1234567890123456789012345',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z'
      };

      expect(category.budgetLimit).toBe(500);
      expect(category.budgetPeriod).toBe('monthly');
      expect(category.isActive).toBe(true);
      expect(category.isDefault).toBe(false);
      expect(category.sortOrder).toBe(1);
      expect(category.userId).toBe('c1234567890123456789012345');
    });

    it('should support all budget period types', () => {
      const periods: Category['budgetPeriod'][] = ['daily', 'weekly', 'monthly', 'yearly'];
      
      periods.forEach(period => {
        expect(['daily', 'weekly', 'monthly', 'yearly']).toContain(period);
      });
    });
  });

  describe('Expense Type', () => {
    it('should have correct structure with camelCase fields', () => {
      const expense: Expense = {
        id: 'c1234567890123456789012345',
        amount: 25.50,
        description: 'Lunch at restaurant',
        date: '2023-01-01T12:00:00.000Z',
        notes: 'Great food!',
        paymentMethod: 'card',
        location: 'Downtown Restaurant',
        receiptUrl: 'https://example.com/receipt.jpg',
        tags: ['lunch', 'restaurant'],
        isRecurring: false,
        recurringFrequency: 'monthly',
        recurringEndDate: '2023-12-31T23:59:59.999Z',
        userId: 'c1234567890123456789012345',
        categoryId: 'c1234567890123456789012345',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z'
      };

      expect(expense.paymentMethod).toBe('card');
      expect(expense.receiptUrl).toBe('https://example.com/receipt.jpg');
      expect(expense.isRecurring).toBe(false);
      expect(expense.recurringFrequency).toBe('monthly');
      expect(expense.recurringEndDate).toBe('2023-12-31T23:59:59.999Z');
      expect(expense.userId).toBe('c1234567890123456789012345');
      expect(expense.categoryId).toBe('c1234567890123456789012345');
    });

    it('should support all payment method types', () => {
      const methods: Expense['paymentMethod'][] = [
        'cash', 'card', 'bank_transfer', 'digital_wallet', 'other'
      ];
      
      methods.forEach(method => {
        expect(['cash', 'card', 'bank_transfer', 'digital_wallet', 'other']).toContain(method);
      });
    });

    it('should support all recurring frequency types', () => {
      const frequencies: NonNullable<Expense['recurringFrequency']>[] = [
        'daily', 'weekly', 'monthly', 'yearly'
      ];
      
      frequencies.forEach(frequency => {
        expect(['daily', 'weekly', 'monthly', 'yearly']).toContain(frequency);
      });
    });
  });

  describe('Form Types', () => {
    describe('RegisterForm', () => {
      it('should have correct structure with camelCase fields', () => {
        const form: RegisterForm = {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          password: 'password123',
          passwordConfirmation: 'password123',
          termsAccepted: true,
          currency: 'USD',
          timezone: 'UTC'
        };

        expect(form.firstName).toBe('John');
        expect(form.lastName).toBe('Doe');
        expect(form.passwordConfirmation).toBe('password123');
        expect(form.termsAccepted).toBe(true);
      });
    });

    describe('LoginForm', () => {
      it('should have correct structure with camelCase fields', () => {
        const form: LoginForm = {
          email: '<EMAIL>',
          password: 'password123',
          rememberMe: true
        };

        expect(form.rememberMe).toBe(true);
      });
    });

    describe('CreateExpenseForm', () => {
      it('should have correct structure with camelCase fields', () => {
        const form: CreateExpenseForm = {
          amount: 25.50,
          description: 'Lunch',
          categoryId: 'c1234567890123456789012345',
          date: '2023-01-01T12:00:00.000Z',
          paymentMethod: 'card',
          isRecurring: false,
          recurringFrequency: 'monthly'
        };

        expect(form.categoryId).toBe('c1234567890123456789012345');
        expect(form.paymentMethod).toBe('card');
        expect(form.isRecurring).toBe(false);
        expect(form.recurringFrequency).toBe('monthly');
      });
    });

    describe('CreateCategoryForm', () => {
      it('should have correct structure with camelCase fields', () => {
        const form: CreateCategoryForm = {
          name: 'Food',
          description: 'Food expenses',
          color: '#FF0000',
          icon: 'food',
          budgetLimit: 500,
          budgetPeriod: 'monthly',
          sortOrder: 1,
          isDefault: false
        };

        expect(form.budgetLimit).toBe(500);
        expect(form.budgetPeriod).toBe('monthly');
        expect(form.sortOrder).toBe(1);
        expect(form.isDefault).toBe(false);
      });
    });
  });

  describe('Filter and Sort Types', () => {
    describe('ExpenseFilters', () => {
      it('should have correct structure with camelCase fields', () => {
        const filters: ExpenseFilters = {
          categoryId: 'c1234567890123456789012345',
          startDate: '2023-01-01',
          endDate: '2023-01-31',
          minAmount: 10,
          maxAmount: 100,
          paymentMethod: 'card',
          isRecurring: false,
          tags: ['food', 'restaurant'],
          search: 'lunch'
        };

        expect(filters.startDate).toBe('2023-01-01');
        expect(filters.endDate).toBe('2023-01-31');
        expect(filters.minAmount).toBe(10);
        expect(filters.maxAmount).toBe(100);
        expect(filters.paymentMethod).toBe('card');
        expect(filters.isRecurring).toBe(false);
      });
    });

    describe('SortOptions', () => {
      it('should have correct structure with camelCase fields', () => {
        const sort: SortOptions = {
          sortBy: 'date',
          sortOrder: 'desc'
        };

        expect(sort.sortBy).toBe('date');
        expect(sort.sortOrder).toBe('desc');
      });
    });
  });

  describe('API Response Types', () => {
    describe('ApiResponse', () => {
      it('should handle success response', () => {
        const response: ApiResponse<User> = {
          success: true,
          data: {
            id: 'c1234567890123456789012345',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            role: 'user',
            currency: 'USD',
            timezone: 'UTC',
            isActive: true,
            emailVerified: false,
            loginCount: 0,
            preferences: {},
            lastUsageReset: '2023-01-01T00:00:00.000Z',
            monthlyExpenseCount: 0,
            monthlyExpenseLimit: 50,
            planType: 'free',
            createdAt: '2023-01-01T00:00:00.000Z',
            updatedAt: '2023-01-01T00:00:00.000Z'
          },
          message: 'Success'
        };

        expect(response.success).toBe(true);
        expect(response.data?.firstName).toBe('John');
      });

      it('should handle error response', () => {
        const response: ApiResponse<User> = {
          success: false,
          error: 'User not found',
          errors: [
            {
              field: 'email',
              message: 'Email is required'
            }
          ]
        };

        expect(response.success).toBe(false);
        expect(response.error).toBe('User not found');
        expect(response.errors?.[0].field).toBe('email');
      });
    });

    describe('PaginatedResponse', () => {
      it('should have correct structure', () => {
        const response: PaginatedResponse<Expense> = {
          data: [],
          pagination: {
            page: 1,
            limit: 20,
            total: 100,
            totalPages: 5
          }
        };

        expect(response.pagination.page).toBe(1);
        expect(response.pagination.totalPages).toBe(5);
      });
    });
  });
});
