"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[245],{

/***/ 6245:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2389);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4976);
/* harmony import */ var _components_layout_PublicLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(4370);






const Features = () => {
    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__/* .useTranslation */ .Bd)();
    const features = [
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChartBarIcon */ .r95,
            title: t('features.analytics.title', 'Analiză Avansată'),
            description: t('features.analytics.description', 'Vizualizări interactive și rapoarte detaliate pentru a înțelege mai bine finanțele dvs.'),
            benefits: [
                t('features.analytics.benefit1', 'Grafice și diagrame interactive'),
                t('features.analytics.benefit2', 'Rapoarte personalizabile'),
                t('features.analytics.benefit3', 'Predicții bazate pe AI'),
                t('features.analytics.benefit4', 'Export în multiple formate'),
            ],
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CreditCardIcon */ .BFk,
            title: t('features.transactions.title', 'Gestionare Tranzacții'),
            description: t('features.transactions.description', 'Urmăriți și categorizați toate tranzacțiile dvs. cu ușurință și precizie.'),
            benefits: [
                t('features.transactions.benefit1', 'Categorizare automată'),
                t('features.transactions.benefit2', 'Import din bănci'),
                t('features.transactions.benefit3', 'Detectare duplicate'),
                t('features.transactions.benefit4', 'Etichete personalizate'),
            ],
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .BellIcon */ .XFE,
            title: t('features.notifications.title', 'Notificări Inteligente'),
            description: t('features.notifications.description', 'Rămâneți la curent cu alertele personalizate pentru buget, facturi și obiective.'),
            benefits: [
                t('features.notifications.benefit1', 'Alerte buget în timp real'),
                t('features.notifications.benefit2', 'Reminder-uri facturi'),
                t('features.notifications.benefit3', 'Notificări obiective'),
                t('features.notifications.benefit4', 'Canale multiple de comunicare'),
            ],
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ShieldCheckIcon */ .Zus,
            title: t('features.security.title', 'Securitate Avansată'),
            description: t('features.security.description', 'Protecția datelor dvs. financiare cu cele mai înalte standarde de securitate.'),
            benefits: [
                t('features.security.benefit1', 'Criptare end-to-end'),
                t('features.security.benefit2', 'Autentificare cu doi factori'),
                t('features.security.benefit3', 'Monitorizare activitate suspectă'),
                t('features.security.benefit4', 'Backup automat securizat'),
            ],
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CurrencyDollarIcon */ .xmO,
            title: t('features.budgeting.title', 'Bugetare Inteligentă'),
            description: t('features.budgeting.description', 'Creați și gestionați bugete personalizate cu recomandări bazate pe AI.'),
            benefits: [
                t('features.budgeting.benefit1', 'Bugete adaptive'),
                t('features.budgeting.benefit2', 'Recomandări personalizate'),
                t('features.budgeting.benefit3', 'Urmărire obiective'),
                t('features.budgeting.benefit4', 'Analiză tendințe cheltuieli'),
            ],
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DocumentTextIcon */ .AQX,
            title: t('features.reports.title', 'Rapoarte Detaliate'),
            description: t('features.reports.description', 'Generați rapoarte comprehensive pentru o înțelegere completă a situației financiare.'),
            benefits: [
                t('features.reports.benefit1', 'Rapoarte lunare/anuale'),
                t('features.reports.benefit2', 'Comparații perioade'),
                t('features.reports.benefit3', 'Analiză categorii'),
                t('features.reports.benefit4', 'Export profesional'),
            ],
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UserGroupIcon */ .K6s,
            title: t('features.collaboration.title', 'Colaborare Familie'),
            description: t('features.collaboration.description', 'Gestionați finanțele familiei cu conturi partajate și permisiuni granulare.'),
            benefits: [
                t('features.collaboration.benefit1', 'Conturi familiale'),
                t('features.collaboration.benefit2', 'Permisiuni personalizate'),
                t('features.collaboration.benefit3', 'Bugete partajate'),
                t('features.collaboration.benefit4', 'Comunicare integrată'),
            ],
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CloudIcon */ .hpF,
            title: t('features.sync.title', 'Sincronizare Cloud'),
            description: t('features.sync.description', 'Accesați datele dvs. de oriunde cu sincronizare automată și backup în cloud.'),
            benefits: [
                t('features.sync.benefit1', 'Sincronizare în timp real'),
                t('features.sync.benefit2', 'Acces multi-dispozitiv'),
                t('features.sync.benefit3', 'Backup automat'),
                t('features.sync.benefit4', 'Recuperare date'),
            ],
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DevicePhoneMobileIcon */ .qXP,
            title: t('features.mobile.title', 'Aplicație Mobilă'),
            description: t('features.mobile.description', 'Gestionați finanțele în mișcare cu aplicația noastră mobilă nativă.'),
            benefits: [
                t('features.mobile.benefit1', 'Interface optimizată'),
                t('features.mobile.benefit2', 'Funcționalitate offline'),
                t('features.mobile.benefit3', 'Notificări push'),
                t('features.mobile.benefit4', 'Scanare bonuri'),
            ],
        },
    ];
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_layout_PublicLayout__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "min-h-screen bg-gray-50", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-white shadow-sm", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/", className: "flex items-center text-gray-600 hover:text-gray-900 transition-colors", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowLeftIcon */ .A60, { className: "w-5 h-5 mr-2" }), t('common.back', 'Înapoi')] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "h-6 w-px bg-gray-300" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1", { className: "text-3xl font-bold text-gray-900", children: t('product.features.title', 'Funcționalități') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "mt-4 text-lg text-gray-600 max-w-3xl", children: t('product.features.subtitle', 'Descoperiți toate funcționalitățile puternice care vă vor ajuta să vă gestionați finanțele mai eficient ca niciodată.') })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-4xl md:text-5xl font-bold mb-6", children: t('product.features.hero.title', 'Totul ce aveți nevoie pentru finanțe perfecte') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto", children: t('product.features.hero.subtitle', 'De la analiză avansată la securitate de nivel bancar - toate într-o singură platformă.') })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8", children: features.map((feature, index) => {
                            const IconComponent = feature.icon;
                            return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-xl shadow-sm border border-gray-200 p-8 hover:shadow-lg transition-all duration-300 hover:-translate-y-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-blue-100 p-3 rounded-lg mr-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconComponent, { className: "w-8 h-8 text-blue-600" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-xl font-semibold text-gray-900", children: feature.title })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600 mb-6 leading-relaxed", children: feature.description }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("ul", { className: "space-y-3", children: feature.benefits.map((benefit, benefitIndex) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("li", { className: "flex items-start", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-gray-700 text-sm", children: benefit })] }, benefitIndex))) })] }, index));
                        }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-white py-16", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center mb-12", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-3xl font-bold text-gray-900 mb-4", children: t('product.features.integration.title', 'Integrări Puternice') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg text-gray-600 max-w-2xl mx-auto", children: t('product.features.integration.subtitle', 'Conectați-vă cu serviciile pe care le utilizați deja pentru o experiență completă.') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CreditCardIcon */ .BFk, { className: "w-8 h-8 text-green-600" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: t('product.features.integration.banking.title', 'Integrare Bancară') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: t('product.features.integration.banking.description', 'Conectare directă cu peste 1000 de bănci și instituții financiare.') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowTrendingUpIcon */ .LOZ, { className: "w-8 h-8 text-purple-600" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: t('product.features.integration.investment.title', 'Platforme Investiții') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: t('product.features.integration.investment.description', 'Urmăriți portofoliul de investiții din toate platformele într-un singur loc.') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .LockClosedIcon */ .JD_, { className: "w-8 h-8 text-blue-600" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: t('product.features.integration.security.title', 'Securitate Avansată') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: t('product.features.integration.security.description', 'Protecție de nivel bancar cu criptare și monitorizare 24/7.') })] })] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-gray-900 text-white py-16", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-3xl font-bold mb-4", children: t('product.features.cta.title', 'Gata să începeți?') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xl text-gray-300 mb-8", children: t('product.features.cta.subtitle', 'Alăturați-vă miilor de utilizatori care și-au transformat finanțele cu FinanceFlow.') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col sm:flex-row gap-4 justify-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/register", className: "bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors", children: t('product.features.cta.start', 'Începeți Gratuit') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/demo", className: "border border-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors", children: t('product.features.cta.demo', 'Vedeți Demo') })] })] }) })] }) }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Features);


/***/ })

}]);