/**
 * <PERSON>e pentru authController actualizat cu suport camelCase
 */

import { Request, Response } from 'express';
import { prisma } from '../../../src/config/prisma';
import * as bcryptjs from 'bcryptjs';
import { register, login, getProfile, updateProfile } from '../../../src/controllers/authController';

// Mock pentru Prisma
jest.mock('../../../src/config/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    category: {
      createMany: jest.fn(),
    },
  },
}));

// Mock pentru bcryptjs
jest.mock('bcryptjs');

// Mock pentru JWT functions
jest.mock('../../../src/middleware/auth', () => ({
  generateToken: jest.fn(() => 'mock-access-token'),
  generateRefreshToken: jest.fn(() => 'mock-refresh-token'),
}));

const mockRequest = (body = {}, user = null) => {
  const req: any = {
    body,
    user,
    path: '/test',
    method: 'POST',
    headers: {},
    query: {},
    params: {},
    get: jest.fn(),
    header: jest.fn()
  };
  return req as Request;
};

const mockResponse = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res as Response;
};

describe('AuthController with camelCase Support', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    it('should register user with camelCase input', async () => {
      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        currency: 'USD',
        timezone: 'UTC'
      };

      const mockUser = {
        id: 'user-cuid-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        currency: 'USD',
        timezone: 'UTC',
        password: 'hashed-password',
        role: 'user',
        isActive: true,
        emailVerified: false,
        loginCount: 0,
        lastUsageReset: new Date(),
        monthlyExpenseCount: 0,
        monthlyExpenseLimit: 50,
        planType: 'free',
        preferences: {},
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);
      (bcryptjs.hash as jest.Mock).mockResolvedValue('hashed-password');
      (prisma.user.create as jest.Mock).mockResolvedValue(mockUser);
      (prisma.user.update as jest.Mock).mockResolvedValue(mockUser);
      (prisma.category.createMany as jest.Mock).mockResolvedValue({ count: 6 });

      const req = mockRequest(registerData);
      const res = mockResponse();

      await register(req, res);

      expect(prisma.user.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          currency: 'USD',
          timezone: 'UTC',
          role: 'user',
          isActive: true,
          emailVerified: false,
          planType: 'free'
        })
      });

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        message: 'User registered successfully',
        data: expect.objectContaining({
          user: expect.any(Object),
          tokens: expect.objectContaining({
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token'
          })
        })
      }));
    });

    it('should return error for existing user', async () => {
      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe'
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue({ id: 'existing-user' });

      const req = mockRequest(registerData);
      const res = mockResponse();

      await register(req, res);

      expect(res.status).toHaveBeenCalledWith(409);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'User with this email already exists'
      });
    });
  });

  describe('login', () => {
    it('should login user successfully', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const mockUser = {
        id: 'user-cuid-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'hashed-password',
        isActive: true,
        planType: 'free',
        subscriptionStatus: null,
        subscriptionCurrentPeriodEnd: null,
        refreshToken: null
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
      (bcryptjs.compare as jest.Mock).mockResolvedValue(true);
      (prisma.user.update as jest.Mock).mockResolvedValue(mockUser);

      const req = mockRequest(loginData);
      const res = mockResponse();

      await login(req, res);

      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user-cuid-123' },
        data: expect.objectContaining({
          lastLogin: expect.any(Date),
          loginCount: { increment: 1 },
          refreshToken: 'mock-refresh-token'
        })
      });

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        message: 'Login successful',
        data: expect.objectContaining({
          user: expect.any(Object),
          tokens: expect.objectContaining({
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token'
          })
        })
      }));
    });

    it('should return error for invalid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrong-password'
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      const req = mockRequest(loginData);
      const res = mockResponse();

      await login(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Invalid email or password'
      });
    });

    it('should return error for inactive user', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const mockUser = {
        id: 'user-cuid-123',
        email: '<EMAIL>',
        password: 'hashed-password',
        isActive: false
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const req = mockRequest(loginData);
      const res = mockResponse();

      await login(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Account is deactivated. Please contact support.'
      });
    });
  });

  describe('updateProfile', () => {
    it('should update profile with camelCase input', async () => {
      const updateData = {
        firstName: 'Jane',
        lastName: 'Smith',
        currency: 'EUR',
        timezone: 'Europe/London',
        avatar: 'https://example.com/avatar.jpg'
      };

      const mockUser = {
        id: 'user-cuid-123',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        currency: 'EUR',
        timezone: 'Europe/London',
        avatar: 'https://example.com/avatar.jpg',
        planType: 'free',
        subscriptionStatus: null,
        subscriptionCurrentPeriodEnd: null
      };

      (prisma.user.update as jest.Mock).mockResolvedValue(mockUser);

      const req = mockRequest(updateData);
      req.user = { id: 'user-cuid-123' };
      const res = mockResponse();

      await updateProfile(req, res);

      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user-cuid-123' },
        data: {
          firstName: 'Jane',
          lastName: 'Smith',
          currency: 'EUR',
          timezone: 'Europe/London',
          avatar: 'https://example.com/avatar.jpg'
        }
      });

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        message: 'Profile updated successfully',
        data: expect.objectContaining({
          user: expect.any(Object)
        })
      }));
    });
  });
});
