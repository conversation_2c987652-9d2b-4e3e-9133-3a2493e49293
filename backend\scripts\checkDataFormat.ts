/**
 * Script simplu pentru verificarea formatului datelor în baza de date
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🔍 Verificarea formatului datelor în baza de date...\n');

  try {
    // Verifică câteva utilizatori pentru a vedea formatul ID-urilor
    const users = await prisma.user.findMany({
      take: 5,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        createdAt: true,
      },
    });

    console.log('👤 Utilizatori găsiți:');
    if (users.length === 0) {
      console.log('   Nu există utilizatori în baza de date.');
    } else {
      users.forEach((user, index) => {
        const idType = /^c[a-z0-9]{24}$/.test(user.id) ? 'CUID' : 'Altceva';
        console.log(`   ${index + 1}. ID: ${user.id} (${idType})`);
        console.log(`      Email: ${user.email}`);
        console.log(`      Nume: ${user.firstName} ${user.lastName}`);
        console.log(`      Creat: ${user.createdAt.toISOString()}`);
        console.log('');
      });
    }

    // Verifică câteva categorii
    const categories = await prisma.category.findMany({
      take: 3,
      select: {
        id: true,
        name: true,
        userId: true,
        createdAt: true,
      },
    });

    console.log('📂 Categorii găsite:');
    if (categories.length === 0) {
      console.log('   Nu există categorii în baza de date.');
    } else {
      categories.forEach((category, index) => {
        const idType = /^c[a-z0-9]{24}$/.test(category.id) ? 'CUID' : 'Altceva';
        const userIdType = /^c[a-z0-9]{24}$/.test(category.userId) ? 'CUID' : 'Altceva';
        console.log(`   ${index + 1}. ID: ${category.id} (${idType})`);
        console.log(`      Nume: ${category.name}`);
        console.log(`      User ID: ${category.userId} (${userIdType})`);
        console.log(`      Creat: ${category.createdAt.toISOString()}`);
        console.log('');
      });
    }

    // Verifică câteva cheltuieli
    const expenses = await prisma.expense.findMany({
      take: 3,
      select: {
        id: true,
        description: true,
        amount: true,
        userId: true,
        categoryId: true,
        createdAt: true,
      },
    });

    console.log('💰 Cheltuieli găsite:');
    if (expenses.length === 0) {
      console.log('   Nu există cheltuieli în baza de date.');
    } else {
      expenses.forEach((expense, index) => {
        const idType = /^c[a-z0-9]{24}$/.test(expense.id) ? 'CUID' : 'Altceva';
        const userIdType = /^c[a-z0-9]{24}$/.test(expense.userId) ? 'CUID' : 'Altceva';
        const categoryIdType = /^c[a-z0-9]{24}$/.test(expense.categoryId) ? 'CUID' : 'Altceva';
        console.log(`   ${index + 1}. ID: ${expense.id} (${idType})`);
        console.log(`      Descriere: ${expense.description}`);
        console.log(`      Sumă: ${expense.amount}`);
        console.log(`      User ID: ${expense.userId} (${userIdType})`);
        console.log(`      Category ID: ${expense.categoryId} (${categoryIdType})`);
        console.log(`      Creat: ${expense.createdAt.toISOString()}`);
        console.log('');
      });
    }

    // Statistici generale
    const userCount = await prisma.user.count();
    const categoryCount = await prisma.category.count();
    const expenseCount = await prisma.expense.count();

    console.log('📊 Statistici generale:');
    console.log(`   - Utilizatori: ${userCount}`);
    console.log(`   - Categorii: ${categoryCount}`);
    console.log(`   - Cheltuieli: ${expenseCount}`);

    // Verifică dacă toate ID-urile sunt CUID
    const allUsers = await prisma.user.findMany({ select: { id: true } });
    const allCategories = await prisma.category.findMany({ select: { id: true } });
    const allExpenses = await prisma.expense.findMany({ select: { id: true } });

    const userCuidCount = allUsers.filter(u => /^c[a-z0-9]{24}$/.test(u.id)).length;
    const categoryCuidCount = allCategories.filter(c => /^c[a-z0-9]{24}$/.test(c.id)).length;
    const expenseCuidCount = allExpenses.filter(e => /^c[a-z0-9]{24}$/.test(e.id)).length;

    console.log('\n🔍 Analiza ID-urilor:');
    console.log(`   - Utilizatori cu CUID: ${userCuidCount}/${userCount} (${userCount > 0 ? Math.round((userCuidCount / userCount) * 100) : 0}%)`);
    console.log(`   - Categorii cu CUID: ${categoryCuidCount}/${categoryCount} (${categoryCount > 0 ? Math.round((categoryCuidCount / categoryCount) * 100) : 0}%)`);
    console.log(`   - Cheltuieli cu CUID: ${expenseCuidCount}/${expenseCount} (${expenseCount > 0 ? Math.round((expenseCuidCount / expenseCount) * 100) : 0}%)`);

    const allCuid = userCuidCount === userCount && categoryCuidCount === categoryCount && expenseCuidCount === expenseCount;
    
    if (allCuid) {
      console.log('\n✅ Toate ID-urile sunt în format CUID! Migrarea este completă.');
    } else {
      console.log('\n⚠️  Nu toate ID-urile sunt în format CUID. Migrarea poate fi necesară.');
    }

    // Verifică structura tabelelor
    console.log('\n🏗️  Verificarea structurii tabelelor:');
    
    const userColumns = await prisma.$queryRaw<Array<{ column_name: string, data_type: string }>>`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position
    `;

    console.log('   Coloane în tabela users:');
    userColumns.forEach(col => {
      console.log(`     - ${col.column_name}: ${col.data_type}`);
    });

    const hasFirstName = userColumns.some(col => col.column_name === 'firstName');
    const hasLastName = userColumns.some(col => col.column_name === 'lastName');
    const hasName = userColumns.some(col => col.column_name === 'name');

    console.log('\n📝 Analiza câmpurilor nume:');
    console.log(`   - Are firstName: ${hasFirstName ? '✅' : '❌'}`);
    console.log(`   - Are lastName: ${hasLastName ? '✅' : '❌'}`);
    console.log(`   - Are name (vechi): ${hasName ? '⚠️' : '✅'}`);

    if (hasFirstName && hasLastName && !hasName) {
      console.log('\n✅ Structura numelor este corectă (firstName + lastName)!');
    } else {
      console.log('\n⚠️  Structura numelor necesită actualizare.');
    }

  } catch (error) {
    console.error('❌ Eroare la verificarea datelor:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { main as checkDataFormat };
