"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[353],{

/***/ 3353:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(893);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9785);
/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(888);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(4976);
/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(4862);
/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(6103);
/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(6215);










// Schema de validare pentru formularul de forgot password
const forgotPasswordSchema = zod__WEBPACK_IMPORTED_MODULE_7__.z.object({
    email: zod__WEBPACK_IMPORTED_MODULE_7__.z.string()
        .min(1, 'Email-ul este obligatoriu')
        .email('Email-ul nu este valid'),
});
const ForgotPassword = () => {
    const [isLoading, setIsLoading] = react__WEBPACK_IMPORTED_MODULE_3___default().useState(false);
    const [isSubmitted, setIsSubmitted] = react__WEBPACK_IMPORTED_MODULE_3___default().useState(false);
    const { register, handleSubmit, formState: { errors }, getValues, } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_4__/* .useForm */ .mN)({
        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__/* .zodResolver */ .u)(forgotPasswordSchema),
    });
    const onSubmit = async (data) => {
        setIsLoading(true);
        try {
            // Aici ar trebui să faci request către API pentru resetarea parolei
            // await authService.forgotPassword(data.email);
            // Simulare request
            await new Promise(resolve => setTimeout(resolve, 2000));
            setIsSubmitted(true);
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.success('Email-ul de resetare a fost trimis!');
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Eroare la trimiterea email-ului';
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.error(errorMessage);
        }
        finally {
            setIsLoading(false);
        }
    };
    if (isSubmitted) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-md w-full space-y-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("svg", { className: "h-6 w-6 text-green-600", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M5 13l4 4L19 7" }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "mt-6 text-center text-3xl font-extrabold text-gray-900", children: "Email trimis!" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "mt-2 text-center text-sm text-gray-600", children: ["Am trimis instruc\u021Biunile de resetare a parolei la adresa", ' ', (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "font-medium text-gray-900", children: getValues('email') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "mt-4 text-center text-sm text-gray-600", children: "Verific\u0103-\u021Bi inbox-ul \u0219i urmeaz\u0103 instruc\u021Biunile din email." })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .Link */ .N_, { to: "/login", className: "group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowLeftIcon */ .A60, { className: "h-5 w-5 mr-2" }), "\u00CEnapoi la conectare"] }) })] }) }));
    }
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-md w-full space-y-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "mt-6 text-center text-3xl font-extrabold text-gray-900", children: "Reseteaz\u0103 parola" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "mt-2 text-center text-sm text-gray-600", children: "Introdu adresa de email asociat\u0103 contului t\u0103u \u0219i \u00EE\u021Bi vom trimite un link pentru resetarea parolei." })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("form", { className: "mt-8 space-y-6", onSubmit: handleSubmit(onSubmit), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { ...register('email'), type: "email", label: "Adresa de email", placeholder: "<EMAIL>", error: errors.email?.message, autoComplete: "email" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay, { type: "submit", variant: "primary", size: "lg", className: "w-full", loading: isLoading, disabled: isLoading, children: isLoading ? 'Se trimite...' : 'Trimite link de resetare' }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .Link */ .N_, { to: "/login", className: "font-medium text-primary-600 hover:text-primary-500 flex items-center justify-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowLeftIcon */ .A60, { className: "h-4 w-4 mr-1" }), "\u00CEnapoi la conectare"] }) })] })] }) }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForgotPassword);


/***/ })

}]);