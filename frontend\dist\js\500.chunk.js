"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[500],{

/***/ 6500:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(893);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9785);
/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(888);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(4976);
/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(4862);
/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(6103);
/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(6215);
/* harmony import */ var _components_ui_Logo__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(7074);
/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(250);












// Schema de validare pentru formularul de login
const loginSchema = zod__WEBPACK_IMPORTED_MODULE_7__.z.object({
    email: zod__WEBPACK_IMPORTED_MODULE_7__.z.string()
        .min(1, 'Email-ul este obligatoriu')
        .email('Email-ul nu este valid'),
    password: zod__WEBPACK_IMPORTED_MODULE_7__.z.string()
        .min(1, 'Parola este obligatorie')
        .min(6, 'Parola trebuie să aibă cel puțin 6 caractere'),
});
const Login = () => {
    const { login, isLoading } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_11__/* .useAuthStore */ .nc)();
    const navigate = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .useNavigate */ .Zp)();
    const { register, handleSubmit, formState: { errors }, } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_4__/* .useForm */ .mN)({
        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__/* .zodResolver */ .u)(loginSchema),
        // Precompletează câmpurile cu credențialele de admin pentru mediul de dezvoltare
        defaultValues: {
            email: '<EMAIL>',
            password: 'admin123',
        },
    });
    const onSubmit = async (data) => {
        try {
            const result = await login(data);
            if (result.success) {
                // Toast-ul de succes este deja afișat în store
                // Obține utilizatorul din store pentru a verifica rolul
                const { user } = _store_authStore__WEBPACK_IMPORTED_MODULE_11__/* .useAuthStore */ .nc.getState();
                // Redirecționează în funcție de rolul utilizatorului
                if (user?.role === 'admin') {
                    navigate('/app/admin/dashboard');
                }
                else {
                    navigate('/app/dashboard');
                }
            }
            else {
                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.error(result.message || 'Eroare la autentificare');
            }
        }
        catch (error) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__/* .toast */ .oR.error(error.message || 'Eroare la autentificare');
        }
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .Link */ .N_, { to: "/", className: "flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors group", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowLeftIcon */ .A60, { className: "w-5 h-5 group-hover:-translate-x-1 transition-transform" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "font-medium", children: "\u00CEnapoi la pagina principal\u0103" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Logo__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A, { size: "md", to: "/", animated: true })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "max-w-md w-full", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mb-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Logo__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A, { size: "lg", showText: false, className: "justify-center" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-3xl font-bold text-gray-900 mb-2", children: "Bun venit \u00EEnapoi!" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: "Conecteaz\u0103-te pentru a-\u021Bi accesa contul" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("form", { className: "space-y-6", onSubmit: handleSubmit(onSubmit), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-5", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { ...register('email'), type: "email", label: "Adresa de email", placeholder: "<EMAIL>", error: errors.email?.message, autoComplete: "email", className: "rounded-xl" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { ...register('password'), type: "password", label: "Parola", placeholder: "Introdu parola", error: errors.password?.message, autoComplete: "current-password", className: "rounded-xl" }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { id: "remember-me", name: "remember-me", type: "checkbox", className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded transition-colors" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { htmlFor: "remember-me", className: "ml-2 block text-sm text-gray-700", children: "\u021Aine-m\u0103 minte" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .Link */ .N_, { to: "/forgot-password", className: "font-medium text-primary-600 hover:text-primary-700 transition-colors", children: "Ai uitat parola?" }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay, { type: "submit", variant: "primary", size: "lg", className: "w-full rounded-xl bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg", loading: isLoading, disabled: isLoading, children: isLoading ? 'Se conectează...' : 'Conectează-te' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute inset-0 flex items-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-full border-t border-gray-200" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "relative flex justify-center text-sm", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "px-4 bg-white text-gray-500", children: "sau" }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-gray-600", children: ["Nu ai cont \u00EEnc\u0103?", ' ', (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_6__/* .Link */ .N_, { to: "/register", className: "font-semibold text-primary-600 hover:text-primary-700 transition-colors", children: "Creeaz\u0103 un cont nou" })] }) })] })] })] }) }) })] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Login);


/***/ })

}]);