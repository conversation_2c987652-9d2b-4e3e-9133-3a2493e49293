"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[350],{

/***/ 5350:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2389);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4976);
/* harmony import */ var _components_layout_PublicLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(4370);






const Contact = () => {
    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__/* .useTranslation */ .Bd)();
    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
        name: '',
        email: '',
        subject: '',
        category: 'general',
        priority: 'medium',
        message: '',
    });
    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);
    const categories = [
        {
            id: 'general',
            name: t('contact.categories.general', 'Întrebare Generală'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .InformationCircleIcon */ .KSg,
        },
        {
            id: 'technical',
            name: t('contact.categories.technical', 'Problemă Tehnică'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .BugAntIcon */ .N7R,
        },
        {
            id: 'billing',
            name: t('contact.categories.billing', 'Facturare'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .EnvelopeIcon */ .u6c,
        },
        {
            id: 'feature',
            name: t('contact.categories.feature', 'Cerere Funcționalitate'),
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CheckCircleIcon */ .C1y,
        },
    ];
    const priorities = [
        {
            id: 'low',
            name: t('contact.priority.low', 'Scăzută'),
            color: 'text-green-600',
        },
        {
            id: 'medium',
            name: t('contact.priority.medium', 'Medie'),
            color: 'text-yellow-600',
        },
        {
            id: 'high',
            name: t('contact.priority.high', 'Înaltă'),
            color: 'text-red-600',
        },
    ];
    const contactMethods = [
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChatBubbleLeftRightIcon */ .vQ9,
            title: t('contact.methods.chat.title', 'Chat Live'),
            description: t('contact.methods.chat.description', 'Răspuns imediat pentru întrebări urgente'),
            availability: t('contact.methods.chat.availability', 'Luni - Vineri, 9:00 - 18:00'),
            action: t('contact.methods.chat.action', 'Începeți Chat'),
            color: 'bg-blue-500',
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .EnvelopeIcon */ .u6c,
            title: t('contact.methods.email.title', 'Email'),
            description: t('contact.methods.email.description', 'Pentru întrebări detaliate și documentație'),
            availability: t('contact.methods.email.availability', 'Răspuns în 24 ore'),
            action: '<EMAIL>',
            color: 'bg-green-500',
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .PhoneIcon */ .EsG,
            title: t('contact.methods.phone.title', 'Telefon'),
            description: t('contact.methods.phone.description', 'Suport telefonic pentru clienții Premium'),
            availability: t('contact.methods.phone.availability', 'Luni - Vineri, 10:00 - 17:00'),
            action: '+40 21 123 4567',
            color: 'bg-purple-500',
        },
    ];
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value,
        }));
    };
    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);
        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));
            setSubmitStatus('success');
            setFormData({
                name: '',
                email: '',
                subject: '',
                category: 'general',
                priority: 'medium',
                message: '',
            });
        }
        catch (error) {
            setSubmitStatus('error');
        }
        finally {
            setIsSubmitting(false);
        }
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_layout_PublicLayout__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "min-h-screen bg-gray-50", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-white shadow-sm", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/", className: "flex items-center text-gray-600 hover:text-gray-900 transition-colors", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowLeftIcon */ .A60, { className: "w-5 h-5 mr-2" }), t('common.back', 'Înapoi')] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "h-6 w-px bg-gray-300" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1", { className: "text-3xl font-bold text-gray-900", children: t('support.contact.title', 'Contact') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "mt-4 text-lg text-gray-600 max-w-3xl", children: t('support.contact.subtitle', 'Suntem aici să vă ajutăm! Alegeți metoda de contact care vi se potrivește cel mai bine.') })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 lg:grid-cols-3 gap-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "lg:col-span-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 mb-6", children: t('contact.methods.title', 'Metode de Contact') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4", children: contactMethods.map((method, index) => {
                                                const IconComponent = method.icon;
                                                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-white rounded-lg p-6 shadow-sm border border-gray-200", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: `${method.color} p-3 rounded-lg mr-4`, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconComponent, { className: "w-6 h-6 text-white" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: method.title }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600 text-sm mb-3", children: method.description }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center text-sm text-gray-500 mb-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ClockIcon */ .O4, { className: "w-4 h-4 mr-1" }), method.availability] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { className: "text-blue-600 hover:text-blue-800 font-medium text-sm", children: method.action })] })] }) }, index));
                                            }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg p-6 shadow-sm border border-gray-200 mt-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: t('contact.office.title', 'Biroul Nostru') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .MapPinIcon */ .TbR, { className: "w-5 h-5 text-gray-400 mr-3 mt-0.5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-900 font-medium", children: t('contact.office.address.title', 'Adresa') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-gray-600 text-sm", children: [t('contact.office.address.line1', 'Strada Exemplu nr. 123'), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("br", {}), t('contact.office.address.line2', 'Sector 1, București'), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("br", {}), t('contact.office.address.line3', 'România, 010101')] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ClockIcon */ .O4, { className: "w-5 h-5 text-gray-400 mr-3 mt-0.5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-900 font-medium", children: t('contact.office.hours.title', 'Program') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-gray-600 text-sm", children: [t('contact.office.hours.weekdays', 'Luni - Vineri: 9:00 - 18:00'), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("br", {}), t('contact.office.hours.weekend', 'Sâmbătă - Duminică: Închis')] })] })] })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "lg:col-span-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-2xl font-semibold text-gray-900 mb-6", children: t('contact.form.title', 'Trimiteți-ne un Mesaj') }), submitStatus === 'success' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-green-50 border border-green-200 rounded-lg p-4 mb-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CheckCircleIcon */ .C1y, { className: "w-5 h-5 text-green-600 mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-green-800", children: t('contact.form.success', 'Mesajul a fost trimis cu succes! Vă vom răspunde în curând.') })] }) })), submitStatus === 'error' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-red-50 border border-red-200 rounded-lg p-4 mb-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ExclamationTriangleIcon */ .Pip, { className: "w-5 h-5 text-red-600 mr-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-red-800", children: t('contact.form.error', 'A apărut o eroare. Vă rugăm să încercați din nou.') })] }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("form", { onSubmit: handleSubmit, className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", { htmlFor: "name", className: "block text-sm font-medium text-gray-700 mb-2", children: [t('contact.form.name', 'Nume Complet'), " *"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "text", id: "name", name: "name", value: formData.name, onChange: handleInputChange, required: true, className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent", placeholder: t('contact.form.name_placeholder', 'Introduceți numele dvs.') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", { htmlFor: "email", className: "block text-sm font-medium text-gray-700 mb-2", children: [t('contact.form.email', 'Email'), " *"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "email", id: "email", name: "email", value: formData.email, onChange: handleInputChange, required: true, className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent", placeholder: t('contact.form.email_placeholder', '<EMAIL>') })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", { htmlFor: "subject", className: "block text-sm font-medium text-gray-700 mb-2", children: [t('contact.form.subject', 'Subiect'), " *"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "text", id: "subject", name: "subject", value: formData.subject, onChange: handleInputChange, required: true, className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent", placeholder: t('contact.form.subject_placeholder', 'Descrieți pe scurt problema') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { htmlFor: "category", className: "block text-sm font-medium text-gray-700 mb-2", children: t('contact.form.category', 'Categorie') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("select", { id: "category", name: "category", value: formData.category, onChange: handleInputChange, className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent", children: categories.map((category) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: category.id, children: category.name }, category.id))) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { htmlFor: "priority", className: "block text-sm font-medium text-gray-700 mb-2", children: t('contact.form.priority', 'Prioritate') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("select", { id: "priority", name: "priority", value: formData.priority, onChange: handleInputChange, className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent", children: priorities.map((priority) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: priority.id, children: priority.name }, priority.id))) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", { htmlFor: "message", className: "block text-sm font-medium text-gray-700 mb-2", children: [t('contact.form.message', 'Mesaj'), " *"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("textarea", { id: "message", name: "message", value: formData.message, onChange: handleInputChange, required: true, rows: 6, className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent", placeholder: t('contact.form.message_placeholder', 'Descrieți detaliat problema sau întrebarea dvs...') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-sm text-gray-500", children: ["* ", t('contact.form.required', 'Câmpuri obligatorii')] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { type: "submit", disabled: isSubmitting, className: "bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors", children: isSubmitting
                                                                    ? t('contact.form.sending', 'Se trimite...')
                                                                    : t('contact.form.send', 'Trimite Mesajul') })] })] })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mt-16", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center mb-12", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-3xl font-bold text-gray-900 mb-4", children: t('contact.faq.title', 'Întrebări Frecvente') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg text-gray-600", children: t('contact.faq.subtitle', 'Poate găsiți răspunsul aici înainte de a ne contacta') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg p-6 shadow-sm border border-gray-200", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-3", children: t('contact.faq.response_time.question', 'Cât durează să primiți un răspuns?') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: t('contact.faq.response_time.answer', 'De obicei răspundem în 24 de ore pentru email și imediat pentru chat live în timpul programului.') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg p-6 shadow-sm border border-gray-200", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-3", children: t('contact.faq.technical_support.question', 'Oferiti suport tehnic?') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: t('contact.faq.technical_support.answer', 'Da, echipa noastră tehnică este disponibilă pentru a vă ajuta cu orice problemă tehnică.') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg p-6 shadow-sm border border-gray-200", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-3", children: t('contact.faq.billing_support.question', 'Pot primi ajutor cu facturarea?') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: t('contact.faq.billing_support.answer', 'Absolut! Echipa noastră de facturare vă poate ajuta cu orice întrebări legate de cont și plăți.') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg p-6 shadow-sm border border-gray-200", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-3", children: t('contact.faq.feature_request.question', 'Cum pot cere o funcționalitate nouă?') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: t('contact.faq.feature_request.answer', 'Folosiți formularul de contact și selectați "Cerere Funcționalitate" ca categorie. Apreciem feedback-ul!') })] })] })] })] })] }) }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Contact);


/***/ })

}]);