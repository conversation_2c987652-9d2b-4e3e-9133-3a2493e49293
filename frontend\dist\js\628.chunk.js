"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[628],{

/***/ 6628:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8035);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(888);
/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6103);
/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(125);
/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6215);
/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(9264);
/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(2552);
/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(3658);











// Mock data pentru demonstrație
const mockCategories = [
    {
        id: 'cat_1',
        name: 'Mâncare',
        description: 'Cheltuieli pentru mâncare și băuturi',
        color: '#3B82F6',
        totalExpenses: 450.25,
        expenseCount: 15,
    },
    {
        id: 'cat_2',
        name: 'Transport',
        description: 'Cheltuieli pentru transport și combustibil',
        color: '#10B981',
        totalExpenses: 200.0,
        expenseCount: 8,
    },
    {
        id: 'cat_3',
        name: 'Utilități',
        description: 'Facturi și utilități casnice',
        color: '#F59E0B',
        totalExpenses: 150.0,
        expenseCount: 5,
    },
    {
        id: 'cat_4',
        name: 'Divertisment',
        description: 'Activități de divertisment și hobby-uri',
        color: '#8B5CF6',
        totalExpenses: 50.0,
        expenseCount: 3,
    },
];
const Categories = () => {
    const [showModal, setShowModal] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);
    const [editingCategory, setEditingCategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);
    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({
        name: '',
        description: '',
        color: '#3B82F6',
    });
    // Simulare query pentru categorii
    const { data: categories, isLoading, error, } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__/* .useQuery */ .pw)({
        queryKey: ['categories'],
        queryFn: async () => {
            // Simulare API call
            await new Promise(resolve => setTimeout(resolve, 500));
            return mockCategories;
        },
    });
    const handleOpenModal = (category = null) => {
        if (category) {
            setEditingCategory(category);
            setFormData({
                name: category.name,
                description: category.description,
                color: category.color,
            });
        }
        else {
            setEditingCategory(null);
            setFormData({
                name: '',
                description: '',
                color: '#3B82F6',
            });
        }
        setShowModal(true);
    };
    const handleCloseModal = () => {
        setShowModal(false);
        setEditingCategory(null);
        setFormData({ name: '', description: '', color: '#3B82F6' });
    };
    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            // Aici ar trebui să faci request către API
            if (editingCategory) {
                // Update category
                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__/* .toast */ .oR.success('Categoria a fost actualizată cu succes!');
            }
            else {
                // Create category
                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__/* .toast */ .oR.success('Categoria a fost creată cu succes!');
            }
            handleCloseModal();
        }
        catch (error) {
            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__/* .toast */ .oR.error('Eroare la salvarea categoriei');
        }
    };
    const handleDelete = async (categoryId) => {
        if (window.confirm('Ești sigur că vrei să ștergi această categorie?')) {
            try {
                // Aici ar trebui să faci request către API
                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__/* .toast */ .oR.success('Categoria a fost ștearsă cu succes!');
            }
            catch (error) {
                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__/* .toast */ .oR.error('Eroare la ștergerea categoriei');
            }
        }
    };
    if (error) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center py-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-red-600", children: "Eroare la \u00EEnc\u0103rcarea categoriilor" }) }));
    }
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col sm:flex-row sm:items-center sm:justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1", { className: "text-2xl font-bold text-gray-900", children: "Categorii" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600", children: "Gestioneaz\u0103 categoriile pentru cheltuielile tale" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-4 sm:mt-0", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "primary", onClick: () => handleOpenModal(), className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .PlusIcon */ .c11, { className: "h-5 w-5 mr-2" }), "Adaug\u0103 categorie"] }) })] }), isLoading ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center justify-center py-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay, { size: "lg" }) })) : categories && categories.length > 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6", children: categories.map(category => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-4 h-4 rounded-full mr-3", style: { backgroundColor: category.color } }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-gray-900", children: category.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500 mt-1", children: category.description })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => handleOpenModal(category), className: "text-gray-400 hover:text-gray-600", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .PencilIcon */ .R2l, { className: "h-4 w-4" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => handleDelete(category.id), className: "text-gray-400 hover:text-red-600", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .TrashIcon */ .ucK, { className: "h-4 w-4" }) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-4 pt-4 border-t border-gray-200", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-between items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: "Total cheltuieli" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-gray-900", children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_10__/* .formatCurrency */ .vv)(category.totalExpenses) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-right", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: "Num\u0103rul de cheltuieli" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-gray-900", children: category.expenseCount })] })] }) })] }, category.id))) })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay, { className: "p-12 text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-500 mb-4", children: "Nu ai \u00EEnc\u0103 categorii create" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "primary", onClick: () => handleOpenModal(), className: "flex items-center mx-auto", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .PlusIcon */ .c11, { className: "h-5 w-5 mr-2" }), "Creeaz\u0103 prima categorie"] })] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay, { isOpen: showModal, onClose: handleCloseModal, title: editingCategory ? 'Editează categoria' : 'Adaugă categorie nouă', children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("form", { onSubmit: handleSubmit, className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay, { label: "Nume categorie", type: "text", value: formData.name, onChange: (e) => setFormData({ ...formData, name: e.target.value }), placeholder: "Ex: M\u00E2ncare, Transport, etc.", required: true }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Descriere" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("textarea", { value: formData.description, onChange: (e) => setFormData({ ...formData, description: e.target.value }), placeholder: "Descriere op\u021Bional\u0103 pentru categorie", rows: 3, className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 mb-1", children: "Culoare" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "color", value: formData.color, onChange: (e) => setFormData({ ...formData, color: e.target.value }), className: "w-12 h-10 border border-gray-300 rounded-md" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "text", value: formData.color, onChange: (e) => setFormData({ ...formData, color: e.target.value }), className: "flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500", placeholder: "#3B82F6" })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-end space-x-3 pt-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { type: "button", variant: "outline", onClick: handleCloseModal, children: "Anuleaz\u0103" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { type: "submit", variant: "primary", children: editingCategory ? 'Actualizează' : 'Creează' })] })] }) })] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Categories);


/***/ })

}]);