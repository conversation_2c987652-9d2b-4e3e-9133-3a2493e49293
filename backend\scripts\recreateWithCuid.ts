/**
 * Script pentru refacerea completă a bazei de date cu CUID-uri standard
 * 
 * ATENȚIE: Acest script va șterge toate datele existente și va recrea baza de date
 * cu CUID-uri standard și date de test noi.
 * 
 * Rulare: npx ts-node scripts/recreateWithCuid.ts
 */

import { PrismaClient } from '@prisma/client';
import { createId } from '@paralleldrive/cuid2';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

interface RecreationStats {
  usersCreated: number;
  categoriesCreated: number;
  expensesCreated: number;
  subscriptionsCreated: number;
  errors: string[];
}

/**
 * Șterge toate datele existente din baza de date
 */
async function clearDatabase(): Promise<void> {
  console.log('🗑️  Ștergerea datelor existente...');
  
  try {
    // Șterge în ordinea corectă pentru a respecta foreign key constraints
    await prisma.expense.deleteMany();
    await prisma.category.deleteMany();
    await prisma.subscription.deleteMany();
    await prisma.usageLog.deleteMany();
    await prisma.webhookEvent.deleteMany();
    await prisma.user.deleteMany();
    await prisma.plan.deleteMany();
    
    console.log('✅ Toate datele au fost șterse cu succes');
  } catch (error) {
    console.error('❌ Eroare la ștergerea datelor:', error);
    throw error;
  }
}

/**
 * Creează planurile de abonament
 */
async function createPlans(): Promise<void> {
  console.log('📋 Crearea planurilor de abonament...');
  
  const plans = [
    {
      id: createId(),
      stripeId: 'price_free',
      name: 'Free',
      description: 'Plan gratuit cu funcționalități de bază',
      price: 0,
      currency: 'USD',
      interval: 'month',
      features: ['50 cheltuieli/lună', 'Categorii de bază', 'Rapoarte simple'],
      limits: { monthlyExpenses: 50, categories: 10 },
      isActive: true,
      sortOrder: 1,
    },
    {
      id: createId(),
      stripeId: 'price_basic_monthly',
      name: 'Basic',
      description: 'Plan de bază pentru utilizatori individuali',
      price: 9.99,
      currency: 'USD',
      interval: 'month',
      features: ['500 cheltuieli/lună', 'Categorii personalizate', 'Rapoarte avansate', 'Export date'],
      limits: { monthlyExpenses: 500, categories: 50 },
      isActive: true,
      sortOrder: 2,
    },
    {
      id: createId(),
      stripeId: 'price_premium_monthly',
      name: 'Premium',
      description: 'Plan premium cu toate funcționalitățile',
      price: 19.99,
      currency: 'USD',
      interval: 'month',
      features: ['Cheltuieli nelimitate', 'Toate funcționalitățile', 'Suport prioritar', 'API access'],
      limits: { monthlyExpenses: -1, categories: -1 },
      isActive: true,
      sortOrder: 3,
    },
  ];
  
  for (const plan of plans) {
    await prisma.plan.create({ data: plan });
    console.log(`   ✅ Plan creat: ${plan.name}`);
  }
}

/**
 * Creează utilizatori de test cu CUID-uri standard
 */
async function createTestUsers(): Promise<string[]> {
  console.log('👤 Crearea utilizatorilor de test...');
  
  const hashedPassword = await bcrypt.hash('password123', 10);
  const userIds: string[] = [];
  
  const users = [
    {
      id: createId(),
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Administrator',
      lastName: 'System',
      role: 'admin' as const,
      currency: 'USD',
      timezone: 'UTC',
      isActive: true,
      emailVerified: true,
      planType: 'premium' as const,
    },
    {
      id: createId(),
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Demo',
      lastName: 'User',
      role: 'user' as const,
      currency: 'USD',
      timezone: 'UTC',
      isActive: true,
      emailVerified: true,
      planType: 'basic' as const,
    },
    {
      id: createId(),
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Test',
      lastName: 'Free',
      role: 'user' as const,
      currency: 'USD',
      timezone: 'UTC',
      isActive: true,
      emailVerified: true,
      planType: 'free' as const,
    },
    {
      id: createId(),
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Test',
      lastName: 'Basic',
      role: 'user' as const,
      currency: 'USD',
      timezone: 'UTC',
      isActive: true,
      emailVerified: true,
      planType: 'basic' as const,
    },
  ];
  
  for (const user of users) {
    const createdUser = await prisma.user.create({ data: user });
    userIds.push(createdUser.id);
    console.log(`   ✅ Utilizator creat: ${user.firstName} ${user.lastName} (${createdUser.id})`);
  }
  
  return userIds;
}

/**
 * Creează categorii de test pentru fiecare utilizator
 */
async function createTestCategories(userIds: string[]): Promise<string[]> {
  console.log('📂 Crearea categoriilor de test...');
  
  const categoryIds: string[] = [];
  const categoryTemplates = [
    { name: 'Mâncare', description: 'Cheltuieli pentru mâncare și băuturi', color: '#FF6B6B', icon: 'utensils' },
    { name: 'Transport', description: 'Cheltuieli pentru transport', color: '#4ECDC4', icon: 'car' },
    { name: 'Sănătate', description: 'Cheltuieli medicale și sănătate', color: '#45B7D1', icon: 'heart' },
    { name: 'Cumpărături', description: 'Cumpărături generale', color: '#96CEB4', icon: 'shopping-bag' },
    { name: 'Utilități', description: 'Facturi și utilități', color: '#FFEAA7', icon: 'home' },
    { name: 'Divertisment', description: 'Activități de divertisment', color: '#DDA0DD', icon: 'film' },
  ];
  
  for (const userId of userIds) {
    for (let i = 0; i < categoryTemplates.length; i++) {
      const template = categoryTemplates[i];
      const category = {
        id: createId(),
        name: template.name,
        description: template.description,
        color: template.color,
        icon: template.icon,
        userId,
        sortOrder: i + 1,
        isActive: true,
        isDefault: i === 0, // Prima categorie este default
      };
      
      const createdCategory = await prisma.category.create({ data: category });
      categoryIds.push(createdCategory.id);
      console.log(`   ✅ Categorie creată: ${template.name} pentru utilizatorul ${userId}`);
    }
  }
  
  return categoryIds;
}

/**
 * Creează cheltuieli de test
 */
async function createTestExpenses(userIds: string[], categoryIds: string[]): Promise<void> {
  console.log('💰 Crearea cheltuielilor de test...');
  
  const expenseTemplates = [
    { description: 'Prânz la restaurant', amount: 25.50, paymentMethod: 'card' as const },
    { description: 'Bilet autobuz', amount: 2.50, paymentMethod: 'cash' as const },
    { description: 'Medicamente', amount: 45.00, paymentMethod: 'card' as const },
    { description: 'Cumpărături supermarket', amount: 120.75, paymentMethod: 'card' as const },
    { description: 'Factura electricitate', amount: 85.30, paymentMethod: 'bank_transfer' as const },
    { description: 'Cinema', amount: 15.00, paymentMethod: 'digital_wallet' as const },
  ];
  
  for (const userId of userIds) {
    // Găsește categoriile pentru acest utilizator
    const userCategories = categoryIds.filter((_, index) => {
      const userIndex = userIds.indexOf(userId);
      const categoriesPerUser = 6;
      return index >= userIndex * categoriesPerUser && index < (userIndex + 1) * categoriesPerUser;
    });
    
    for (let i = 0; i < expenseTemplates.length; i++) {
      const template = expenseTemplates[i];
      const expense = {
        id: createId(),
        description: template.description,
        amount: template.amount,
        date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Ultimele 30 de zile
        paymentMethod: template.paymentMethod,
        userId,
        categoryId: userCategories[i] || userCategories[0],
        tags: [],
        isRecurring: false,
      };
      
      await prisma.expense.create({ data: expense });
      console.log(`   ✅ Cheltuială creată: ${template.description} pentru utilizatorul ${userId}`);
    }
  }
}

/**
 * Verifică că toate ID-urile sunt în format CUID2 standard
 */
async function verifyCuidFormat(): Promise<boolean> {
  console.log('🔍 Verificarea formatului CUID2...');
  
  const cuidRegex = /^c[a-z0-9]{24}$/;
  
  // Verifică utilizatorii
  const users = await prisma.user.findMany({ select: { id: true } });
  const invalidUserIds = users.filter(u => !cuidRegex.test(u.id));
  
  // Verifică categoriile
  const categories = await prisma.category.findMany({ select: { id: true } });
  const invalidCategoryIds = categories.filter(c => !cuidRegex.test(c.id));
  
  // Verifică cheltuielile
  const expenses = await prisma.expense.findMany({ select: { id: true } });
  const invalidExpenseIds = expenses.filter(e => !cuidRegex.test(e.id));
  
  console.log(`📊 Rezultate verificare CUID2:`);
  console.log(`   - Utilizatori: ${users.length - invalidUserIds.length}/${users.length} valizi`);
  console.log(`   - Categorii: ${categories.length - invalidCategoryIds.length}/${categories.length} valizi`);
  console.log(`   - Cheltuieli: ${expenses.length - invalidExpenseIds.length}/${expenses.length} valizi`);
  
  const allValid = invalidUserIds.length === 0 && invalidCategoryIds.length === 0 && invalidExpenseIds.length === 0;
  
  if (allValid) {
    console.log('✅ Toate ID-urile sunt în format CUID2 standard!');
  } else {
    console.log('❌ Unele ID-uri nu sunt în format CUID2 standard!');
    if (invalidUserIds.length > 0) console.log(`   ID-uri utilizatori invalizi: ${invalidUserIds.map(u => u.id).join(', ')}`);
    if (invalidCategoryIds.length > 0) console.log(`   ID-uri categorii invalizi: ${invalidCategoryIds.map(c => c.id).join(', ')}`);
    if (invalidExpenseIds.length > 0) console.log(`   ID-uri cheltuieli invalizi: ${invalidExpenseIds.map(e => e.id).join(', ')}`);
  }
  
  return allValid;
}

/**
 * Funcția principală pentru refacerea bazei de date
 */
async function main(): Promise<RecreationStats> {
  console.log('🚀 Începerea refacerii complete a bazei de date cu CUID-uri standard...\n');
  
  const stats: RecreationStats = {
    usersCreated: 0,
    categoriesCreated: 0,
    expensesCreated: 0,
    subscriptionsCreated: 0,
    errors: [],
  };
  
  try {
    // Pasul 1: Șterge toate datele existente
    await clearDatabase();
    
    // Pasul 2: Creează planurile
    await createPlans();
    
    // Pasul 3: Creează utilizatorii de test
    const userIds = await createTestUsers();
    stats.usersCreated = userIds.length;
    
    // Pasul 4: Creează categoriile de test
    const categoryIds = await createTestCategories(userIds);
    stats.categoriesCreated = categoryIds.length;
    
    // Pasul 5: Creează cheltuielile de test
    await createTestExpenses(userIds, categoryIds);
    const expenseCount = await prisma.expense.count();
    stats.expensesCreated = expenseCount;
    
    // Pasul 6: Verifică formatul CUID2
    const isValidFormat = await verifyCuidFormat();
    
    if (isValidFormat) {
      console.log('\n🎉 Refacerea bazei de date s-a finalizat cu succes!');
      console.log(`📊 Statistici finale:`);
      console.log(`   - Utilizatori creați: ${stats.usersCreated}`);
      console.log(`   - Categorii create: ${stats.categoriesCreated}`);
      console.log(`   - Cheltuieli create: ${stats.expensesCreated}`);
      console.log(`   - Toate ID-urile sunt în format CUID2 standard ✅`);
    } else {
      throw new Error('ID-urile generate nu sunt în format CUID2 standard');
    }
    
  } catch (error) {
    console.error('\n❌ Eroare critică în timpul refacerii:', error);
    stats.errors.push(`Eroare critică: ${error}`);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
  
  return stats;
}

// Rulează scriptul dacă este apelat direct
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Eroare neașteptată:', error);
    process.exit(1);
  });
}

export { main as recreateWithCuid };
