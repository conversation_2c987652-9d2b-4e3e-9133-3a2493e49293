"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[911],{

/***/ 2911:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2389);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4976);
/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6103);
/* harmony import */ var _components_ui_Dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(8724);
/* harmony import */ var _hooks_useLanguage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(1307);
/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(250);









const Landing = () => {
    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__/* .useTranslation */ .Bd)();
    const { currentLanguage, setLanguage } = (0,_hooks_useLanguage__WEBPACK_IMPORTED_MODULE_7__/* .useLanguage */ .o)();
    const { user, isAuthenticated, logout } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_8__/* .useAuthStore */ .nc)();
    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);
    const userMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
    // Închide meniul utilizatorului când se face click în afara acestuia
    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {
        const handleClickOutside = (event) => {
            if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
                setShowUserMenu(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    // Opțiuni pentru selectorul de limbă
    const languageOptions = [
        { value: 'ro', label: 'Română' },
        { value: 'en', label: 'English' },
    ];
    const features = [
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChartBarIcon */ .r95,
            title: t('landing.features.items.analytics.title'),
            description: t('landing.features.items.analytics.description'),
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CurrencyDollarIcon */ .xmO,
            title: t('landing.features.items.categories.title'),
            description: t('landing.features.items.categories.description'),
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ShieldCheckIcon */ .Zus,
            title: t('landing.features.items.security.title'),
            description: t('landing.features.items.security.description'),
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .DevicePhoneMobileIcon */ .qXP,
            title: t('landing.features.items.mobile.title'),
            description: t('landing.features.items.mobile.description'),
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ClockIcon */ .O4,
            title: t('landing.features.items.realtime.title'),
            description: t('landing.features.items.realtime.description'),
        },
        {
            icon: _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UserGroupIcon */ .K6s,
            title: t('landing.features.items.sharing.title'),
            description: t('landing.features.items.sharing.description'),
        },
    ];
    const testimonials = [
        {
            name: t('landing.testimonials.items.maria.name'),
            role: t('landing.testimonials.items.maria.role'),
            content: t('landing.testimonials.items.maria.content'),
            rating: 5,
        },
        {
            name: t('landing.testimonials.items.alexandru.name'),
            role: t('landing.testimonials.items.alexandru.role'),
            content: t('landing.testimonials.items.alexandru.content'),
            rating: 5,
        },
        {
            name: t('landing.testimonials.items.elena.name'),
            role: t('landing.testimonials.items.elena.role'),
            content: t('landing.testimonials.items.elena.content'),
            rating: 5,
        },
    ];
    const pricingPlans = [
        {
            name: t('landing.pricing.plans.free.name'),
            price: '0',
            period: t('landing.pricing.plans.free.period'),
            features: [
                t('landing.pricing.plans.free.features.transactions'),
                t('landing.pricing.plans.free.features.categories'),
                t('landing.pricing.plans.free.features.reports'),
                t('landing.pricing.plans.free.features.support'),
            ],
            popular: false,
        },
        {
            name: t('landing.pricing.plans.pro.name'),
            price: '29',
            period: t('landing.pricing.plans.pro.period'),
            features: [
                t('landing.pricing.plans.pro.features.transactions'),
                t('landing.pricing.plans.pro.features.categories'),
                t('landing.pricing.plans.pro.features.reports'),
                t('landing.pricing.plans.pro.features.export'),
                t('landing.pricing.plans.pro.features.support'),
                t('landing.pricing.plans.pro.features.backup'),
            ],
            popular: true,
        },
        {
            name: t('landing.pricing.plans.business.name'),
            price: '99',
            period: t('landing.pricing.plans.business.period'),
            features: [
                t('landing.pricing.plans.business.features.allPro'),
                t('landing.pricing.plans.business.features.multipleAccounts'),
                t('landing.pricing.plans.business.features.apiAccess'),
                t('landing.pricing.plans.business.features.integrations'),
                t('landing.pricing.plans.business.features.manager'),
                t('landing.pricing.plans.business.features.sla'),
            ],
            popular: false,
        },
    ];
    const stats = [
        {
            number: t('landing.stats.activeUsers.number'),
            label: t('landing.stats.activeUsers.label'),
        },
        {
            number: t('landing.stats.expensesTracked.number'),
            label: t('landing.stats.expensesTracked.label'),
        },
        {
            number: t('landing.stats.averageSavings.number'),
            label: t('landing.stats.averageSavings.label'),
        },
        {
            number: t('landing.stats.uptime.number'),
            label: t('landing.stats.uptime.label'),
        },
    ];
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "min-h-screen bg-white", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("header", { className: "bg-white shadow-sm sticky top-0 z-50", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-between items-center py-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CurrencyDollarIcon */ .xmO, { className: "w-5 h-5 text-white" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent", children: "FinanceFlow" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("nav", { className: "hidden md:flex space-x-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "#features", className: "text-gray-600 hover:text-blue-600 transition-colors", children: t('landing.header.features') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "#testimonials", className: "text-gray-600 hover:text-blue-600 transition-colors", children: t('landing.header.testimonials') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "#pricing", className: "text-gray-600 hover:text-blue-600 transition-colors", children: t('landing.header.pricing') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Dropdown__WEBPACK_IMPORTED_MODULE_6__/* .Select */ .l6, { value: languageOptions.find(opt => opt.value === currentLanguage), onChange: (option) => setLanguage(option.value), options: languageOptions, className: "w-32", buttonClassName: "bg-white hover:bg-gray-50 border-gray-300 hover:border-blue-400 rounded-t-lg px-4 py-2.5 text-sm font-medium text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm hover:shadow-md", optionClassName: "hover:bg-blue-50 font-medium", menuClassName: "border-t-0 rounded-t-none rounded-b-lg border-gray-300 shadow-lg", offset: 0 }), isAuthenticated ? (
                                    // Meniu pentru utilizatori autentificați
                                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "relative", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { className: "p-2 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .BellIcon */ .XFE, { className: "w-5 h-5" }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative", ref: userMenuRef, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { onClick: () => setShowUserMenu(!showUserMenu), className: "flex items-center space-x-2 p-2 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UserIcon */ .nys, { className: "w-4 h-4 text-white" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium", children: user?.firstName }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChevronDownIcon */ .D3D, { className: "w-4 h-4" })] }), showUserMenu && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "px-4 py-3 border-b border-gray-100", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-sm font-medium text-gray-900", children: [user?.firstName, " ", user?.lastName] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500", children: user?.email })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/app/dashboard", className: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors", onClick: () => setShowUserMenu(false), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ChartBarIcon */ .r95, { className: "w-4 h-4 mr-3" }), "Dashboard"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/app/profile", className: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors", onClick: () => setShowUserMenu(false), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .UserIcon */ .nys, { className: "w-4 h-4 mr-3" }), "Profil"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/app/settings", className: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors", onClick: () => setShowUserMenu(false), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .Cog6ToothIcon */ .Vy, { className: "w-4 h-4 mr-3" }), "Set\u0103ri"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "border-t border-gray-100 mt-2 pt-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { onClick: () => {
                                                                        logout();
                                                                        setShowUserMenu(false);
                                                                    }, className: "flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowRightOnRectangleIcon */ .RzF, { className: "w-4 h-4 mr-3" }), "Deconectare"] }) })] }))] })] })) : (
                                    // Meniu pentru utilizatori neautentificați
                                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/login", className: "text-gray-600 hover:text-blue-600 transition-colors", children: t('landing.header.login') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/register", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "primary", size: "sm", children: t('landing.header.register') }) })] }))] })] }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { className: "relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 pt-20 pb-32 overflow-hidden", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "absolute inset-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute top-20 left-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute top-40 right-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute bottom-20 left-1/2 w-72 h-72 bg-indigo-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "animate-fade-in-up", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("h1", { className: "text-5xl md:text-6xl font-bold mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent", children: t('landing.hero.title.part1') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "block bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent", children: t('landing.hero.title.part2') })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "animate-fade-in-up animation-delay-200", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed", children: t('landing.hero.subtitle') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "animate-fade-in-up animation-delay-400 flex flex-row gap-4 justify-center flex-wrap", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/register", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "primary", size: "lg", className: "w-auto whitespace-nowrap transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl", children: t('landing.hero.cta.primary') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "outline", size: "lg", className: "w-auto whitespace-nowrap transform hover:scale-105 transition-all duration-200 hover:shadow-lg", children: t('landing.hero.cta.secondary') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-500 mt-4", children: t('landing.hero.features') })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { id: "features", className: "py-24 bg-white relative overflow-hidden", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute inset-0 bg-gradient-to-b from-gray-50/50 to-white" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center mb-16", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-4xl font-bold text-gray-900 mb-4", children: t('landing.features.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xl text-gray-600 max-w-2xl mx-auto", children: t('landing.features.subtitle') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8", children: features.map((feature, index) => {
                                    const IconComponent = feature.icon;
                                    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "group p-6 rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 bg-white/80 backdrop-blur-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconComponent, { className: "w-6 h-6 text-white" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300", children: feature.title }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600 leading-relaxed", children: feature.description })] }, index));
                                }) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { className: "bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 py-20 relative overflow-hidden", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute inset-0 bg-black/10" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "absolute inset-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute top-0 left-1/4 w-96 h-96 bg-white/10 rounded-full filter blur-3xl" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute bottom-0 right-1/4 w-96 h-96 bg-white/10 rounded-full filter blur-3xl" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-8 text-center", children: stats.map((stat, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "group", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-5xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300", children: stat.number }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-blue-100 text-lg font-medium", children: stat.label })] }, index))) }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("section", { id: "testimonials", className: "py-24 bg-gradient-to-b from-gray-50 to-white", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center mb-16", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-3xl md:text-4xl font-bold text-gray-900 mb-4", children: t('landing.testimonials.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xl text-gray-600", children: t('landing.testimonials.subtitle') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8", children: testimonials.map((testimonial, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "group bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1 border border-gray-100", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center mb-6", children: [...Array(testimonial.rating)].map((_, i) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .StarIcon */ .Gg5, { className: "w-5 h-5 text-yellow-400 fill-current group-hover:scale-110 transition-transform duration-300", style: { animationDelay: `${i * 100}ms` } }, i))) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-gray-700 mb-6 italic text-lg leading-relaxed", children: ["\"", testimonial.content, "\""] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-white font-bold text-lg", children: testimonial.name.charAt(0) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "font-semibold text-gray-900", children: testimonial.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-gray-500", children: testimonial.role })] })] })] }, index))) })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { id: "pricing", className: "py-24 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "absolute inset-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute top-20 right-20 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute bottom-20 left-20 w-64 h-64 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-2000" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center mb-16", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-3xl md:text-4xl font-bold text-gray-900 mb-4", children: t('landing.pricing.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xl text-gray-600", children: t('landing.pricing.subtitle') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8", children: pricingPlans.map((plan, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: `group relative p-8 rounded-2xl border-2 transition-all duration-500 hover:-translate-y-2 ${plan.popular
                                        ? 'border-blue-500 shadow-2xl scale-105 bg-gradient-to-b from-blue-50 to-white'
                                        : 'border-gray-200 bg-white hover:border-blue-300 hover:shadow-xl'}`, children: [plan.popular && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute -top-4 left-1/2 transform -translate-x-1/2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-medium shadow-lg", children: t('landing.pricing.popular') }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-2xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300", children: plan.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent", children: plan.price }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "text-gray-600 ml-2 text-lg", children: [t('landing.pricing.currency'), " ", plan.period] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("ul", { className: "space-y-4 mb-8", children: plan.features.map((feature, featureIndex) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("li", { className: "flex items-center group-hover:translate-x-1 transition-transform duration-300", style: { animationDelay: `${featureIndex * 100}ms` }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CheckIcon */ .Srz, { className: "w-5 h-5 text-green-500 mr-3 flex-shrink-0" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-gray-600", children: feature })] }, featureIndex))) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/register", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: plan.popular ? 'primary' : 'outline', size: "lg", className: "w-full transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl", children: plan.price === '0' ? t('landing.pricing.buttons.free') : t('landing.pricing.buttons.choose') }) })] }, index))) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { className: "py-24 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 relative overflow-hidden", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "absolute inset-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute top-10 left-10 w-72 h-72 bg-white/10 rounded-full filter blur-3xl animate-pulse" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute bottom-10 right-10 w-72 h-72 bg-white/10 rounded-full filter blur-3xl animate-pulse animation-delay-2000" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full filter blur-3xl" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-4xl md:text-5xl font-bold text-white mb-6 leading-tight", children: t('landing.cta.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg md:text-xl text-blue-100 mb-10 leading-relaxed max-w-2xl mx-auto", children: t('landing.cta.subtitle') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex justify-center mb-8", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/register", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay, { variant: "white", size: "lg", className: "whitespace-nowrap transform hover:scale-110 transition-all duration-300 shadow-2xl hover:shadow-3xl", children: t('landing.cta.button') }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-blue-100 text-base md:text-lg font-medium", children: t('landing.cta.features') })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("footer", { className: "bg-gray-900 text-white py-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .CurrencyDollarIcon */ .xmO, { className: "w-5 h-5 text-white" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-xl font-bold", children: "FinanceFlow" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-400", children: t('landing.footer.description') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-semibold mb-4", children: t('landing.footer.product.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "space-y-2 text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/features", className: "hover:text-white transition-colors", children: t('landing.footer.product.features') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/pricing", className: "hover:text-white transition-colors", children: t('landing.footer.product.pricing') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "#", className: "hover:text-white transition-colors", children: t('landing.footer.product.api') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "#", className: "hover:text-white transition-colors", children: t('landing.footer.product.integrations') }) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-semibold mb-4", children: t('landing.footer.support.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "space-y-2 text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/documentation", className: "hover:text-white transition-colors", children: t('landing.footer.support.documentation') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/help", className: "hover:text-white transition-colors", children: t('landing.footer.support.guides') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/contact", className: "hover:text-white transition-colors", children: t('landing.footer.support.contact') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "#", className: "hover:text-white transition-colors", children: t('landing.footer.support.status') }) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-semibold mb-4", children: t('landing.footer.legal.title') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "space-y-2 text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/terms", className: "hover:text-white transition-colors", children: t('landing.footer.legal.terms') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/privacy", className: "hover:text-white transition-colors", children: t('landing.footer.legal.privacy') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/cookies", className: "hover:text-white transition-colors", children: t('landing.footer.legal.cookies') }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "#", className: "hover:text-white transition-colors", children: t('landing.footer.legal.gdpr') }) })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "border-t border-gray-800 mt-8 pt-8 text-center text-gray-400", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { children: t('landing.footer.copyright') }) })] }) })] }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Landing);


/***/ })

}]);