"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[424],{

/***/ 6424:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4848);
/* harmony import */ var _heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3740);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6540);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2389);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4976);
/* harmony import */ var _components_layout_PublicLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(4370);






const Privacy = () => {
    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__/* .useTranslation */ .Bd)();
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_layout_PublicLayout__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "min-h-screen bg-gray-50", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-white shadow-sm", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_4__/* .Link */ .N_, { to: "/", className: "flex items-center text-gray-600 hover:text-gray-900 transition-colors", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ArrowLeftIcon */ .A60, { className: "w-5 h-5 mr-2" }), t('common.back', 'Înapoi')] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "h-6 w-px bg-gray-300" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1", { className: "text-2xl font-bold text-gray-900", children: t('legal.privacy.title', 'Politica de Confidențialitate') })] }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "bg-white rounded-lg shadow-sm p-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-6 mb-12", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center p-6 bg-blue-50 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .ShieldCheckIcon */ .Zus, { className: "w-12 h-12 text-blue-600 mx-auto mb-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-semibold text-gray-900 mb-2", children: t('legal.privacy.features.encryption.title', 'Criptare Avansată') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-600", children: t('legal.privacy.features.encryption.description', 'Toate datele sunt criptate cu AES-256') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center p-6 bg-green-50 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .LockClosedIcon */ .JD_, { className: "w-12 h-12 text-green-600 mx-auto mb-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-semibold text-gray-900 mb-2", children: t('legal.privacy.features.secure.title', 'Stocare Securizată') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-600", children: t('legal.privacy.features.secure.description', 'Servere protejate și backup-uri regulate') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center p-6 bg-purple-50 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__/* .EyeSlashIcon */ .G3N, { className: "w-12 h-12 text-purple-600 mx-auto mb-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-semibold text-gray-900 mb-2", children: t('legal.privacy.features.private.title', 'Confidențialitate Totală') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-600", children: t('legal.privacy.features.private.description', 'Nu partajăm datele cu terțe părți') })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "prose prose-lg max-w-none", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600 mb-8", children: t('legal.privacy.lastUpdated', 'Ultima actualizare: 1 ianuarie 2024') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { className: "mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 mb-4", children: t('legal.privacy.intro.title', '1. Introducere') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-700 leading-relaxed", children: t('legal.privacy.intro.content', 'La FinanceFlow, protecția datelor dvs. personale este o prioritate absolută. Această Politică de Confidențialitate explică cum colectăm, utilizăm, protejăm și partajăm informațiile dvs. personale.') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { className: "mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 mb-4", children: t('legal.privacy.collection.title', '2. Informațiile pe care le Colectăm') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-gray-900 mb-3", children: t('legal.privacy.collection.personal.title', 'Informații Personale:') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "list-disc list-inside text-gray-700 space-y-2 mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.collection.personal.name', 'Nume și adresă de email') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.collection.personal.financial', 'Informații financiare (cheltuieli, venituri, categorii)') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.collection.personal.preferences', 'Preferințe și setări ale aplicației') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-gray-900 mb-3", children: t('legal.privacy.collection.technical.title', 'Informații Tehnice:') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "list-disc list-inside text-gray-700 space-y-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.collection.technical.device', 'Informații despre dispozitiv și browser') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.collection.technical.usage', 'Date de utilizare și performanță') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.collection.technical.logs', 'Log-uri de securitate și erori') })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { className: "mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 mb-4", children: t('legal.privacy.usage.title', '3. Cum Utilizăm Informațiile') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "list-disc list-inside text-gray-700 space-y-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.usage.service', 'Pentru a furniza și îmbunătăți serviciile noastre') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.usage.personalization', 'Pentru a personaliza experiența dvs.') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.usage.communication', 'Pentru comunicare și suport tehnic') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.usage.security', 'Pentru securitate și prevenirea fraudelor') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.usage.analytics', 'Pentru analize și îmbunătățiri ale produsului') })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { className: "mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 mb-4", children: t('legal.privacy.protection.title', '4. Protecția Datelor') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-700 leading-relaxed mb-4", children: t('legal.privacy.protection.intro', 'Implementăm măsuri de securitate avansate pentru a proteja datele dvs.:') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "list-disc list-inside text-gray-700 space-y-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.protection.encryption', 'Criptare AES-256 pentru toate datele sensibile') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.protection.https', 'Conexiuni HTTPS securizate') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.protection.access', 'Control strict al accesului la date') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.protection.monitoring', 'Monitorizare continuă pentru amenințări') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.protection.backup', 'Backup-uri regulate și securizate') })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { className: "mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 mb-4", children: t('legal.privacy.sharing.title', '5. Partajarea Informațiilor') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-700 leading-relaxed mb-4", children: t('legal.privacy.sharing.policy', 'Nu vindem, închiriem sau partajăm informațiile dvs. personale cu terțe părți, cu excepția următoarelor situații:') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "list-disc list-inside text-gray-700 space-y-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.sharing.consent', 'Cu consimțământul dvs. explicit') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.sharing.legal', 'Când este cerut de lege') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.sharing.service', 'Cu furnizori de servicii de încredere (doar pentru funcționarea serviciului)') })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { className: "mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 mb-4", children: t('legal.privacy.rights.title', '6. Drepturile Dvs.') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-700 leading-relaxed mb-4", children: t('legal.privacy.rights.intro', 'Aveți următoarele drepturi în ceea ce privește datele dvs. personale:') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", { className: "list-disc list-inside text-gray-700 space-y-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.rights.access', 'Dreptul de acces la datele dvs.') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.rights.rectification', 'Dreptul de rectificare a datelor inexacte') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.rights.erasure', 'Dreptul la ștergerea datelor') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.rights.portability', 'Dreptul la portabilitatea datelor') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li", { children: t('legal.privacy.rights.objection', 'Dreptul de opoziție la prelucrare') })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { className: "mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 mb-4", children: t('legal.privacy.cookies.title', '7. Cookies și Tehnologii Similare') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-700 leading-relaxed", children: t('legal.privacy.cookies.content', 'Utilizăm cookies și tehnologii similare pentru a îmbunătăți funcționalitatea aplicației, a analiza utilizarea și a personaliza experiența. Puteți controla setările cookies prin browserul dvs.') })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("section", { className: "mb-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 mb-4", children: t('legal.privacy.contact.title', '8. Contact') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-gray-700 leading-relaxed", children: [t('legal.privacy.contact.content', 'Pentru întrebări despre această Politică de Confidențialitate sau pentru a vă exercita drepturile, contactați-ne la: '), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a", { href: "mailto:<EMAIL>", className: "text-blue-600 hover:text-blue-800", children: "<EMAIL>" })] })] })] })] }) })] }) }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Privacy);


/***/ })

}]);