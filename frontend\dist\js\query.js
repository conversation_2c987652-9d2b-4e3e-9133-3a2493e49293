"use strict";
(self["webpackChunkexpense_tracker_frontend"] = self["webpackChunkexpense_tracker_frontend"] || []).push([[137],{

/***/ 8035:
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  E1: () => (/* reexport */ modern/* QueryClient */.E1),
  Ht: () => (/* reexport */ QueryClientProvider),
  tG: () => (/* reexport */ modern/* onlineManager */.tG),
  qu: () => (/* reexport */ useInfiniteQuery),
  n_: () => (/* reexport */ useMutation),
  pw: () => (/* reexport */ useQuery),
  jE: () => (/* reexport */ QueryClientProvider_useQueryClient)
});

// UNUSED EXPORTS: CancelledError, HydrationBoundary, InfiniteQueryObserver, IsRestoringProvider, Mutation, MutationCache, MutationObserver, QueriesObserver, Query, QueryCache, QueryClientContext, QueryErrorResetBoundary, QueryObserver, dataTagErrorSymbol, dataTagSymbol, defaultScheduler, defaultShouldDehydrateMutation, defaultShouldDehydrateQuery, dehydrate, experimental_streamedQuery, focusManager, hashKey, hydrate, infiniteQueryOptions, isCancelledError, isServer, keepPreviousData, matchMutation, matchQuery, noop, notifyManager, partialMatchKey, queryOptions, replaceEqualDeep, shouldThrowError, skipToken, unsetMarker, useIsFetching, useIsMutating, useIsRestoring, useMutationState, usePrefetchInfiniteQuery, usePrefetchQuery, useQueries, useQueryErrorResetBoundary, useSuspenseInfiniteQuery, useSuspenseQueries, useSuspenseQuery

// EXTERNAL MODULE: ./node_modules/@tanstack/query-core/build/modern/index.js + 21 modules
var modern = __webpack_require__(9956);
;// ./node_modules/@tanstack/react-query/build/modern/types.js
//# sourceMappingURL=types.js.map
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(6540);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(4848);
;// ./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js
"use client";

// src/QueryClientProvider.tsx


var QueryClientContext = react.createContext(
  void 0
);
var QueryClientProvider_useQueryClient = (queryClient) => {
  const client = react.useContext(QueryClientContext);
  if (queryClient) {
    return queryClient;
  }
  if (!client) {
    throw new Error("No QueryClient set, use QueryClientProvider to set one");
  }
  return client;
};
var QueryClientProvider = ({
  client,
  children
}) => {
  react.useEffect(() => {
    client.mount();
    return () => {
      client.unmount();
    };
  }, [client]);
  return /* @__PURE__ */ (0,jsx_runtime.jsx)(QueryClientContext.Provider, { value: client, children });
};

//# sourceMappingURL=QueryClientProvider.js.map
;// ./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js
"use client";

// src/IsRestoringProvider.ts

var IsRestoringContext = react.createContext(false);
var IsRestoringProvider_useIsRestoring = () => react.useContext(IsRestoringContext);
var IsRestoringProvider = IsRestoringContext.Provider;

//# sourceMappingURL=IsRestoringProvider.js.map
;// ./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js
"use client";

// src/QueryErrorResetBoundary.tsx


function createValue() {
  let isReset = false;
  return {
    clearReset: () => {
      isReset = false;
    },
    reset: () => {
      isReset = true;
    },
    isReset: () => {
      return isReset;
    }
  };
}
var QueryErrorResetBoundaryContext = react.createContext(createValue());
var QueryErrorResetBoundary_useQueryErrorResetBoundary = () => react.useContext(QueryErrorResetBoundaryContext);
var QueryErrorResetBoundary = ({
  children
}) => {
  const [value] = React.useState(() => createValue());
  return /* @__PURE__ */ jsx(QueryErrorResetBoundaryContext.Provider, { value, children: typeof children === "function" ? children(value) : children });
};

//# sourceMappingURL=QueryErrorResetBoundary.js.map
;// ./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js
"use client";

// src/errorBoundaryUtils.ts


var errorBoundaryUtils_ensurePreventErrorBoundaryRetry = (options, errorResetBoundary) => {
  if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {
    if (!errorResetBoundary.isReset()) {
      options.retryOnMount = false;
    }
  }
};
var errorBoundaryUtils_useClearResetErrorBoundary = (errorResetBoundary) => {
  react.useEffect(() => {
    errorResetBoundary.clearReset();
  }, [errorResetBoundary]);
};
var errorBoundaryUtils_getHasError = ({
  result,
  errorResetBoundary,
  throwOnError,
  query,
  suspense
}) => {
  return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || (0,modern/* shouldThrowError */.GU)(throwOnError, [result.error, query]));
};

//# sourceMappingURL=errorBoundaryUtils.js.map
;// ./node_modules/@tanstack/react-query/build/modern/suspense.js
// src/suspense.ts
var suspense_defaultThrowOnError = (_error, query) => query.state.data === void 0;
var suspense_ensureSuspenseTimers = (defaultedOptions) => {
  if (defaultedOptions.suspense) {
    const clamp = (value) => value === "static" ? value : Math.max(value ?? 1e3, 1e3);
    const originalStaleTime = defaultedOptions.staleTime;
    defaultedOptions.staleTime = typeof originalStaleTime === "function" ? (...args) => clamp(originalStaleTime(...args)) : clamp(originalStaleTime);
    if (typeof defaultedOptions.gcTime === "number") {
      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1e3);
    }
  }
};
var suspense_willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;
var suspense_shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;
var suspense_fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {
  errorResetBoundary.clearReset();
});

//# sourceMappingURL=suspense.js.map
;// ./node_modules/@tanstack/react-query/build/modern/useQueries.js
"use client";

// src/useQueries.ts







function useQueries_useQueries({
  queries,
  ...options
}, queryClient) {
  const client = useQueryClient(queryClient);
  const isRestoring = useIsRestoring();
  const errorResetBoundary = useQueryErrorResetBoundary();
  const defaultedQueries = React.useMemo(
    () => queries.map((opts) => {
      const defaultedOptions = client.defaultQueryOptions(
        opts
      );
      defaultedOptions._optimisticResults = isRestoring ? "isRestoring" : "optimistic";
      return defaultedOptions;
    }),
    [queries, client, isRestoring]
  );
  defaultedQueries.forEach((query) => {
    ensureSuspenseTimers(query);
    ensurePreventErrorBoundaryRetry(query, errorResetBoundary);
  });
  useClearResetErrorBoundary(errorResetBoundary);
  const [observer] = React.useState(
    () => new QueriesObserver(
      client,
      defaultedQueries,
      options
    )
  );
  const [optimisticResult, getCombinedResult, trackResult] = observer.getOptimisticResult(
    defaultedQueries,
    options.combine
  );
  const shouldSubscribe = !isRestoring && options.subscribed !== false;
  React.useSyncExternalStore(
    React.useCallback(
      (onStoreChange) => shouldSubscribe ? observer.subscribe(notifyManager.batchCalls(onStoreChange)) : noop,
      [observer, shouldSubscribe]
    ),
    () => observer.getCurrentResult(),
    () => observer.getCurrentResult()
  );
  React.useEffect(() => {
    observer.setQueries(
      defaultedQueries,
      options
    );
  }, [defaultedQueries, options, observer]);
  const shouldAtLeastOneSuspend = optimisticResult.some(
    (result, index) => shouldSuspend(defaultedQueries[index], result)
  );
  const suspensePromises = shouldAtLeastOneSuspend ? optimisticResult.flatMap((result, index) => {
    const opts = defaultedQueries[index];
    if (opts) {
      const queryObserver = new QueryObserver(client, opts);
      if (shouldSuspend(opts, result)) {
        return fetchOptimistic(opts, queryObserver, errorResetBoundary);
      } else if (willFetch(result, isRestoring)) {
        void fetchOptimistic(opts, queryObserver, errorResetBoundary);
      }
    }
    return [];
  }) : [];
  if (suspensePromises.length > 0) {
    throw Promise.all(suspensePromises);
  }
  const firstSingleResultWhichShouldThrow = optimisticResult.find(
    (result, index) => {
      const query = defaultedQueries[index];
      return query && getHasError({
        result,
        errorResetBoundary,
        throwOnError: query.throwOnError,
        query: client.getQueryCache().get(query.queryHash),
        suspense: query.suspense
      });
    }
  );
  if (firstSingleResultWhichShouldThrow?.error) {
    throw firstSingleResultWhichShouldThrow.error;
  }
  return getCombinedResult(trackResult());
}

//# sourceMappingURL=useQueries.js.map
;// ./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js
"use client";

// src/useBaseQuery.ts







function useBaseQuery_useBaseQuery(options, Observer, queryClient) {
  if (true) {
    if (typeof options !== "object" || Array.isArray(options)) {
      throw new Error(
        'Bad argument type. Starting with v5, only the "Object" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object'
      );
    }
  }
  const isRestoring = IsRestoringProvider_useIsRestoring();
  const errorResetBoundary = QueryErrorResetBoundary_useQueryErrorResetBoundary();
  const client = QueryClientProvider_useQueryClient(queryClient);
  const defaultedOptions = client.defaultQueryOptions(options);
  client.getDefaultOptions().queries?._experimental_beforeQuery?.(
    defaultedOptions
  );
  if (true) {
    if (!defaultedOptions.queryFn) {
      console.error(
        `[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`
      );
    }
  }
  defaultedOptions._optimisticResults = isRestoring ? "isRestoring" : "optimistic";
  suspense_ensureSuspenseTimers(defaultedOptions);
  errorBoundaryUtils_ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary);
  errorBoundaryUtils_useClearResetErrorBoundary(errorResetBoundary);
  const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);
  const [observer] = react.useState(
    () => new Observer(
      client,
      defaultedOptions
    )
  );
  const result = observer.getOptimisticResult(defaultedOptions);
  const shouldSubscribe = !isRestoring && options.subscribed !== false;
  react.useSyncExternalStore(
    react.useCallback(
      (onStoreChange) => {
        const unsubscribe = shouldSubscribe ? observer.subscribe(modern/* notifyManager */.jG.batchCalls(onStoreChange)) : modern/* noop */.lQ;
        observer.updateResult();
        return unsubscribe;
      },
      [observer, shouldSubscribe]
    ),
    () => observer.getCurrentResult(),
    () => observer.getCurrentResult()
  );
  react.useEffect(() => {
    observer.setOptions(defaultedOptions);
  }, [defaultedOptions, observer]);
  if (suspense_shouldSuspend(defaultedOptions, result)) {
    throw suspense_fetchOptimistic(defaultedOptions, observer, errorResetBoundary);
  }
  if (errorBoundaryUtils_getHasError({
    result,
    errorResetBoundary,
    throwOnError: defaultedOptions.throwOnError,
    query: client.getQueryCache().get(defaultedOptions.queryHash),
    suspense: defaultedOptions.suspense
  })) {
    throw result.error;
  }
  ;
  client.getDefaultOptions().queries?._experimental_afterQuery?.(
    defaultedOptions,
    result
  );
  if (defaultedOptions.experimental_prefetchInRender && !modern/* isServer */.S$ && suspense_willFetch(result, isRestoring)) {
    const promise = isNewCacheEntry ? (
      // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted
      suspense_fetchOptimistic(defaultedOptions, observer, errorResetBoundary)
    ) : (
      // subscribe to the "cache promise" so that we can finalize the currentThenable once data comes in
      client.getQueryCache().get(defaultedOptions.queryHash)?.promise
    );
    promise?.catch(modern/* noop */.lQ).finally(() => {
      observer.updateResult();
    });
  }
  return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;
}

//# sourceMappingURL=useBaseQuery.js.map
;// ./node_modules/@tanstack/react-query/build/modern/useQuery.js
"use client";

// src/useQuery.ts


function useQuery(options, queryClient) {
  return useBaseQuery_useBaseQuery(options, modern/* QueryObserver */.$x, queryClient);
}

//# sourceMappingURL=useQuery.js.map
;// ./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js
"use client";

// src/useSuspenseQuery.ts



function useSuspenseQuery(options, queryClient) {
  if (true) {
    if (options.queryFn === skipToken) {
      console.error("skipToken is not allowed for useSuspenseQuery");
    }
  }
  return useBaseQuery(
    {
      ...options,
      enabled: true,
      suspense: true,
      throwOnError: defaultThrowOnError,
      placeholderData: void 0
    },
    QueryObserver,
    queryClient
  );
}

//# sourceMappingURL=useSuspenseQuery.js.map
;// ./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js
"use client";

// src/useSuspenseInfiniteQuery.ts



function useSuspenseInfiniteQuery(options, queryClient) {
  if (true) {
    if (options.queryFn === skipToken) {
      console.error("skipToken is not allowed for useSuspenseInfiniteQuery");
    }
  }
  return useBaseQuery(
    {
      ...options,
      enabled: true,
      suspense: true,
      throwOnError: defaultThrowOnError
    },
    InfiniteQueryObserver,
    queryClient
  );
}

//# sourceMappingURL=useSuspenseInfiniteQuery.js.map
;// ./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js
"use client";

// src/useSuspenseQueries.ts



function useSuspenseQueries(options, queryClient) {
  return useQueries(
    {
      ...options,
      queries: options.queries.map((query) => {
        if (true) {
          if (query.queryFn === skipToken) {
            console.error("skipToken is not allowed for useSuspenseQueries");
          }
        }
        return {
          ...query,
          suspense: true,
          throwOnError: defaultThrowOnError,
          enabled: true,
          placeholderData: void 0
        };
      })
    },
    queryClient
  );
}

//# sourceMappingURL=useSuspenseQueries.js.map
;// ./node_modules/@tanstack/react-query/build/modern/usePrefetchQuery.js
// src/usePrefetchQuery.tsx

function usePrefetchQuery(options, queryClient) {
  const client = useQueryClient(queryClient);
  if (!client.getQueryState(options.queryKey)) {
    client.prefetchQuery(options);
  }
}

//# sourceMappingURL=usePrefetchQuery.js.map
;// ./node_modules/@tanstack/react-query/build/modern/usePrefetchInfiniteQuery.js
// src/usePrefetchInfiniteQuery.tsx

function usePrefetchInfiniteQuery(options, queryClient) {
  const client = useQueryClient(queryClient);
  if (!client.getQueryState(options.queryKey)) {
    client.prefetchInfiniteQuery(options);
  }
}

//# sourceMappingURL=usePrefetchInfiniteQuery.js.map
;// ./node_modules/@tanstack/react-query/build/modern/queryOptions.js
// src/queryOptions.ts
function queryOptions(options) {
  return options;
}

//# sourceMappingURL=queryOptions.js.map
;// ./node_modules/@tanstack/react-query/build/modern/infiniteQueryOptions.js
// src/infiniteQueryOptions.ts
function infiniteQueryOptions(options) {
  return options;
}

//# sourceMappingURL=infiniteQueryOptions.js.map
;// ./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js
"use client";

// src/HydrationBoundary.tsx



var HydrationBoundary = ({
  children,
  options = {},
  state,
  queryClient
}) => {
  const client = useQueryClient(queryClient);
  const optionsRef = React.useRef(options);
  optionsRef.current = options;
  const hydrationQueue = React.useMemo(() => {
    if (state) {
      if (typeof state !== "object") {
        return;
      }
      const queryCache = client.getQueryCache();
      const queries = state.queries || [];
      const newQueries = [];
      const existingQueries = [];
      for (const dehydratedQuery of queries) {
        const existingQuery = queryCache.get(dehydratedQuery.queryHash);
        if (!existingQuery) {
          newQueries.push(dehydratedQuery);
        } else {
          const hydrationIsNewer = dehydratedQuery.state.dataUpdatedAt > existingQuery.state.dataUpdatedAt || dehydratedQuery.promise && existingQuery.state.status !== "pending" && existingQuery.state.fetchStatus !== "fetching" && dehydratedQuery.dehydratedAt !== void 0 && dehydratedQuery.dehydratedAt > existingQuery.state.dataUpdatedAt;
          if (hydrationIsNewer) {
            existingQueries.push(dehydratedQuery);
          }
        }
      }
      if (newQueries.length > 0) {
        hydrate(client, { queries: newQueries }, optionsRef.current);
      }
      if (existingQueries.length > 0) {
        return existingQueries;
      }
    }
    return void 0;
  }, [client, state]);
  React.useEffect(() => {
    if (hydrationQueue) {
      hydrate(client, { queries: hydrationQueue }, optionsRef.current);
    }
  }, [client, hydrationQueue]);
  return children;
};

//# sourceMappingURL=HydrationBoundary.js.map
;// ./node_modules/@tanstack/react-query/build/modern/useIsFetching.js
"use client";

// src/useIsFetching.ts



function useIsFetching(filters, queryClient) {
  const client = useQueryClient(queryClient);
  const queryCache = client.getQueryCache();
  return React.useSyncExternalStore(
    React.useCallback(
      (onStoreChange) => queryCache.subscribe(notifyManager.batchCalls(onStoreChange)),
      [queryCache]
    ),
    () => client.isFetching(filters),
    () => client.isFetching(filters)
  );
}

//# sourceMappingURL=useIsFetching.js.map
;// ./node_modules/@tanstack/react-query/build/modern/useMutationState.js
"use client";

// src/useMutationState.ts



function useIsMutating(filters, queryClient) {
  const client = useQueryClient(queryClient);
  return useMutationState(
    { filters: { ...filters, status: "pending" } },
    client
  ).length;
}
function getResult(mutationCache, options) {
  return mutationCache.findAll(options.filters).map(
    (mutation) => options.select ? options.select(mutation) : mutation.state
  );
}
function useMutationState(options = {}, queryClient) {
  const mutationCache = useQueryClient(queryClient).getMutationCache();
  const optionsRef = React.useRef(options);
  const result = React.useRef(null);
  if (!result.current) {
    result.current = getResult(mutationCache, options);
  }
  React.useEffect(() => {
    optionsRef.current = options;
  });
  return React.useSyncExternalStore(
    React.useCallback(
      (onStoreChange) => mutationCache.subscribe(() => {
        const nextResult = replaceEqualDeep(
          result.current,
          getResult(mutationCache, optionsRef.current)
        );
        if (result.current !== nextResult) {
          result.current = nextResult;
          notifyManager.schedule(onStoreChange);
        }
      }),
      [mutationCache]
    ),
    () => result.current,
    () => result.current
  );
}

//# sourceMappingURL=useMutationState.js.map
;// ./node_modules/@tanstack/react-query/build/modern/useMutation.js
"use client";

// src/useMutation.ts



function useMutation(options, queryClient) {
  const client = QueryClientProvider_useQueryClient(queryClient);
  const [observer] = react.useState(
    () => new modern/* MutationObserver */._S(
      client,
      options
    )
  );
  react.useEffect(() => {
    observer.setOptions(options);
  }, [observer, options]);
  const result = react.useSyncExternalStore(
    react.useCallback(
      (onStoreChange) => observer.subscribe(modern/* notifyManager */.jG.batchCalls(onStoreChange)),
      [observer]
    ),
    () => observer.getCurrentResult(),
    () => observer.getCurrentResult()
  );
  const mutate = react.useCallback(
    (variables, mutateOptions) => {
      observer.mutate(variables, mutateOptions).catch(modern/* noop */.lQ);
    },
    [observer]
  );
  if (result.error && (0,modern/* shouldThrowError */.GU)(observer.options.throwOnError, [result.error])) {
    throw result.error;
  }
  return { ...result, mutate, mutateAsync: result.mutate };
}

//# sourceMappingURL=useMutation.js.map
;// ./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js
"use client";

// src/useInfiniteQuery.ts


function useInfiniteQuery(options, queryClient) {
  return useBaseQuery_useBaseQuery(
    options,
    modern/* InfiniteQueryObserver */.zT,
    queryClient
  );
}

//# sourceMappingURL=useInfiniteQuery.js.map
;// ./node_modules/@tanstack/react-query/build/modern/index.js
// src/index.ts




















//# sourceMappingURL=index.js.map

/***/ })

}]);