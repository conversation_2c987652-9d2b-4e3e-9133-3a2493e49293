/**
 * Teste pentru middleware-ul de transformare între snake_case și camelCase
 */

import { Request, Response } from 'express';
import {
  transformRequestToSnake,
  transformResponseToCamel,
  transformCaseBidirectional,
  enableDualCaseSupport
} from '../../../src/middleware/caseTransform';

// Mock pentru Express Request și Response
const mockRequest = (body = {}, query = {}, path = '/test') => {
  return {
    body,
    query,
    path,
    method: 'POST'
  } as Request;
};

const mockResponse = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res as Response;
};

const mockNext = jest.fn();

describe('Case Transform Middleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('transformRequestToSnake', () => {
    it('should transform request body from camelCase to snake_case', () => {
      const req = mockRequest({
        userId: 1,
        firstName: 'John',
        isActive: true,
        nestedObject: {
          streetName: 'Main St'
        }
      });

      const middleware = transformRequestToSnake({ logTransformations: false });
      middleware(req, mockResponse(), mockNext);

      expect(req.body).toEqual({
        user_id: 1,
        first_name: 'John',
        is_active: true,
        nested_object: {
          street_name: 'Main St'
        }
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should transform query parameters from camelCase to snake_case', () => {
      const req = mockRequest({}, {
        userId: '1',
        isActive: 'true'
      });

      const middleware = transformRequestToSnake({
        transformQuery: true,
        logTransformations: false
      });
      middleware(req, mockResponse(), mockNext);

      expect(req.query).toEqual({
        user_id: '1',
        is_active: 'true'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should skip transformation for specified paths', () => {
      const req = mockRequest({ userId: 1 }, {}, '/health');
      const originalBody = { ...req.body };

      const middleware = transformRequestToSnake({
        skipPaths: ['/health'],
        logTransformations: false
      });
      middleware(req, mockResponse(), mockNext);

      expect(req.body).toEqual(originalBody);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle errors gracefully', () => {
      const req = mockRequest();
      // Simulăm o eroare prin modificarea metodei Object.entries
      const originalEntries = Object.entries;
      Object.entries = jest.fn().mockImplementation(() => {
        throw new Error('Test error');
      });

      const middleware = transformRequestToSnake({ logTransformations: false });
      middleware(req, mockResponse(), mockNext);

      expect(mockNext).toHaveBeenCalled();
      // The error should be passed to next()
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(Error);

      // Restore original method
      Object.entries = originalEntries;
    });
  });

  describe('transformResponseToCamel', () => {
    it('should transform response from snake_case to camelCase', () => {
      const req = mockRequest();
      const res = mockResponse();
      const originalJson = res.json;

      const middleware = transformResponseToCamel({ logTransformations: false });
      middleware(req, res, mockNext);

      // Simulate a response
      res.json({
        success: true,
        data: {
          user_id: 1,
          first_name: 'John',
          is_active: true,
          nested_object: {
            street_name: 'Main St'
          }
        }
      });

      expect(originalJson).toHaveBeenCalledWith({
        success: true,
        data: {
          userId: 1,
          firstName: 'John',
          isActive: true,
          nestedObject: {
            streetName: 'Main St'
          }
        }
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle errors in response transformation', () => {
      const req = mockRequest();
      const res = mockResponse();
      const originalJson = res.json;

      const middleware = transformResponseToCamel({ logTransformations: false });
      middleware(req, res, mockNext);

      // Create a problematic object that will cause an error
      const problematicData = Object.create(null);
      Object.defineProperty(problematicData, 'problematic', {
        get: () => { throw new Error('Test error'); }
      });

      // Simulate a response with the problematic data
      res.json(problematicData);

      // Should still call the original json with the original data
      expect(originalJson).toHaveBeenCalledWith(problematicData);
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('transformCaseBidirectional', () => {
    it('should return an array of middleware functions', () => {
      const middleware = transformCaseBidirectional();
      expect(Array.isArray(middleware)).toBe(true);
      expect(middleware.length).toBe(2);
    });
  });

  describe('enableDualCaseSupport', () => {
    it('should auto-detect and transform camelCase requests to snake_case', () => {
      const req = mockRequest({
        userId: 1,
        firstName: 'John',
        isActive: true
      });

      const middleware = enableDualCaseSupport({ logTransformations: false });
      middleware(req, mockResponse(), mockNext);

      expect(req.body).toEqual({
        user_id: 1,
        first_name: 'John',
        is_active: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should not transform snake_case requests', () => {
      const req = mockRequest({
        user_id: 1,
        first_name: 'John',
        is_active: true
      });

      const middleware = enableDualCaseSupport({ logTransformations: false });
      middleware(req, mockResponse(), mockNext);

      expect(req.body).toEqual({
        user_id: 1,
        first_name: 'John',
        is_active: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle mixed format requests', () => {
      const req = mockRequest({
        userId: 1,
        first_name: 'John',
        isActive: true
      });

      const middleware = enableDualCaseSupport({ logTransformations: false });
      middleware(req, mockResponse(), mockNext);

      // Should not transform mixed format
      expect(req.body).toEqual({
        userId: 1,
        first_name: 'John',
        isActive: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle errors gracefully', () => {
      const req = mockRequest({ validData: 'test' });

      // Simulăm o eroare prin modificarea CaseConverter.toSnake
      const originalToSnake = require('../../../src/utils/caseConverter').CaseConverter.toSnake;
      require('../../../src/utils/caseConverter').CaseConverter.toSnake = jest.fn().mockImplementation(() => {
        throw new Error('Test error');
      });

      const middleware = enableDualCaseSupport({ logTransformations: false });
      middleware(req, mockResponse(), mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(Error);

      // Restore original method
      require('../../../src/utils/caseConverter').CaseConverter.toSnake = originalToSnake;
    });
  });
});
